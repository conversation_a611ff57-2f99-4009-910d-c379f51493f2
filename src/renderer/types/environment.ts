/**
 * Environment-related type definitions
 */

export interface Environment {
  id: number;
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  isActive: boolean;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface EnvironmentVariable {
  key: string;
  value: string;
  enabled: boolean;
  description?: string;
}

export interface EnvironmentData {
  id?: string | number;
  entityId?: number;
  name: string;
  description?: string;
  variables?: Record<string, string> | EnvironmentVariable[];
  isActive?: boolean;
  workspaceId?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CreateEnvironmentData {
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  workspaceId: number;
}

export interface UpdateEnvironmentData {
  name?: string;
  description?: string;
  variables?: EnvironmentVariable[];
  isActive?: boolean;
}

export interface EnvironmentSettings {
  autoSync?: boolean;
  encryptValues?: boolean;
  shareWithTeam?: boolean;
}

export interface FullEnvironmentData extends EnvironmentData {
  settings?: EnvironmentSettings;
}
