import React from 'react';

interface ConfirmationDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  type?: 'warning' | 'danger' | 'info';
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  type = 'warning'
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'danger': return '⚠️';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '⚠️';
    }
  };

  const getConfirmButtonClass = () => {
    switch (type) {
      case 'danger': return 'btn btn-danger';
      case 'warning': return 'btn btn-warning';
      case 'info': return 'btn btn-primary';
      default: return 'btn btn-warning';
    }
  };

  return (
    <>
      <div className="confirmation-dialog-overlay" onClick={onCancel} />
      <div className="confirmation-dialog">
        <div className="confirmation-dialog-header">
          <span className="confirmation-dialog-icon">{getIcon()}</span>
          <h3 className="confirmation-dialog-title">{title}</h3>
        </div>
        
        <div className="confirmation-dialog-content">
          <p className="confirmation-dialog-message">{message}</p>
        </div>
        
        <div className="confirmation-dialog-actions">
          <button
            className="btn btn-secondary"
            onClick={onCancel}
          >
            {cancelText}
          </button>
          <button
            className={getConfirmButtonClass()}
            onClick={onConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </>
  );
};

export default ConfirmationDialog;
