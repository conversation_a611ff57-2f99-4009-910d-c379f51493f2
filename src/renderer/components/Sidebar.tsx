import React, { useState, useEffect } from 'react';
import TreeView from './TreeView';
import { WorkspaceItem, WorkspaceData } from '../types/workspace';
import { generateSeedData } from '../data/seedData';

interface SidebarProps {
  onOpenRequest: (request: any) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onOpenRequest }) => {
  const [workspace, setWorkspace] = useState<WorkspaceData | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | undefined>();

  useEffect(() => {
    // Load workspace from localStorage or initialize with seed data
    const savedWorkspace = localStorage.getItem('apicool-workspace');
    if (savedWorkspace) {
      try {
        const parsed = JSON.parse(savedWorkspace);
        // Convert date strings back to Date objects
        parsed.items = parsed.items.map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        }));
        setWorkspace(parsed);
      } catch (error) {
        console.error('Failed to load workspace from localStorage:', error);
        const seedData = generateSeedData();
        setWorkspace(seedData);
      }
    } else {
      const seedData = generateSeedData();
      setWorkspace(seedData);
    }
  }, []);

  // Save workspace to localStorage whenever it changes
  useEffect(() => {
    if (workspace) {
      localStorage.setItem('apicool-workspace', JSON.stringify(workspace));
    }
  }, [workspace]);

  // Listen for workspace updates from other components
  useEffect(() => {
    const handleWorkspaceUpdate = (event: CustomEvent) => {
      const updatedWorkspace = event.detail;
      // Convert date strings back to Date objects
      updatedWorkspace.items = updatedWorkspace.items.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      }));
      setWorkspace(updatedWorkspace);
    };

    window.addEventListener('workspace-updated', handleWorkspaceUpdate as EventListener);

    return () => {
      window.removeEventListener('workspace-updated', handleWorkspaceUpdate as EventListener);
    };
  }, []);

  const handleItemClick = (item: WorkspaceItem) => {
    setSelectedItemId(item.id);
    if (item.type === 'request') {
      onOpenRequest({
        id: item.id,
        name: item.name,
        method: item.method,
        url: item.url,
        headers: item.headers,
        params: item.params,
        body: item.body
      });
    }
  };

  const handleItemToggle = (itemId: string) => {
    if (!workspace) return;

    setWorkspace(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId
            ? { ...item, expanded: !item.expanded }
            : item
        )
      };
    });
  };

  const handleItemRename = (itemId: string, newName: string) => {
    if (!workspace) return;

    setWorkspace(prev => {
      if (!prev) return prev;

      const updatedWorkspace = {
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId
            ? { ...item, name: newName, updatedAt: new Date() }
            : item
        )
      };

      // Also update any open tabs with the same ID
      const openTabs = JSON.parse(localStorage.getItem('apicool-open-tabs') || '[]');
      const updatedTabs = openTabs.map((tab: any) =>
        tab.id === itemId ? { ...tab, name: newName } : tab
      );
      localStorage.setItem('apicool-open-tabs', JSON.stringify(updatedTabs));

      // Trigger tab update event
      window.dispatchEvent(new CustomEvent('tabs-updated', { detail: updatedTabs }));

      return updatedWorkspace;
    });
  };

  const handleItemDelete = (itemId: string) => {
    if (!workspace) return;

    // Find all descendant items to delete
    const getDescendants = (parentId: string): string[] => {
      const children = workspace.items.filter(item => item.parentId === parentId);
      const descendants = children.map(child => child.id);
      children.forEach(child => {
        descendants.push(...getDescendants(child.id));
      });
      return descendants;
    };

    const toDelete = [itemId, ...getDescendants(itemId)];

    setWorkspace(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        items: prev.items.filter(item => !toDelete.includes(item.id))
      };
    });

    // Clear selection if deleted item was selected
    if (toDelete.includes(selectedItemId || '')) {
      setSelectedItemId(undefined);
    }
  };

  const handleItemMove = (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => {
    if (!workspace) return;

    const dragItem = workspace.items.find(item => item.id === dragId);
    const hoverItem = workspace.items.find(item => item.id === hoverId);

    if (!dragItem || !hoverItem) return;

    // Prevent dropping item into itself or its descendants
    const isDescendant = (parentId: string, childId: string): boolean => {
      const children = workspace.items.filter(item => item.parentId === parentId);
      return children.some(child =>
        child.id === childId || isDescendant(child.id, childId)
      );
    };

    if (dragId === hoverId || isDescendant(dragId, hoverId)) return;

    setWorkspace(prev => {
      if (!prev) return prev;

      let newParentId: string | undefined;
      let newOrder: number;

      if (position === 'inside') {
        newParentId = hoverId;
        const siblings = prev.items.filter(item => item.parentId === hoverId);
        newOrder = siblings.length > 0 ? Math.max(...siblings.map(s => s.order)) + 1 : 0;
      } else {
        newParentId = hoverItem.parentId;
        const siblings = prev.items.filter(item => item.parentId === newParentId);
        const hoverIndex = siblings.findIndex(s => s.id === hoverId);
        newOrder = position === 'before' ? hoverItem.order : hoverItem.order + 1;

        // Adjust orders of other items
        siblings.forEach(sibling => {
          if (sibling.id !== dragId) {
            if (position === 'before' && sibling.order >= hoverItem.order) {
              sibling.order += 1;
            } else if (position === 'after' && sibling.order > hoverItem.order) {
              sibling.order += 1;
            }
          }
        });
      }

      return {
        ...prev,
        items: prev.items.map(item =>
          item.id === dragId
            ? { ...item, parentId: newParentId, order: newOrder, updatedAt: new Date() }
            : item
        )
      };
    });
  };

  const handleAddNew = (parentId?: string, type: 'folder' | 'request' = 'request') => {
    if (!workspace) return;

    const siblings = workspace.items.filter(item => item.parentId === parentId);
    const maxOrder = siblings.length > 0 ? Math.max(...siblings.map(s => s.order)) : -1;

    const newItem: WorkspaceItem = {
      id: `${type}-${Date.now()}`,
      name: type === 'folder' ? 'New Folder' : 'New Request',
      type,
      parentId,
      order: maxOrder + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...(type === 'folder' ? { expanded: true } : {
        method: 'GET' as const,
        url: '',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        params: {},
        body: ''
      })
    };

    setWorkspace(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        items: [...prev.items, newItem]
      };
    });

    setSelectedItemId(newItem.id);

    // If it's a request, automatically open it in a new tab
    if (type === 'request') {
      onOpenRequest({
        id: newItem.id,
        name: newItem.name,
        method: newItem.method,
        url: newItem.url,
        headers: newItem.headers,
        params: newItem.params,
        body: newItem.body
      });
    }
  };

  const handleNewCollection = () => {
    handleAddNew(undefined, 'folder');
  };

  if (!workspace) {
    return (
      <div className="sidebar">
        <div className="sidebar-header">
          <h3>Collections</h3>
          <button className="btn btn-sm btn-primary">+ New</button>
        </div>
        <div style={{ padding: '16px', textAlign: 'center', color: 'var(--color-text-secondary)' }}>
          Loading workspace...
        </div>
      </div>
    );
  }

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Collections</h3>
        <div className="sidebar-actions">
          <button
            className="sidebar-action-btn"
            onClick={handleNewCollection}
            title="New Collection"
          >
            📁+
          </button>
          <button
            className="sidebar-action-btn"
            onClick={() => handleAddNew(undefined, 'request')}
            title="New Request"
          >
            🔗+
          </button>
        </div>
      </div>

      <TreeView
        items={workspace.items}
        allItems={workspace.items}
        onItemClick={handleItemClick}
        onItemToggle={handleItemToggle}
        onItemRename={handleItemRename}
        onItemDelete={handleItemDelete}
        onItemMove={handleItemMove}
        onAddNew={handleAddNew}
        selectedItemId={selectedItemId}
      />
    </div>
  );
};

export default Sidebar;
