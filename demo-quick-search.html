<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Search & Command Box Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #212529;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            padding: 20px;
            background: #007acc;
            color: white;
        }
        
        .demo-content {
            padding: 40px;
            text-align: center;
        }
        
        .shortcut-demo {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            margin: 20px 0;
        }
        
        .shortcut-key {
            display: inline-block;
            background: #343a40;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
            margin: 0 4px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: left;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #007acc;
        }
        
        .feature-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .command-examples {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .command-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .command-item:last-child {
            border-bottom: none;
        }
        
        .command-prefix {
            background: #007acc;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .command-name {
            font-weight: 500;
            margin-right: 10px;
        }
        
        .command-desc {
            color: #6c757d;
            font-size: 13px;
        }
        
        .search-examples {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .search-example {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .search-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .search-query {
            background: white;
            border: 1px solid #007acc;
            border-radius: 4px;
            padding: 4px 8px;
            font-family: monospace;
            margin-right: 10px;
            min-width: 120px;
        }
        
        .search-result {
            color: #6c757d;
            font-size: 13px;
        }
        
        .btn-demo {
            background: #007acc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-demo:hover {
            background: #0056b3;
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔍 Quick Search & Command Box</h1>
            <p>Global search and command palette for efficient navigation and actions</p>
        </div>
        
        <div class="demo-content">
            <div class="shortcut-demo">
                <h2>Try it now!</h2>
                <p>Press <span class="shortcut-key">Ctrl</span> + <span class="shortcut-key">O</span> (or <span class="shortcut-key">Cmd</span> + <span class="shortcut-key">O</span> on Mac)</p>
                <p>to open the Quick Search & Command Box</p>
                <button class="btn-demo" onclick="showDemo()">Click here to simulate</button>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">Universal Search</div>
                    <div class="feature-description">
                        Search across all requests, folders, collections, and examples in your workspace. 
                        Find what you need instantly with fuzzy matching.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">Command Palette</div>
                    <div class="feature-description">
                        Type "/" to access commands like creating requests, launching AI chat, 
                        running collections, and more.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⌨️</div>
                    <div class="feature-title">Keyboard Navigation</div>
                    <div class="feature-description">
                        Navigate with arrow keys, select with Enter, and close with Escape. 
                        Fully keyboard accessible for power users.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">Smart Actions</div>
                    <div class="feature-description">
                        Selecting a request opens it in a new tab. Folders and collections 
                        open their respective editors. Commands execute immediately.
                    </div>
                </div>
            </div>

            <h3>🔍 Search Examples</h3>
            <div class="search-examples">
                <div class="search-example">
                    <span class="search-icon">🔗</span>
                    <span class="search-query">login</span>
                    <span class="search-result">→ Finds "Login" request in Authentication folder</span>
                </div>
                <div class="search-example">
                    <span class="search-icon">📁</span>
                    <span class="search-query">user</span>
                    <span class="search-result">→ Finds "Users" folder and "User Management API" collection</span>
                </div>
                <div class="search-example">
                    <span class="search-icon">📚</span>
                    <span class="search-query">ecommerce</span>
                    <span class="search-result">→ Finds "E-commerce API" collection</span>
                </div>
            </div>

            <h3>⚡ Command Examples</h3>
            <div class="command-examples">
                <div class="command-item">
                    <span class="command-prefix">/</span>
                    <span class="command-name">create request</span>
                    <span class="command-desc">Create a new HTTP request</span>
                </div>
                <div class="command-item">
                    <span class="command-prefix">/</span>
                    <span class="command-name">launch ai</span>
                    <span class="command-desc">Open AI assistant for help</span>
                </div>
                <div class="command-item">
                    <span class="command-prefix">/</span>
                    <span class="command-name">run collection</span>
                    <span class="command-desc">Execute all requests in a collection</span>
                </div>
                <div class="command-item">
                    <span class="command-prefix">/</span>
                    <span class="command-name">import postman</span>
                    <span class="command-desc">Import from Postman format</span>
                </div>
                <div class="command-item">
                    <span class="command-prefix">/</span>
                    <span class="command-name">settings</span>
                    <span class="command-desc">Configure application settings</span>
                </div>
            </div>

            <div class="note">
                <strong>💡 Pro Tip:</strong> The Quick Search Box remembers your recent selections and 
                provides intelligent suggestions based on your usage patterns.
            </div>
        </div>
    </div>

    <script>
        function showDemo() {
            alert('In the actual application, this would open the Quick Search & Command Box!\n\nTry pressing Ctrl+O (or Cmd+O) in the main application.');
        }

        // Listen for the actual keyboard shortcut
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                e.preventDefault();
                showDemo();
            }
        });
    </script>
</body>
</html>
