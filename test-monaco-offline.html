<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monaco Editor Offline Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2d2d2d;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #28a745;
            color: white;
        }
        .status.error {
            background-color: #dc3545;
            color: white;
        }
        .status.loading {
            background-color: #ffc107;
            color: black;
        }
        #editor {
            height: 400px;
            border: 1px solid #333;
            margin: 10px 0;
        }
        .worker-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .worker-item {
            padding: 10px;
            border: 1px solid #333;
            border-radius: 4px;
            background-color: #1e1e1e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Monaco Editor Offline Mode Test</h1>
        
        <div class="test-section">
            <h2>1. Worker Files Check</h2>
            <div id="worker-status" class="status loading">Checking worker files...</div>
            <div id="worker-details" class="worker-status"></div>
        </div>

        <div class="test-section">
            <h2>2. Monaco Editor Loading</h2>
            <div id="monaco-status" class="status loading">Loading Monaco Editor...</div>
            <div id="editor"></div>
        </div>

        <div class="test-section">
            <h2>3. Language Features Test</h2>
            <div id="features-status" class="status loading">Testing language features...</div>
            <div id="features-details"></div>
        </div>
    </div>

    <!-- Monaco Editor CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/editor/editor.main.css">
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/loader.js"></script>
    <script>
        // Configure Monaco Editor loader for CDN
        require.config({
            paths: {
                'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'
            }
        });

        // Set up Monaco Environment for web workers
        window.MonacoEnvironment = {
            getWorkerUrl: function (moduleId, label) {
                if (label === 'json') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/json/json.worker.js';
                }
                if (label === 'css' || label === 'scss' || label === 'less') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/css/css.worker.js';
                }
                if (label === 'html' || label === 'handlebars' || label === 'razor') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/html/html.worker.js';
                }
                if (label === 'typescript' || label === 'javascript') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/typescript/ts.worker.js';
                }
                return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js';
            }
        };

        // Initialize Monaco Editor
        require(['vs/editor/editor.main'], function() {
            console.log('Monaco Editor loaded successfully from CDN');
            window.monacoLoaded = true;
            // Dispatch custom event to notify components
            window.dispatchEvent(new CustomEvent('monacoLoaded'));
        });
    </script>

    <script>
        // Test CDN worker files availability
        async function testWorkerFiles() {
            const workers = [
                'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js',
                'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/json/json.worker.js',
                'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/html/html.worker.js',
                'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/css/css.worker.js',
                'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/typescript/ts.worker.js'
            ];

            const workerStatus = document.getElementById('worker-status');
            const workerDetails = document.getElementById('worker-details');

            let allWorkersPassed = true;

            for (const workerUrl of workers) {
                const workerName = workerUrl.split('/').pop();
                try {
                    const response = await fetch(workerUrl, { method: 'HEAD' });
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? 'Available' : `Error: ${response.status}`;

                    if (!response.ok) allWorkersPassed = false;

                    const workerItem = document.createElement('div');
                    workerItem.className = 'worker-item';
                    workerItem.innerHTML = `
                        <strong>${workerName}</strong><br>
                        <span class="status ${status}" style="display: inline-block; padding: 2px 8px; margin-top: 5px;">
                            ${statusText}
                        </span>
                    `;
                    workerDetails.appendChild(workerItem);
                } catch (error) {
                    allWorkersPassed = false;
                    const workerItem = document.createElement('div');
                    workerItem.className = 'worker-item';
                    workerItem.innerHTML = `
                        <strong>${workerName}</strong><br>
                        <span class="status error" style="display: inline-block; padding: 2px 8px; margin-top: 5px;">
                            Error: ${error.message}
                        </span>
                    `;
                    workerDetails.appendChild(workerItem);
                }
            }

            workerStatus.className = `status ${allWorkersPassed ? 'success' : 'error'}`;
            workerStatus.textContent = allWorkersPassed ?
                'All CDN worker files are available!' :
                'Some CDN worker files are missing or inaccessible!';
        }

        // Test Monaco Editor loading
        async function testMonacoEditor() {
            const monacoStatus = document.getElementById('monaco-status');

            try {
                // Wait for Monaco to be loaded from CDN
                if (window.monacoLoaded && typeof monaco !== 'undefined') {
                    monacoStatus.className = 'status success';
                    monacoStatus.textContent = 'Monaco Editor loaded successfully from CDN!';

                    // Create editor instance
                    const editor = monaco.editor.create(document.getElementById('editor'), {
                        value: '{\n  "name": "Monaco Editor CDN Test",\n  "version": "1.0.0",\n  "status": "working",\n  "source": "CDN"\n}',
                        language: 'json',
                        theme: 'vs-dark',
                        automaticLayout: true,
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        fontSize: 13,
                        lineNumbers: 'on',
                        wordWrap: 'on'
                    });

                    testLanguageFeatures();
                } else {
                    throw new Error('Monaco global object not found or not loaded from CDN');
                }
            } catch (error) {
                monacoStatus.className = 'status error';
                monacoStatus.textContent = `Monaco Editor failed to load: ${error.message}`;
            }
        }

        // Test language features
        function testLanguageFeatures() {
            const featuresStatus = document.getElementById('features-status');
            const featuresDetails = document.getElementById('features-details');
            
            try {
                const features = [
                    { name: 'JSON Language', test: () => monaco.languages.getLanguages().some(l => l.id === 'json') },
                    { name: 'TypeScript Language', test: () => monaco.languages.getLanguages().some(l => l.id === 'typescript') },
                    { name: 'JavaScript Language', test: () => monaco.languages.getLanguages().some(l => l.id === 'javascript') },
                    { name: 'HTML Language', test: () => monaco.languages.getLanguages().some(l => l.id === 'html') },
                    { name: 'CSS Language', test: () => monaco.languages.getLanguages().some(l => l.id === 'css') }
                ];
                
                let allFeaturesPassed = true;
                
                features.forEach(feature => {
                    try {
                        const passed = feature.test();
                        if (!passed) allFeaturesPassed = false;
                        
                        const featureItem = document.createElement('div');
                        featureItem.className = 'worker-item';
                        featureItem.innerHTML = `
                            <strong>${feature.name}</strong><br>
                            <span class="status ${passed ? 'success' : 'error'}" style="display: inline-block; padding: 2px 8px; margin-top: 5px;">
                                ${passed ? 'Available' : 'Not Available'}
                            </span>
                        `;
                        featuresDetails.appendChild(featureItem);
                    } catch (error) {
                        allFeaturesPassed = false;
                        const featureItem = document.createElement('div');
                        featureItem.className = 'worker-item';
                        featureItem.innerHTML = `
                            <strong>${feature.name}</strong><br>
                            <span class="status error" style="display: inline-block; padding: 2px 8px; margin-top: 5px;">
                                Error: ${error.message}
                            </span>
                        `;
                        featuresDetails.appendChild(featureItem);
                    }
                });
                
                featuresStatus.className = `status ${allFeaturesPassed ? 'success' : 'error'}`;
                featuresStatus.textContent = allFeaturesPassed ? 
                    'All language features are working!' : 
                    'Some language features are not working!';
                    
            } catch (error) {
                featuresStatus.className = 'status error';
                featuresStatus.textContent = `Language features test failed: ${error.message}`;
            }
        }

        // Run tests
        document.addEventListener('DOMContentLoaded', () => {
            testWorkerFiles();

            // Listen for Monaco loaded event
            window.addEventListener('monacoLoaded', () => {
                testMonacoEditor();
            });

            // Fallback timeout in case Monaco doesn't load
            setTimeout(() => {
                if (!window.monacoLoaded) {
                    testMonacoEditor();
                }
            }, 10000);
        });
    </script>
</body>
</html>
