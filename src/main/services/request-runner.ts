import axios, { AxiosResponse, AxiosRequestConfig, AxiosError } from 'axios';
import { VariableResolutionService, Variable } from './variable-resolution';
import { HistoryService } from './history';
import * as zlib from 'zlib';
import { promisify } from 'util';

interface RequestRunnerData {
  id: string;
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
  preScript?: string;
  testScript?: string;
}

interface RequestResult {
  id: string;
  name: string;
  status: 'success' | 'error' | 'timeout';
  statusCode?: number;
  statusText?: string;
  responseTime: number;
  responseSize: number;
  responseHeaders: Record<string, string>;
  responseBody: any;
  error?: string;
  testResults?: TestResult[];
  cookies?: Cookie[];
}

interface TestResult {
  name: string;
  status: 'pass' | 'fail';
  message?: string;
}

interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: string;
  httpOnly?: boolean;
  secure?: boolean;
}

interface CollectionRunOptions {
  environmentId?: string;
  iterationCount?: number;
  delay?: number;
  stopOnError?: boolean;
  timeout?: number;
}

export class RequestRunnerService {
  private variableResolver: VariableResolutionService;
  private historyService: HistoryService | null = null;
  private timeout: number = 30000; // 30 seconds default

  constructor(historyService?: HistoryService) {
    this.variableResolver = new VariableResolutionService();
    this.historyService = historyService || null;
  }

  /**
   * Execute a single request
   */
  public async executeRequest(
    requestData: RequestRunnerData,
    environmentVariables: Record<string, Variable> = {},
    collectionVariables: Record<string, Variable> = {},
    workspaceId?: number,
    environmentName?: string
  ): Promise<RequestResult> {
    const startTime = Date.now();

    try {
      // Initialize variable scope
      this.variableResolver.initializeScope(environmentVariables, collectionVariables);

      // Execute pre-request script
      if (requestData.preScript) {
        await this.executeScript(requestData.preScript, 'pre-request');
      }

      // Resolve variables in request
      const resolvedRequest = this.resolveRequestVariables(requestData);

      // Validate resolved request
      this.validateRequest(resolvedRequest);

      // Execute HTTP request
      const response = await this.makeHttpRequest(resolvedRequest);
      const endTime = Date.now();

      // Parse response
      const result: RequestResult = {
        id: requestData.id,
        name: requestData.name,
        status: 'success',
        statusCode: response.status,
        statusText: response.statusText,
        responseTime: endTime - startTime,
        responseSize: this.calculateResponseSize(response),
        responseHeaders: this.normalizeHeaders(response.headers || {}),
        responseBody: response.data,
        cookies: this.extractCookies(response)
      };

      // Execute test script
      if (requestData.testScript) {
        result.testResults = await this.executeTestScript(requestData.testScript, result);
      }

      // Create history entry for successful request
      if (this.historyService && workspaceId) {
        try {
          await this.historyService.createHistoryEntry({
            requestId: parseInt(requestData.id, 10) || undefined,
            name: requestData.name,
            method: requestData.method,
            url: resolvedRequest.url,
            headers: resolvedRequest.headers || {},
            body: resolvedRequest.body,
            response: {
              status: result.statusCode,
              headers: result.responseHeaders,
              data: result.responseBody
            },
            statusCode: result.statusCode,
            responseTime: result.responseTime,
            environment: environmentName,
            workspaceId
          });
        } catch (historyError) {
          console.error('Failed to create history entry:', historyError);
          // Don't fail the request if history creation fails
        }
      }

      return result;

    } catch (error) {
      const endTime = Date.now();
      const errorResult = this.createErrorResult(requestData, error as Error, endTime - startTime);

      // Create history entry for failed request
      if (this.historyService && workspaceId) {
        try {
          const resolvedRequest = this.resolveRequestVariables(requestData);
          await this.historyService.createHistoryEntry({
            requestId: parseInt(requestData.id, 10) || undefined,
            name: requestData.name,
            method: requestData.method,
            url: resolvedRequest.url,
            headers: resolvedRequest.headers || {},
            body: resolvedRequest.body,
            response: null,
            statusCode: errorResult.statusCode,
            responseTime: errorResult.responseTime,
            error: errorResult.error,
            environment: environmentName,
            workspaceId
          });
        } catch (historyError) {
          console.error('Failed to create history entry for error:', historyError);
          // Don't fail the request if history creation fails
        }
      }

      return errorResult;
    }
  }

  /**
   * Execute a collection of requests
   */
  public async executeCollection(
    requests: RequestRunnerData[],
    environmentVariables: Record<string, Variable> = {},
    collectionVariables: Record<string, Variable> = {},
    options: CollectionRunOptions = {}
  ): Promise<RequestResult[]> {
    const results: RequestResult[] = [];
    
    // Initialize variable scope for the entire collection run
    this.variableResolver.initializeScope(environmentVariables, collectionVariables);

    for (let i = 0; i < requests.length; i++) {
      const request = requests[i];

      try {
        // Execute request with shared variable state
        const result = await this.executeRequestInCollection(request);
        results.push(result);

        // Stop on error if configured
        if (options.stopOnError && result.status === 'error') {
          break;
        }

        // Add delay between requests if configured
        if (options.delay && i < requests.length - 1) {
          await this.delay(options.delay);
        }

      } catch (error) {
        const errorResult = this.createErrorResult(request, error as Error, 0);
        results.push(errorResult);

        if (options.stopOnError) {
          break;
        }
      }
    }

    return results;
  }

  /**
   * Execute request within a collection context (shared variables)
   */
  private async executeRequestInCollection(requestData: RequestRunnerData): Promise<RequestResult> {
    const startTime = Date.now();

    try {
      // Execute pre-request script
      if (requestData.preScript) {
        await this.executeScript(requestData.preScript, 'pre-request');
      }

      // Resolve variables in request
      const resolvedRequest = this.resolveRequestVariables(requestData);

      // Validate resolved request
      this.validateRequest(resolvedRequest);

      // Execute HTTP request
      const response = await this.makeHttpRequest(resolvedRequest);
      const endTime = Date.now();

      // Parse response
      const result: RequestResult = {
        id: requestData.id,
        name: requestData.name,
        status: 'success',
        statusCode: response.status,
        statusText: response.statusText,
        responseTime: endTime - startTime,
        responseSize: this.calculateResponseSize(response),
        responseHeaders: this.normalizeHeaders(response.headers || {}),
        responseBody: response.data,
        cookies: this.extractCookies(response)
      };

      // Execute test script
      if (requestData.testScript) {
        result.testResults = await this.executeTestScript(requestData.testScript, result);
      }

      return result;

    } catch (error) {
      const endTime = Date.now();
      return this.createErrorResult(requestData, error as Error, endTime - startTime);
    }
  }

  /**
   * Resolve variables in request data
   */
  private resolveRequestVariables(requestData: RequestRunnerData): RequestRunnerData {
    return {
      ...requestData,
      url: this.variableResolver.resolveTemplate(requestData.url),
      headers: this.variableResolver.resolveObject(requestData.headers || {}),
      body: this.variableResolver.resolveObject(requestData.body)
    };
  }

  /**
   * Validate resolved request
   */
  private validateRequest(request: RequestRunnerData): void {
    if (!request.url) {
      throw new Error('Request URL is required');
    }

    if (!request.method) {
      throw new Error('Request method is required');
    }

    // Validate URL format
    try {
      new URL(request.url);
    } catch {
      throw new Error(`Invalid URL format: ${request.url}`);
    }
  }

  /**
   * Make HTTP request using axios
   */
  private async makeHttpRequest(request: RequestRunnerData): Promise<AxiosResponse> {
    const config: AxiosRequestConfig = {
      method: request.method.toLowerCase() as any,
      url: request.url,
      headers: request.headers || {},
      timeout: this.timeout,
      validateStatus: () => true, // Don't throw on HTTP error status codes
      withCredentials: true, // Include cookies
      responseType: 'arraybuffer', // Get raw binary data
      decompress: false, // Handle decompression manually
      maxRedirects: 5, // Handle redirects
      maxContentLength: 50 * 1024 * 1024, // 50MB max response size
      maxBodyLength: 50 * 1024 * 1024 // 50MB max request body size
    };

    // Add body for methods that support it
    if (['post', 'put', 'patch'].includes(request.method.toLowerCase()) && request.body) {
      config.data = request.body;
    }

    // Add Accept-Encoding header to request compression
    if (!config.headers['Accept-Encoding'] && !config.headers['accept-encoding']) {
      config.headers['Accept-Encoding'] = 'gzip, deflate, br';
    }

    const response = await axios(config);

    // Process the response to handle encoding and compression
    const processedResponse = await this.processResponse(response);

    return processedResponse;
  }

  /**
   * Process response to handle compression and encoding
   */
  private async processResponse(response: AxiosResponse): Promise<AxiosResponse> {
    const gunzip = promisify(zlib.gunzip);
    const inflate = promisify(zlib.inflate);
    const brotliDecompress = promisify(zlib.brotliDecompress);

    let data = response.data;
    const contentEncoding = response.headers['content-encoding'] || response.headers['Content-Encoding'];
    const contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';

    try {
      // Handle compression
      if (contentEncoding) {
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

        switch (contentEncoding.toLowerCase().trim()) {
          case 'gzip':
          case 'x-gzip':
            try {
              data = await gunzip(buffer);
            } catch (gzipError) {
              console.warn('Failed to decompress gzip data:', gzipError);
              // Keep original data if decompression fails
            }
            break;
          case 'deflate':
            try {
              data = await inflate(buffer);
            } catch (deflateError) {
              console.warn('Failed to decompress deflate data:', deflateError);
              // Keep original data if decompression fails
            }
            break;
          case 'br':
          case 'brotli':
            try {
              data = await brotliDecompress(buffer);
            } catch (brotliError) {
              console.warn('Failed to decompress brotli data:', brotliError);
              // Keep original data if decompression fails
            }
            break;
          case 'identity':
            // No compression
            break;
          default:
            console.warn(`Unknown content encoding: ${contentEncoding}`);
            break;
        }
      }

      // Convert to appropriate format based on content type
      data = this.convertResponseData(data, contentType);

    } catch (error) {
      console.warn('Failed to process response:', error);
      // If processing fails, try to convert the original data
      data = this.convertResponseData(response.data, contentType);
    }

    // Return modified response
    return {
      ...response,
      data
    };
  }

  /**
   * Convert response data to appropriate format based on content type
   */
  private convertResponseData(data: any, contentType: string): any {
    // If data is already a string, return as is
    if (typeof data === 'string') {
      return data;
    }

    // Convert Buffer/ArrayBuffer to string for text content
    if (Buffer.isBuffer(data) || data instanceof ArrayBuffer) {
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

      // Check if content is likely binary
      if (this.isBinaryContent(contentType)) {
        // For binary content, return base64 encoded string
        return {
          type: 'binary',
          data: buffer.toString('base64'),
          size: buffer.length,
          contentType
        };
      } else {
        // For text content, detect encoding and decode appropriately
        try {
          // Try to detect encoding from content-type
          const charset = this.extractCharsetFromContentType(contentType);

          if (charset) {
            // Use specified charset
            return buffer.toString(charset as BufferEncoding);
          } else {
            // Default to UTF-8
            return buffer.toString('utf8');
          }
        } catch (error) {
          // If specified encoding fails, try common encodings
          const encodings: BufferEncoding[] = ['utf8', 'latin1', 'ascii'];

          for (const encoding of encodings) {
            try {
              const decoded = buffer.toString(encoding);
              // Basic check for valid text (no null bytes in first 100 chars)
              if (!decoded.substring(0, 100).includes('\0')) {
                return decoded;
              }
            } catch {
              continue;
            }
          }

          // If all text encodings fail, treat as binary
          return {
            type: 'binary',
            data: buffer.toString('base64'),
            size: buffer.length,
            contentType
          };
        }
      }
    }

    // For other data types, stringify
    return typeof data === 'object' ? JSON.stringify(data) : String(data);
  }

  /**
   * Extract charset from content-type header
   */
  private extractCharsetFromContentType(contentType: string): string | null {
    const charsetMatch = contentType.match(/charset=([^;,\s]+)/i);
    if (charsetMatch) {
      const charset = charsetMatch[1].toLowerCase().replace(/['"]/g, '');
      // Map common charset names to Node.js encoding names
      switch (charset) {
        case 'utf-8':
        case 'utf8':
          return 'utf8';
        case 'iso-8859-1':
        case 'latin1':
          return 'latin1';
        case 'ascii':
          return 'ascii';
        case 'utf-16':
        case 'utf16':
          return 'utf16le';
        default:
          // Try to use the charset as-is if it's a valid Node.js encoding
          try {
            Buffer.from('test', charset as BufferEncoding);
            return charset;
          } catch {
            return null;
          }
      }
    }
    return null;
  }

  /**
   * Check if content type indicates binary data
   */
  private isBinaryContent(contentType: string): boolean {
    const binaryTypes = [
      'image/',
      'video/',
      'audio/',
      'application/pdf',
      'application/zip',
      'application/x-zip-compressed',
      'application/octet-stream',
      'application/x-binary',
      'application/x-msdownload',
      'application/x-executable',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument',
      'application/vnd.ms-powerpoint',
      'application/msword',
      'font/',
      'application/font-woff',
      'application/x-font-ttf'
    ];

    return binaryTypes.some(type => contentType.toLowerCase().includes(type.toLowerCase()));
  }

  /**
   * Execute pre-request or test script
   */
  private async executeScript(script: string, type: 'pre-request' | 'test'): Promise<void> {
    // Create a sandboxed environment for script execution
    const scriptContext = this.createScriptContext();

    try {
      // Use Function constructor for safer script execution
      const scriptFunction = new Function('pm', 'console', script);
      scriptFunction(scriptContext.pm, scriptContext.console);
    } catch (error) {
      console.error(`Error executing ${type} script:`, error);
      throw new Error(`${type} script execution failed: ${(error as Error).message}`);
    }
  }

  /**
   * Execute test script and return results
   */
  private async executeTestScript(script: string, response: RequestResult): Promise<TestResult[]> {
    const testResults: TestResult[] = [];
    const scriptContext = this.createScriptContext(response, testResults);

    try {
      const scriptFunction = new Function('pm', 'console', script);
      scriptFunction(scriptContext.pm, scriptContext.console);
    } catch (error) {
      testResults.push({
        name: 'Script Execution',
        status: 'fail',
        message: `Test script execution failed: ${(error as Error).message}`
      });
    }

    return testResults;
  }

  /**
   * Create script execution context (Postman-like API)
   */
  private createScriptContext(response?: RequestResult, testResults?: TestResult[]) {
    const self = this;
    
    return {
      pm: {
        // Variable management
        variables: {
          get: (name: string) => self.variableResolver.getVariable(name),
          set: (name: string, value: string) => self.variableResolver.setVariable(name, value),
          unset: (name: string) => self.variableResolver.updateVariable(name, ''),
          has: (name: string) => self.variableResolver.getVariable(name) !== undefined
        },
        
        // Environment variables
        environment: {
          get: (name: string) => self.variableResolver.getVariablesByScope('environment')[name]?.currentValue,
          set: (name: string, value: string) => {
            // Note: This would need to be implemented to update environment
            console.warn('Environment.set not implemented in script context');
          }
        },
        
        // Collection variables
        collectionVariables: {
          get: (name: string) => self.variableResolver.getVariablesByScope('collection')[name]?.currentValue,
          set: (name: string, value: string) => {
            // Note: This would need to be implemented to update collection
            console.warn('CollectionVariables.set not implemented in script context');
          }
        },
        
        // Response object (for test scripts)
        response: response ? {
          code: response.statusCode,
          status: response.statusText,
          headers: response.responseHeaders,
          responseTime: response.responseTime,
          responseSize: response.responseSize,
          text: () => typeof response.responseBody === 'string' ? response.responseBody : JSON.stringify(response.responseBody),
          json: () => typeof response.responseBody === 'object' ? response.responseBody : JSON.parse(response.responseBody)
        } : undefined,
        
        // Test functions (for test scripts)
        test: testResults ? (name: string, fn: () => void) => {
          try {
            fn();
            testResults.push({ name, status: 'pass' });
          } catch (error) {
            testResults.push({ 
              name, 
              status: 'fail', 
              message: (error as Error).message 
            });
          }
        } : undefined,
        
        // Expect function (for test scripts)
        expect: (actual: any) => ({
          to: {
            equal: (expected: any) => {
              if (actual !== expected) {
                throw new Error(`Expected ${actual} to equal ${expected}`);
              }
            },
            be: {
              ok: () => {
                if (!actual) {
                  throw new Error(`Expected ${actual} to be truthy`);
                }
              }
            },
            have: {
              status: (expectedStatus: number) => {
                if (response && response.statusCode !== expectedStatus) {
                  throw new Error(`Expected status ${expectedStatus}, got ${response.statusCode}`);
                }
              }
            }
          }
        })
      },
      
      console: {
        log: (...args: any[]) => console.log('[Script]', ...args),
        error: (...args: any[]) => console.error('[Script]', ...args),
        warn: (...args: any[]) => console.warn('[Script]', ...args)
      }
    };
  }

  /**
   * Calculate response size in bytes
   */
  private calculateResponseSize(response: AxiosResponse): number {
    let bodySize = 0;

    if (response.data) {
      if (typeof response.data === 'object' && response.data.type === 'binary') {
        // For binary data, use the actual size
        bodySize = response.data.size || 0;
      } else if (typeof response.data === 'string') {
        // For string data, use byte length
        bodySize = new TextEncoder().encode(response.data).length;
      } else {
        // For other data, stringify and measure
        bodySize = JSON.stringify(response.data).length;
      }
    }

    const headersSize = Object.entries(response.headers || {})
      .reduce((size, [key, value]) => size + key.length + String(value).length, 0);
    return bodySize + headersSize;
  }

  /**
   * Extract cookies from response
   */
  private extractCookies(response: AxiosResponse): Cookie[] {
    const cookies: Cookie[] = [];
    const setCookieHeader = response.headers['set-cookie'];
    
    if (setCookieHeader) {
      setCookieHeader.forEach((cookieString: string) => {
        const cookie = this.parseCookie(cookieString);
        if (cookie) {
          cookies.push(cookie);
        }
      });
    }
    
    return cookies;
  }

  /**
   * Parse cookie string
   */
  private parseCookie(cookieString: string): Cookie | null {
    const parts = cookieString.split(';').map(part => part.trim());
    const [nameValue] = parts;
    const [name, value] = nameValue.split('=');
    
    if (!name || value === undefined) {
      return null;
    }
    
    const cookie: Cookie = { name, value };
    
    // Parse additional cookie attributes
    parts.slice(1).forEach(part => {
      const [key, val] = part.split('=');
      const lowerKey = key.toLowerCase();
      
      switch (lowerKey) {
        case 'domain':
          cookie.domain = val;
          break;
        case 'path':
          cookie.path = val;
          break;
        case 'expires':
          cookie.expires = val;
          break;
        case 'httponly':
          cookie.httpOnly = true;
          break;
        case 'secure':
          cookie.secure = true;
          break;
      }
    });
    
    return cookie;
  }

  /**
   * Normalize axios headers to Record<string, string>
   */
  private normalizeHeaders(headers: any): Record<string, string> {
    const normalized: Record<string, string> = {};

    if (headers && typeof headers === 'object') {
      for (const [key, value] of Object.entries(headers)) {
        if (value !== undefined && value !== null) {
          normalized[key] = String(value);
        }
      }
    }

    return normalized;
  }

  /**
   * Create error result
   */
  private createErrorResult(request: RequestRunnerData, error: Error, responseTime: number): RequestResult {
    let statusCode: number | undefined;
    let statusText: string | undefined;

    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        statusCode = axiosError.response.status;
        statusText = axiosError.response.statusText;
      }
    }
    
    return {
      id: request.id,
      name: request.name,
      status: 'error',
      statusCode,
      statusText,
      responseTime,
      responseSize: 0,
      responseHeaders: {},
      responseBody: null,
      error: error.message,
      cookies: []
    };
  }

  /**
   * Delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current variable state
   */
  public getVariableState() {
    return this.variableResolver.exportVariables();
  }

  /**
   * Set timeout for requests
   */
  public setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
}

export type { RequestRunnerData, RequestResult, TestResult, Cookie, CollectionRunOptions };
