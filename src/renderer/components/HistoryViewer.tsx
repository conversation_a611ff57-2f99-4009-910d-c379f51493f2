import React, { useState, useEffect } from 'react';

interface HistoryEntry {
  id: number;
  requestId?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  response?: any;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  environment?: string;
  workspaceId: number;
  executedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface HistoryStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
}

interface HistoryViewerProps {
  workspaceId: number;
  onOpenRequest?: (historyEntry: HistoryEntry) => void;
}

const HistoryViewer: React.FC<HistoryViewerProps> = ({ workspaceId, onOpenRequest }) => {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [stats, setStats] = useState<HistoryStats | null>(null);
  const [selectedEntry, setSelectedEntry] = useState<HistoryEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([]);

  useEffect(() => {
    loadHistory();
    loadStats();
  }, [workspaceId]);

  useEffect(() => {
    // Filter history based on search query
    if (searchQuery.trim()) {
      const filtered = history.filter(entry =>
        entry.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.method.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredHistory(filtered);
    } else {
      setFilteredHistory(history);
    }
  }, [history, searchQuery]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await window.electronAPI.history.getAll(workspaceId, 100, 0);
      setHistory(historyData);
    } catch (err) {
      setError('Failed to load history');
      console.error('Error loading history:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await window.electronAPI.history.getStats(workspaceId);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading history stats:', err);
    }
  };

  const handleDeleteEntry = async (id: number) => {
    if (!confirm('Are you sure you want to delete this history entry?')) return;

    try {
      await window.electronAPI.history.delete(id);
      setHistory(prev => prev.filter(entry => entry.id !== id));
      if (selectedEntry?.id === id) {
        setSelectedEntry(null);
      }
    } catch (err) {
      setError('Failed to delete history entry');
    }
  };

  const handleClearHistory = async () => {
    if (!confirm('Are you sure you want to clear all history? This action cannot be undone.')) return;

    try {
      await window.electronAPI.history.clear(workspaceId);
      setHistory([]);
      setSelectedEntry(null);
      loadStats();
    } catch (err) {
      setError('Failed to clear history');
    }
  };

  const handleReplayRequest = (entry: HistoryEntry) => {
    if (onOpenRequest) {
      onOpenRequest(entry);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return 'N/A';
    return `${time}ms`;
  };

  const getStatusColor = (statusCode?: number) => {
    if (!statusCode) return 'gray';
    if (statusCode >= 200 && statusCode < 300) return 'green';
    if (statusCode >= 300 && statusCode < 400) return 'orange';
    if (statusCode >= 400) return 'red';
    return 'gray';
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET': return 'blue';
      case 'POST': return 'green';
      case 'PUT': return 'orange';
      case 'DELETE': return 'red';
      case 'PATCH': return 'purple';
      default: return 'gray';
    }
  };

  if (loading) {
    return <div className="history-viewer loading">Loading history...</div>;
  }

  return (
    <div className="history-viewer">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="history-layout">
        {/* History Sidebar */}
        <div className="history-sidebar">
          <div className="history-header">
            <h3>Request History</h3>
            <div className="history-actions">
              <button 
                className="btn-secondary btn-small"
                onClick={loadHistory}
                title="Refresh"
              >
                ↻
              </button>
              <button 
                className="btn-danger btn-small"
                onClick={handleClearHistory}
                title="Clear All"
              >
                Clear
              </button>
            </div>
          </div>

          {/* Stats */}
          {stats && (
            <div className="history-stats">
              <div className="stat-item">
                <span className="stat-label">Total:</span>
                <span className="stat-value">{stats.totalRequests}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Success:</span>
                <span className="stat-value success">{stats.successfulRequests}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Failed:</span>
                <span className="stat-value error">{stats.failedRequests}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Avg Time:</span>
                <span className="stat-value">{stats.averageResponseTime}ms</span>
              </div>
            </div>
          )}

          {/* Search */}
          <div className="history-search">
            <input
              type="text"
              placeholder="Search history..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>

          {/* History List */}
          <div className="history-list">
            {filteredHistory.map(entry => (
              <div
                key={entry.id}
                className={`history-item ${selectedEntry?.id === entry.id ? 'selected' : ''}`}
                onClick={() => setSelectedEntry(entry)}
              >
                <div className="history-item-header">
                  <span className={`method-badge ${getMethodColor(entry.method)}`}>
                    {entry.method}
                  </span>
                  <span className="history-name">{entry.name}</span>
                  {entry.statusCode && (
                    <span className={`status-badge ${getStatusColor(entry.statusCode)}`}>
                      {entry.statusCode}
                    </span>
                  )}
                </div>
                <div className="history-item-url">{entry.url}</div>
                <div className="history-item-meta">
                  <span className="history-time">{formatDate(entry.executedAt)}</span>
                  {entry.responseTime && (
                    <span className="response-time">{formatResponseTime(entry.responseTime)}</span>
                  )}
                  {entry.environment && (
                    <span className="environment-badge">{entry.environment}</span>
                  )}
                </div>
              </div>
            ))}

            {filteredHistory.length === 0 && (
              <div className="history-empty">
                {searchQuery ? 'No matching history entries' : 'No history entries yet'}
              </div>
            )}
          </div>
        </div>

        {/* History Details */}
        <div className="history-content">
          {selectedEntry ? (
            <div className="history-details">
              <div className="details-header">
                <div className="details-title">
                  <span className={`method-badge ${getMethodColor(selectedEntry.method)}`}>
                    {selectedEntry.method}
                  </span>
                  <h3>{selectedEntry.name}</h3>
                  {selectedEntry.statusCode && (
                    <span className={`status-badge ${getStatusColor(selectedEntry.statusCode)}`}>
                      {selectedEntry.statusCode}
                    </span>
                  )}
                </div>
                <div className="details-actions">
                  <button 
                    className="btn-primary"
                    onClick={() => handleReplayRequest(selectedEntry)}
                  >
                    Open as Request
                  </button>
                  <button 
                    className="btn-danger"
                    onClick={() => handleDeleteEntry(selectedEntry.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>

              <div className="details-content">
                <div className="detail-section">
                  <h4>Request Details</h4>
                  <div className="detail-item">
                    <strong>URL:</strong> {selectedEntry.url}
                  </div>
                  <div className="detail-item">
                    <strong>Executed:</strong> {formatDate(selectedEntry.executedAt)}
                  </div>
                  {selectedEntry.environment && (
                    <div className="detail-item">
                      <strong>Environment:</strong> {selectedEntry.environment}
                    </div>
                  )}
                  {selectedEntry.responseTime && (
                    <div className="detail-item">
                      <strong>Response Time:</strong> {formatResponseTime(selectedEntry.responseTime)}
                    </div>
                  )}
                </div>

                {Object.keys(selectedEntry.headers).length > 0 && (
                  <div className="detail-section">
                    <h4>Headers</h4>
                    <div className="headers-list">
                      {Object.entries(selectedEntry.headers).map(([key, value]) => (
                        <div key={key} className="header-item">
                          <strong>{key}:</strong> {value}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedEntry.body && (
                  <div className="detail-section">
                    <h4>Request Body</h4>
                    <pre className="code-block">
                      {typeof selectedEntry.body === 'string' 
                        ? selectedEntry.body 
                        : JSON.stringify(selectedEntry.body, null, 2)
                      }
                    </pre>
                  </div>
                )}

                {selectedEntry.response && (
                  <div className="detail-section">
                    <h4>Response</h4>
                    <pre className="code-block">
                      {typeof selectedEntry.response === 'string' 
                        ? selectedEntry.response 
                        : JSON.stringify(selectedEntry.response, null, 2)
                      }
                    </pre>
                  </div>
                )}

                {selectedEntry.error && (
                  <div className="detail-section">
                    <h4>Error</h4>
                    <div className="error-block">{selectedEntry.error}</div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="history-empty-details">
              <p>Select a history entry to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryViewer;
