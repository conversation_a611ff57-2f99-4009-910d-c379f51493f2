import React, { useState, useEffect, useCallback, useMemo } from 'react';
import ParamsTable from './ParamsTable';
import HeadersTable from './HeadersTable';
import MonacoEditor from './MonacoEditor';
import { FullRequestData, KeyValuePair } from '../types/request';
import { useKeyboardShortcuts, SHORTCUTS } from '../hooks/useKeyboardShortcuts';

interface RequestEditorProps {
  requestId: string;
  requestName: string;
  // onSave: (data: FullRequestData) => void;
  onClose: () => void;
  onSendRequest: () => void;
  onChange?: (data?: FullRequestData) => void;
  readOnly?: boolean;
  initialData?: FullRequestData;
}

// Default request data factory
const createDefaultRequestData = (requestId: string, requestName: string): FullRequestData => ({
  id: parseInt(requestId) || 0,
  name: requestName,
  method: 'GET',
  url: '',
  headers: {},
  params: {},
  body: '',
  description: '',
  auth: { type: 'none' },
  preScript: '',
  testScript: '',
  workspaceId: parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10)
});

// HTTP methods constant
const HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'] as const;

// Utility functions to convert between formats
const convertToKeyValuePairs = (data: Record<string, string> | KeyValuePair[]): KeyValuePair[] => {
  if (Array.isArray(data)) {
    return data;
  }
  return Object.entries(data).map(([key, value]) => ({
    key,
    value,
    description: '',
    enabled: true
  }));
};


// Body language options
const BODY_LANGUAGES = [
  { value: 'plaintext', label: 'Text' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'html', label: 'HTML' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' }
] as const;

const RequestEditor: React.FC<RequestEditorProps> = ({
  requestId,
  requestName,
 
  onSendRequest,
  onChange,
  readOnly = false,
  initialData
}) => {
  const [requestData, setRequestData] = useState<FullRequestData>(
    () => createDefaultRequestData(requestId, requestName)
  );
  const [activeTab, setActiveTab] = useState('params');
  const [bodyType, setBodyType] = useState('none');
  const [bodyLanguage, setBodyLanguage] = useState('json');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Separate state for scripts to prevent reset issues
  const [preScript, setPreScript] = useState('');
  const [testScript, setTestScript] = useState('');

  // Memoized normalized data function
  const normalizeRequestData = useCallback((data: Partial<FullRequestData>): FullRequestData => ({
    ...createDefaultRequestData(requestId, requestName),
    ...data,
    headers: data.headers || {},
    params: data.params || {},
    body: data.body || '',
    description: data.description || '',
    auth: data.auth || { type: 'none' },
    preScript: data.preScript || '',
    testScript: data.testScript || ''
  }), [requestId, requestName]);

  // Optimized data update function
  const updateRequestData = useCallback((updates: Partial<FullRequestData> | ((prev: FullRequestData) => FullRequestData)) => {
    setRequestData(prev => {
      const newData = typeof updates === 'function' ? updates(prev) : { ...prev, ...updates };
      console.log(`[RequestEditor] Updated requestData:`, newData);
      
      // Call onChange with the updated data
      onChange?.(newData);
      return newData;
    });
    setHasUnsavedChanges(true);
  }, [onChange]);

  // Generic input change handler
  const handleInputChange = useCallback((field: keyof FullRequestData, value: any) => {
    console.log(`[RequestEditor] handleInputChange called - field: ${field}, value:`, value);
    updateRequestData({ [field]: value });
  }, [updateRequestData]);

  // Load request data
  useEffect(() => {
    const loadData = async () => {
      try {
        let data: FullRequestData;
        
        if (initialData) {
          console.log('[RequestEditor] Using pre-loaded initial data:', initialData);
          data = normalizeRequestData(initialData);
        } 
        
        setRequestData(data);
        setPreScript(data.preScript || '');
        setTestScript(data.testScript || '');
        setBodyType(data.body ? 'raw' : 'none');
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error('Failed to load request data:', error);
        const defaultData = createDefaultRequestData(requestId, requestName);
        setRequestData(defaultData);
        setPreScript('');
        setTestScript('');
        setHasUnsavedChanges(false);
      }
    };

    loadData();
  }, []);



  // Optimized change handlers
  const handleMethodChange = useCallback((method: string) => {
    handleInputChange('method', method);
  }, [handleInputChange]);

  const handleUrlChange = useCallback((url: string) => {
    handleInputChange('url', url);
  }, [handleInputChange]);

  // Key-value pair handlers (headers/params) - now supports KeyValuePair arrays
  const createKeyValueHandler = useCallback((field: 'headers' | 'params') => ({
    onChange: (items: KeyValuePair[]) => {
      console.log(`[RequestEditor] ${field}Change called with:`, items);
      handleInputChange(field, items);
    }
  }), [handleInputChange]);

  // Memoized handlers
  const headerHandlers = useMemo(() => createKeyValueHandler('headers'), [createKeyValueHandler]);
  const paramHandlers = useMemo(() => createKeyValueHandler('params'), [createKeyValueHandler]);

  // Script change handlers - simplified to avoid dependency loops
  const handlePreScriptChange = useCallback((value: string) => {
    setPreScript(value);
    setHasUnsavedChanges(true);
    // Update the main request data using functional update
    setRequestData(prev => {
      const newData = { ...prev, preScript: value };
      onChange?.(newData);
      return newData;
    });
  }, [onChange]);

  const handleTestScriptChange = useCallback((value: string) => {
    setTestScript(value);
    setHasUnsavedChanges(true);
    // Update the main request data using functional update
    setRequestData(prev => {
      const newData = { ...prev, testScript: value };
      onChange?.(newData);
      return newData;
    });
  }, [onChange]);

  const handleBodyChange = useCallback((body: string) => {
    handleInputChange('body', body);
  }, [handleInputChange]);

  // Auth change handlers
  const handleAuthChange = useCallback((authType: 'none' | 'bearer' | 'basic' | 'api-key') => {
    console.log('[RequestEditor] handleAuthChange called with:', authType);

    updateRequestData(prev => {
      let newAuth: any = { type: authType };

      // Preserve existing auth data if switching back to same type
      if (authType === 'basic' && prev.auth?.type === 'basic') {
        newAuth = {
          type: authType,
          username: prev.auth.username || '',
          password: prev.auth.password || ''
        };
      } else if (authType === 'bearer' && prev.auth?.type === 'bearer') {
        newAuth = {
          type: authType,
          token: prev.auth.token || ''
        };
      } else if (authType === 'api-key' && prev.auth?.type === 'api-key') {
        newAuth = {
          type: authType,
          key: prev.auth.key || '',
          value: prev.auth.value || ''
        };
      }

      return { ...prev, auth: newAuth };
    });
  }, [updateRequestData]);

  const handleAuthFieldChange = useCallback((field: string, value: string) => {
    console.log(`[RequestEditor] handleAuthFieldChange called - field: ${field}, value:`, value);

    updateRequestData(prev => ({
      ...prev,
      auth: { ...prev.auth, [field]: value }
    }));
  }, [updateRequestData]);

  // Body type change handler
  const handleBodyTypeChange = useCallback((type: string) => {
    setBodyType(type);

    // Clear body content when switching to 'none'
    if (type === 'none') {
      handleInputChange('body', '');
    }
    // Initialize form data structure for form types
    else if (type === 'form-data' || type === 'x-www-form-urlencoded') {
      if (!requestData.body || typeof requestData.body === 'string') {
        handleInputChange('body', []);
      }
    }
  }, [handleInputChange, requestData.body]);

  // Form data handlers
  const handleFormDataChange = useCallback((formData: Array<{key: string, value: string, type?: string}>) => {
    console.log('[RequestEditor] handleFormDataChange called with:', formData);
    handleInputChange('body', formData);
  }, [handleInputChange]);

  // Simple form data table component
  const FormDataTable = ({ data, onChange, type }: {
    data: Array<{key: string, value: string, type?: string}>,
    onChange: (data: Array<{key: string, value: string, type?: string}>) => void,
    type: 'form-data' | 'x-www-form-urlencoded'
  }) => {
    const [rows, setRows] = useState(() => {
      const initialRows = Array.isArray(data) && data.length > 0 ? data : [];
      return [...initialRows, { key: '', value: '', type: 'text' }];
    });

    const updateRow = (index: number, field: string, value: string) => {
      const newRows = [...rows];
      newRows[index] = { ...newRows[index], [field]: value };

      // Add new empty row if this is the last row and user is typing
      if (index === newRows.length - 1 && field === 'key' && value) {
        newRows.push({ key: '', value: '', type: 'text' });
      }

      setRows(newRows);

      // Filter out empty rows for onChange
      const validRows = newRows.filter(row => row.key.trim() !== '');
      onChange(validRows);
    };

    const deleteRow = (index: number) => {
      const newRows = rows.filter((_, i) => i !== index);
      setRows(newRows);
      const validRows = newRows.filter(row => row.key.trim() !== '');
      onChange(validRows);
    };

    return (
      <div className="form-data-table">
        <div className="table-header">
          <span>Key</span>
          <span>Value</span>
          {type === 'form-data' && <span>Type</span>}
          <span>Actions</span>
        </div>
        {rows.map((row, index) => (
          <div key={index} className="table-row">
            <input
              type="text"
              placeholder="Key"
              value={row.key}
              onChange={(e) => updateRow(index, 'key', e.target.value)}
              disabled={readOnly}
            />
            <input
              type="text"
              placeholder="Value"
              value={row.value}
              onChange={(e) => updateRow(index, 'value', e.target.value)}
              disabled={readOnly}
            />
            {type === 'form-data' && (
              <select
                value={row.type || 'text'}
                onChange={(e) => updateRow(index, 'type', e.target.value)}
                disabled={readOnly}
              >
                <option value="text">Text</option>
                <option value="file">File</option>
              </select>
            )}
            <button
              type="button"
              onClick={() => deleteRow(index)}
              disabled={readOnly || rows.length <= 1}
              className="delete-btn"
            >
              ×
            </button>
          </div>
        ))}
      </div>
    );
  };

  const handleSave = useCallback(() => {
    // Merge script state with request data for saving
    const dataToSave = {
      ...requestData,
      preScript,
      testScript
    };
    console.log('[RequestEditor] handleSave called with requestData:', dataToSave);
    console.log('[RequestEditor] Headers:', dataToSave.headers);
    console.log('[RequestEditor] Params:', dataToSave.params);
   // onSave(dataToSave);
    setHasUnsavedChanges(false);
  }, [requestData, preScript, testScript]);

  // Unified keyboard shortcuts for editor
  const editorShortcuts = [
    SHORTCUTS.SAVE,
    SHORTCUTS.SEND_REQUEST
  ];

  const editorShortcutHandlers = {
    's': () => {
      handleSave();
    },
    'Enter': () => {
      onSendRequest();
    }
  };

  useKeyboardShortcuts(editorShortcuts, editorShortcutHandlers);

  // Custom save shortcut event listener (for external triggers)
  useEffect(() => {
    const handleSaveShortcut = () => {
      if (hasUnsavedChanges) {
        handleSave();
      }
    };

    window.addEventListener('editor-save-shortcut', handleSaveShortcut);
    return () => {
      window.removeEventListener('editor-save-shortcut', handleSaveShortcut);
    };
  }, [hasUnsavedChanges, handleSave]);

  // Memoized body content for Monaco Editor
  const bodyContent = useMemo(() => {
    if (typeof requestData.body === 'string') {
      return requestData.body;
    }
    return requestData.body ? JSON.stringify(requestData.body, null, 2) : '';
  }, [requestData.body]);

  // Tab content renderer
  const renderTabContent = useMemo(() => {
    switch (activeTab) {
      case 'params':
        return (
          <ParamsTable
            params={convertToKeyValuePairs(requestData.params || {})}
            onChange={paramHandlers.onChange}
          />
        );

      case 'headers':
        return (
          <HeadersTable
            headers={convertToKeyValuePairs(requestData.headers || {})}
            onChange={headerHandlers.onChange}
          />
        );

      case 'body':
        return (
          <div className="body-editor">
            <div className="body-type-selector">
              {['none', 'raw', 'form-data', 'x-www-form-urlencoded'].map(type => (
                <label key={type}>
                  <input
                    type="radio"
                    name="body-type"
                    value={type}
                    checked={bodyType === type}
                    onChange={(e) => handleBodyTypeChange(e.target.value)}
                    disabled={readOnly}
                  />
                  {type}
                </label>
              ))}
            </div>

            {bodyType === 'raw' && (
              <div className="body-content">
                <div className="body-format-selector">
                  <select
                    value={bodyLanguage}
                    onChange={(e) => setBodyLanguage(e.target.value)}
                  >
                    {BODY_LANGUAGES.map(({ value, label }) => (
                      <option key={value} value={value}>{label}</option>
                    ))}
                  </select>
                </div>
                <div className="monaco-editor-container-wrapper">
                  <MonacoEditor
                    value={bodyContent}
                    onChange={handleBodyChange}
                    language={bodyLanguage}
                    placeholder={`Enter ${bodyLanguage.toUpperCase()} content...`}
                    label="Request Body"
                    theme="vs-dark"
                    showToolbar={true}
                    readOnly={readOnly}
                  />
                </div>
              </div>
            )}

            {bodyType === 'none' && (
              <div className="body-empty">
                <p>This request does not have a body</p>
              </div>
            )}

            {(bodyType === 'form-data' || bodyType === 'x-www-form-urlencoded') && (
              <div className="body-form">
                <FormDataTable
                  data={Array.isArray(requestData.body) ? requestData.body : []}
                  onChange={handleFormDataChange}
                  type={bodyType as 'form-data' | 'x-www-form-urlencoded'}
                />
              </div>
            )}
          </div>
        );

      case 'auth':
        return (
          <div className="auth-editor">
            <div className="auth-type-selector">
              <label>Type:</label>
              <select
                value={requestData.auth?.type || 'none'}
                onChange={(e) => handleAuthChange(e.target.value as 'none' | 'bearer' | 'basic' | 'api-key')}
                disabled={readOnly}
              >
                <option value="none">No Auth</option>
                <option value="api-key">API Key</option>
                <option value="bearer">Bearer Token</option>
                <option value="basic">Basic Auth</option>
              </select>
            </div>

            {requestData.auth?.type === 'basic' && (
              <div className="auth-fields">
                <div className="form-group">
                  <label>Username:</label>
                  <input
                    type="text"
                    value={requestData.auth.username || ''}
                    onChange={(e) => handleAuthFieldChange('username', e.target.value)}
                    placeholder="Enter username"
                    disabled={readOnly}
                  />
                </div>
                <div className="form-group">
                  <label>Password:</label>
                  <input
                    type="password"
                    value={requestData.auth.password || ''}
                    onChange={(e) => handleAuthFieldChange('password', e.target.value)}
                    placeholder="Enter password"
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            {requestData.auth?.type === 'bearer' && (
              <div className="auth-fields">
                <div className="form-group">
                  <label>Token:</label>
                  <input
                    type="text"
                    value={requestData.auth.token || ''}
                    onChange={(e) => handleAuthFieldChange('token', e.target.value)}
                    placeholder="Enter bearer token"
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            {requestData.auth?.type === 'api-key' && (
              <div className="auth-fields">
                <div className="form-group">
                  <label>Key:</label>
                  <input
                    type="text"
                    value={requestData.auth.key || ''}
                    onChange={(e) => handleAuthFieldChange('key', e.target.value)}
                    placeholder="Enter API key name"
                    disabled={readOnly}
                  />
                </div>
                <div className="form-group">
                  <label>Value:</label>
                  <input
                    type="text"
                    value={requestData.auth.value || ''}
                    onChange={(e) => handleAuthFieldChange('value', e.target.value)}
                    placeholder="Enter API key value"
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            {requestData.auth?.type === 'none' && (
              <p>No authentication will be used for this request.</p>
            )}
          </div>
        );

      case 'pre-script':
        return (
          <textarea
            className="script-editor"
            placeholder="Pre-request script (JavaScript)..."
            value={preScript}
            onChange={(e) => handlePreScriptChange(e.target.value)}
            readOnly={readOnly}
          />
        );

      case 'tests':
        return (
          <textarea
            className="script-editor"
            placeholder="Test script (JavaScript)..."
            value={testScript}
            onChange={(e) => handleTestScriptChange(e.target.value)}
            readOnly={readOnly}
          />
        );

      default:
        return null;
    }
  }, [
    activeTab,
    requestData,
    paramHandlers,
    headerHandlers,
    bodyType,
    bodyLanguage,
    bodyContent,
    handleBodyChange,
    handleBodyTypeChange,
    handleFormDataChange,
    handleAuthChange,
    handleAuthFieldChange,
    handleInputChange,
    readOnly,
    preScript,
    testScript,
    handlePreScriptChange,
    handleTestScriptChange
  ]);

  // Tab configuration
  const tabs = useMemo(() => [
    { id: 'params', label: 'Params' },
    { id: 'headers', label: 'Headers' },
    { id: 'body', label: 'Body' },
    { id: 'auth', label: 'Authorization' },
    { id: 'pre-script', label: 'Pre-request Script' },
    { id: 'tests', label: 'Tests' }
  ], []);

  return (
    <div className="request-editor">
      {/* URL Bar */}
      <div className="request-url-bar">
        <div className="url-input-group">
          <select
            className="method-select"
            value={requestData.method}
            onChange={(e) => handleMethodChange(e.target.value)}
            disabled={readOnly}
          >
            {HTTP_METHODS.map(method => (
              <option key={method} value={method}>{method}</option>
            ))}
          </select>
          <input
            type="text"
            className="url-input"
            placeholder="Enter request URL..."
            value={requestData.url}
            onChange={(e) => handleUrlChange(e.target.value)}
            readOnly={readOnly}
          />
          <button className="send-btn" onClick={onSendRequest}>
            Send
          </button>
        </div>
      </div>

      {/* Content Tabs */}
      <div className="content-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`content-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content-area">
        {renderTabContent}
      </div>
    </div>
  );
};

export default RequestEditor;