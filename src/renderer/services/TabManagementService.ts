import { ItemServiceUtils } from './ItemServiceRegistry';
import { TabItem } from '../components/ItemTabs';

/**
 * Centralized tab management service that handles all tab operations
 * using the appropriate item services
 */
export class TabManagementService {
  /**
   * Save a tab item to the database
   */
  static async saveTabItem(tabItem: TabItem, workspaceId: number): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    needsDialog?: boolean;
  }> {
    try {
      console.log('[TabManagementService] saveTabItem called with tabItem:', tabItem);
      console.log('[TabManagementService] TabItem data:', tabItem.data);
      console.log('[TabManagementService] TabItem headers:', tabItem.data?.headers);
      console.log('[TabManagementService] TabItem params:', tabItem.data?.params);

      // Check if this item type needs a save dialog
      const needsDialog = ItemServiceUtils.needsSaveDialog(tabItem);

      if (needsDialog) {
        return {
          success: false,
          needsDialog: true
        };
      }

      // Save using the appropriate service
      const savedData = await ItemServiceUtils.saveTabItem(tabItem, workspaceId);
      
      return {
        success: true,
        data: savedData
      };
    } catch (error) {
      console.error('Failed to save tab item:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Delete a tab item from the database
   */
  static async deleteTabItem(tabItem: TabItem): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const success = await ItemServiceUtils.deleteTabItem(tabItem);
      return { success };
    } catch (error) {
      console.error('Failed to delete tab item:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Load a tab item from the database
   */
  static async loadTabItem(type: string, id: string | number): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const data = await ItemServiceUtils.loadTabItem(type, id);
      
      if (!data) {
        return {
          success: false,
          error: 'Item not found'
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('Failed to load tab item:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create a new tab item with default data
   */
  static createNewTabItem(type: string, overrides: any = {}): TabItem {
    return ItemServiceUtils.createNewTabItem(type, overrides);
  }

  /**
   * Check if a tab item is new (not saved to database)
   */
  static isNewTabItem(tabItem: TabItem): boolean {
    return ItemServiceUtils.isNewTabItem(tabItem);
  }

  /**
   * Get display name for a tab item
   */
  static getTabDisplayName(tabItem: TabItem): string {
    return ItemServiceUtils.getTabDisplayName(tabItem);
  }

  /**
   * Get icon for a tab item
   */
  static getTabIcon(tabItem: TabItem): string {
    return ItemServiceUtils.getTabIcon(tabItem);
  }

  /**
   * Get method badge for request-type items
   */
  static getMethodBadge(tabItem: TabItem): string | null {
    return ItemServiceUtils.getMethodBadge(tabItem);
  }

  /**
   * Get method color for HTTP methods
   */
  static getMethodColor(method: string): string {
    return ItemServiceUtils.getMethodColor(method);
  }

  /**
   * Check if a tab item has unsaved changes
   */
  static hasUnsavedChanges(tabItem: TabItem): boolean {
    // Check explicit flag first
    if (tabItem.hasUnsavedChanges === true) {
      return true;
    }

    // Check if it's a new item
    return this.isNewTabItem(tabItem);
  }

  /**
   * Update tab item data
   */
  static updateTabItemData(tabItem: TabItem, updates: any): TabItem {
    return {
      ...tabItem,
      data: {
        ...tabItem.data,
        ...updates
      },
      hasUnsavedChanges: true
    };
  }

  /**
   * Mark tab item as saved
   */
  static markTabItemAsSaved(tabItem: TabItem, savedData?: any): TabItem {
    return {
      ...tabItem,
      data: savedData ? { ...tabItem.data, ...savedData } : tabItem.data,
      hasUnsavedChanges: false
    };
  }

  /**
   * Convert legacy Request object to TabItem
   */
  static requestToTabItem(request: any): TabItem {
    return {
      id: request.id,
      type: 'request',
      data: {
        entityId: request.entityId,
        name: request.name,
        method: request.method,
        url: request.url,
        headers: request.headers,
        params: request.params,
        body: request.body
      },
      hasUnsavedChanges: request.hasUnsavedChanges
    };
  }

  /**
   * Convert TabItem to legacy Request object (for backward compatibility)
   */
  static tabItemToRequest(tabItem: TabItem): any {
    if (tabItem.type !== 'request') {
      throw new Error('TabItem is not a request type');
    }

    return {
      id: tabItem.id,
      entityId: tabItem.data.entityId,
      name: tabItem.data.name,
      method: tabItem.data.method,
      url: tabItem.data.url,
      headers: tabItem.data.headers,
      params: tabItem.data.params,
      body: tabItem.data.body,
      hasUnsavedChanges: tabItem.hasUnsavedChanges
    };
  }
}
