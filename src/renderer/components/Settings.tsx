import React, { useState, useEffect } from 'react';

interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'ollama' | 'gemini' | 'anthropic' | 'siliconflow' | 'alibaba' | 'custom';
  baseUrl?: string;
  apiKey?: string;
  models: string[];
}

interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  ai: {
    defaultProvider: string;
    providers: AIProvider[];
    defaultModel: string;
    temperature: number;
    maxTokens: number;
  };
  general: {
    autoSave: boolean;
    confirmDelete: boolean;
    showLineNumbers: boolean;
    wordWrap: boolean;
  };
  network: {
    timeout: number;
    followRedirects: boolean;
    validateSSL: boolean;
  };
}

interface SettingsProps {
  onClose: () => void;
}

const Settings: React.FC<SettingsProps> = ({ onClose }) => {
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'ai' | 'network' | 'about'>('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingProvider, setEditingProvider] = useState<AIProvider | null>(null);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const settingsData = await window.electronAPI.settings.getAll();
      setSettings(settingsData);
    } catch (err) {
      setError('Failed to load settings');
      console.error('Error loading settings:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      await window.electronAPI.settings.updateMultiple(settings);
      
      // Apply theme immediately
      document.documentElement.setAttribute('data-theme', settings.theme);
      
      // Dispatch theme change event
      window.dispatchEvent(new CustomEvent('theme-changed', { detail: settings.theme }));
    } catch (err) {
      setError('Failed to save settings');
      console.error('Error saving settings:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = (path: string, value: any) => {
    if (!settings) return;

    const newSettings = { ...settings };
    const keys = path.split('.');
    let current: any = newSettings;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setSettings(newSettings);
  };

  const handleProviderSave = async (provider: AIProvider) => {
    if (!settings) return;

    try {
      await window.electronAPI.settings.updateAIProvider(provider);

      const updatedProviders = [...settings.ai.providers];
      const existingIndex = updatedProviders.findIndex(p => p.id === provider.id);

      if (existingIndex >= 0) {
        updatedProviders[existingIndex] = provider;
      } else {
        updatedProviders.push(provider);
      }

      // Update the providers list
      handleSettingChange('ai.providers', updatedProviders);

      // For Ollama providers, update the default provider and model if a model is selected
      if (provider.type === 'ollama' && provider.models && provider.models.length > 0) {
        // Set this provider as the default
        handleSettingChange('ai.defaultProvider', provider.id);
        // Set the selected model as the default
        handleSettingChange('ai.defaultModel', provider.models[0]);
      }

      setEditingProvider(null);

      // Notify chat panel of AI settings change with provider info
      window.dispatchEvent(new CustomEvent('ai-settings-changed', {
        detail: {
          provider: provider,
          isOllama: provider.type === 'ollama',
          defaultModel: provider.type === 'ollama' ? provider.models[0] : undefined
        }
      }));
    } catch (err) {
      setError('Failed to save provider');
    }
  };

  const handleProviderDelete = async (providerId: string) => {
    if (!settings) return;
    if (!confirm('Are you sure you want to delete this provider?')) return;

    try {
      await window.electronAPI.settings.removeAIProvider(providerId);
      const updatedProviders = settings.ai.providers.filter(p => p.id !== providerId);
      handleSettingChange('ai.providers', updatedProviders);

      // Notify chat panel of AI settings change
      window.dispatchEvent(new CustomEvent('ai-settings-changed'));
    } catch (err) {
      setError('Failed to delete provider');
    }
  };

  const handleProviderTest = async (provider: AIProvider) => {
    try {
      setTestingProvider(provider.id);
      const isConnected = await window.electronAPI.ai.testConnection(provider);
      alert(isConnected ? 'Connection successful!' : 'Connection failed!');
    } catch (err) {
      alert('Connection test failed!');
    } finally {
      setTestingProvider(null);
    }
  };

  const resetSettings = async () => {
    if (!confirm('Are you sure you want to reset all settings to defaults?')) return;

    try {
      await window.electronAPI.settings.reset();
      await loadSettings();
    } catch (err) {
      setError('Failed to reset settings');
    }
  };

  if (loading) {
    return (
      <div className="settings-modal">
        <div className="settings-content">
          <div className="settings-loading">Loading settings...</div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="settings-modal">
        <div className="settings-content">
          <div className="settings-error">Failed to load settings</div>
        </div>
      </div>
    );
  }

  return (
    <div className="settings-modal">
      <div className="settings-content">
        <div className="settings-header">
          <h2>Settings</h2>
          <button className="settings-close" onClick={onClose}>×</button>
        </div>

        {error && (
          <div className="error-message">
            {error}
            <button onClick={() => setError(null)}>×</button>
          </div>
        )}

        <div className="settings-body">
          <div className="settings-sidebar">
            <div className="settings-tabs">
              <button
                className={`settings-tab ${activeTab === 'general' ? 'active' : ''}`}
                onClick={() => setActiveTab('general')}
              >
                General
              </button>
              <button
                className={`settings-tab ${activeTab === 'ai' ? 'active' : ''}`}
                onClick={() => setActiveTab('ai')}
              >
                AI & Chat
              </button>
              <button
                className={`settings-tab ${activeTab === 'network' ? 'active' : ''}`}
                onClick={() => setActiveTab('network')}
              >
                Network
              </button>
              <button
                className={`settings-tab ${activeTab === 'about' ? 'active' : ''}`}
                onClick={() => setActiveTab('about')}
              >
                About
              </button>
            </div>
          </div>

          <div className="settings-main">
            {activeTab === 'general' && (
              <div className="settings-section">
                <h3>General Settings</h3>
                
                <div className="setting-group">
                  <label>Theme</label>
                  <select
                    value={settings.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                  >
                    <option value="auto">Auto (System)</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.general.autoSave}
                      onChange={(e) => handleSettingChange('general.autoSave', e.target.checked)}
                    />
                    Auto-save changes
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.general.confirmDelete}
                      onChange={(e) => handleSettingChange('general.confirmDelete', e.target.checked)}
                    />
                    Confirm before deleting
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.general.showLineNumbers}
                      onChange={(e) => handleSettingChange('general.showLineNumbers', e.target.checked)}
                    />
                    Show line numbers in editors
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.general.wordWrap}
                      onChange={(e) => handleSettingChange('general.wordWrap', e.target.checked)}
                    />
                    Word wrap in editors
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'ai' && (
              <div className="settings-section">
                <h3>AI & Chat Settings</h3>
                
                <div className="setting-group">
                  <label>Default Provider</label>
                  <select
                    value={settings.ai.defaultProvider}
                    onChange={(e) => handleSettingChange('ai.defaultProvider', e.target.value)}
                  >
                    {settings.ai.providers.map(provider => (
                      <option key={provider.id} value={provider.id}>
                        {provider.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="setting-group">
                  <label>Default Model</label>
                  <input
                    type="text"
                    value={settings.ai.defaultModel}
                    onChange={(e) => handleSettingChange('ai.defaultModel', e.target.value)}
                    placeholder="e.g., gpt-3.5-turbo"
                  />
                </div>

                <div className="setting-group">
                  <label>Temperature ({settings.ai.temperature})</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={settings.ai.temperature}
                    onChange={(e) => handleSettingChange('ai.temperature', parseFloat(e.target.value))}
                  />
                </div>

                <div className="setting-group">
                  <label>Max Tokens</label>
                  <input
                    type="number"
                    min="1"
                    max="8192"
                    value={settings.ai.maxTokens}
                    onChange={(e) => handleSettingChange('ai.maxTokens', parseInt(e.target.value))}
                  />
                </div>

                <div className="providers-section">
                  <div className="providers-header">
                    <h4>AI Providers</h4>
                    <button
                      className="btn-primary btn-with-icon"
                      onClick={() => setEditingProvider({
                        id: '',
                        name: '',
                        type: 'custom',
                        baseUrl: '',
                        apiKey: '',
                        models: []
                      })}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                      </svg>
                      Add Provider
                    </button>
                  </div>

                  <div className="providers-list">
                    {settings.ai.providers.map(provider => (
                      <div key={provider.id} className="provider-item">
                        <div className="provider-info">
                          <div className="provider-name">{provider.name}</div>
                          <div className="provider-type">{provider.type}</div>
                        </div>
                        <div className="provider-actions">
                          <button
                            className="btn-icon btn-small"
                            onClick={() => setEditingProvider(provider)}
                            title="Edit provider"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                            </svg>
                          </button>
                          <button
                            className="btn-icon btn-small"
                            onClick={() => handleProviderTest(provider)}
                            disabled={testingProvider === provider.id}
                            title={testingProvider === provider.id ? 'Testing connection...' : 'Test connection'}
                          >
                            {testingProvider === provider.id ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="spinning">
                                <path d="M21 12a9 9 0 11-6.219-8.56"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                              </svg>
                            )}
                          </button>
                          <button
                            className="btn-icon btn-small btn-danger"
                            onClick={() => handleProviderDelete(provider.id)}
                            title="Delete provider"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"/>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                              <line x1="10" y1="11" x2="10" y2="17"/>
                              <line x1="14" y1="11" x2="14" y2="17"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'network' && (
              <div className="settings-section">
                <h3>Network Settings</h3>
                
                <div className="setting-group">
                  <label>Request Timeout (ms)</label>
                  <input
                    type="number"
                    min="1000"
                    max="300000"
                    value={settings.network.timeout}
                    onChange={(e) => handleSettingChange('network.timeout', parseInt(e.target.value))}
                  />
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.network.followRedirects}
                      onChange={(e) => handleSettingChange('network.followRedirects', e.target.checked)}
                    />
                    Follow redirects
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.network.validateSSL}
                      onChange={(e) => handleSettingChange('network.validateSSL', e.target.checked)}
                    />
                    Validate SSL certificates
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div className="settings-section">
                <h3>About ApiCool</h3>
                <div className="about-content">
                  <p><strong>Version:</strong> 1.0.0</p>
                  <p><strong>Built with:</strong> Electron, React, TypeScript</p>
                  <p><strong>AI Features:</strong> OpenAI, Ollama, Gemini, Claude</p>
                  <p><strong>Import/Export:</strong> Postman, OpenAPI, Swagger</p>
                  
                  <div className="about-actions">
                    <button className="btn-danger btn-with-icon" onClick={resetSettings}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="1,4 1,10 7,10"/>
                        <path d="M3.51,15a9,9 0 1,0 2.13-9.36L1,10"/>
                      </svg>
                      Reset All Settings
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="settings-footer">
          <button className="btn-secondary btn-with-icon" onClick={onClose}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
            Cancel
          </button>
          <button
            className="btn-primary btn-with-icon"
            onClick={saveSettings}
            disabled={saving}
          >
            {saving ? (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="spinning">
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                  <polyline points="17,21 17,13 7,13 7,21"/>
                  <polyline points="7,3 7,8 15,8"/>
                </svg>
                Save
              </>
            )}
          </button>
        </div>
      </div>

      {editingProvider && (
        <ProviderEditor
          provider={editingProvider}
          onSave={handleProviderSave}
          onCancel={() => setEditingProvider(null)}
        />
      )}
    </div>
  );
};

interface ProviderEditorProps {
  provider: AIProvider;
  onSave: (provider: AIProvider) => void;
  onCancel: () => void;
}

const ProviderEditor: React.FC<ProviderEditorProps> = ({ provider, onSave, onCancel }) => {
  const [formData, setFormData] = useState<AIProvider>(provider);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [connecting, setConnecting] = useState(false);
  const [connected, setConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.id) {
      formData.id = formData.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }
    onSave(formData);
  };

  const connectToOllama = async () => {
    if (!formData.baseUrl) return;

    setConnecting(true);
    setConnectionError(null);
    setConnected(false);

    try {
      // For Ollama, use direct API call to test connection
      if (formData.type === 'ollama') {
        const response = await fetch(`${formData.baseUrl}/api/tags`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          const models = data.models?.map((model: any) => model.name) || [];
          setAvailableModels(models);
          setConnected(true);

          // If no model is currently selected and we have models, select the first one
          if (!formData.models || formData.models.length === 0) {
            if (models.length > 0) {
              setFormData({ ...formData, models: [models[0]] });
            }
          }
        } else {
          throw new Error(`Failed to connect: ${response.statusText}`);
        }
      } else {
        // For other providers, use the existing API
        const models = await window.electronAPI.ai.fetchModels({
          type: formData.type,
          baseUrl: formData.baseUrl,
          apiKey: formData.apiKey
        });
        setAvailableModels(models);
        setConnected(true);
      }
    } catch (error) {
      console.error('Error connecting:', error);
      setConnectionError(error instanceof Error ? error.message : 'Failed to connect');
      setAvailableModels([]);
      setConnected(false);
    } finally {
      setConnecting(false);
    }
  };

  const handleModelSelect = (selectedModel: string) => {
    // For Ollama, we only select one model at a time
    if (formData.type === 'ollama') {
      setFormData({ ...formData, models: [selectedModel] });
    } else {
      // For other providers, keep the multi-select behavior
      const currentModels = formData.models || [];
      const isSelected = currentModels.includes(selectedModel);
      const newModels = isSelected
        ? currentModels.filter(m => m !== selectedModel)
        : [...currentModels, selectedModel];
      setFormData({ ...formData, models: newModels });
    }
  };

  const getPlaceholderForType = (type: string) => {
    switch (type) {
      case 'openai':
        return 'https://api.openai.com/v1';
      case 'ollama':
        return 'http://localhost:11434';
      case 'gemini':
        return 'https://generativelanguage.googleapis.com/v1beta';
      case 'anthropic':
        return 'https://api.anthropic.com/v1';
      case 'siliconflow':
        return 'https://api.siliconflow.cn/v1';
      case 'alibaba':
        return 'https://dashscope.aliyuncs.com/api/v1';
      case 'custom':
        return 'https://your-api.example.com/v1';
      default:
        return 'https://api.example.com/v1';
    }
  };

  // Handle Escape key to close modal
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onCancel();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onCancel]);

  return (
    <div className="modal-overlay" onClick={(e) => e.target === e.currentTarget && onCancel()}>
      <div className="modal-content">
        <div className="modal-header">
          <h3>
            {provider.id ? 'Edit Provider' : 'Add Provider'}
            {formData.type === 'ollama' && provider.id && (
              <span className="provider-type-badge">Ollama</span>
            )}
          </h3>
          <button onClick={onCancel}>×</button>
        </div>

        <form id="provider-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label>Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>

          {/* Only show type selector for new providers or non-Ollama providers */}
          {(!provider.id || formData.type !== 'ollama') && (
            <div className="form-group">
              <label>Type</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
              >
                <option value="openai">OpenAI</option>
                <option value="ollama">Ollama (Local)</option>
                <option value="gemini">Google Gemini</option>
                <option value="anthropic">Anthropic Claude</option>
                <option value="siliconflow">SiliconFlow</option>
                <option value="alibaba">Alibaba Cloud (DashScope)</option>
                <option value="custom">Custom OpenAI-Compatible</option>
              </select>
            </div>
          )}

          <div className="form-group">
            <label>Base URL</label>
            <div className="input-with-button">
              <input
                type="url"
                value={formData.baseUrl || ''}
                onChange={(e) => {
                  setFormData({ ...formData, baseUrl: e.target.value });
                  // Reset connection state when URL changes
                  setConnected(false);
                  setConnectionError(null);
                  setAvailableModels([]);
                }}
                placeholder={getPlaceholderForType(formData.type)}
              />
              <button
                type="button"
                className={`btn-secondary btn-small ${connected ? 'connected' : ''}`}
                onClick={connectToOllama}
                disabled={!formData.baseUrl || connecting}
                title={formData.type === 'ollama' ? 'Connect to Ollama' : 'Fetch available models'}
              >
                {connecting ? (
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="spinning">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                ) : connected ? (
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 6L9 17l-5-5"/>
                  </svg>
                ) : (
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                  </svg>
                )}
              </button>
            </div>
          </div>

          {/* Hide API Key for Ollama since it doesn't need authentication */}
          {formData.type !== 'ollama' && (
            <div className="form-group">
              <label>API Key</label>
              <input
                type="password"
                value={formData.apiKey || ''}
                onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                placeholder="Your API key"
              />
            </div>
          )}

          <div className="form-group">
            <label>Models</label>
            {connectionError && (
              <div className="error-message">
                {connectionError}
              </div>
            )}
            <div className="models-container">
              {formData.type === 'ollama' ? (
                // Single-select dropdown for Ollama
                availableModels.length > 0 ? (
                  <div className="models-dropdown-container">
                    <select
                      value={formData.models?.[0] || ''}
                      onChange={(e) => handleModelSelect(e.target.value)}
                      className="models-dropdown-single"
                    >
                      <option value="">Select a model...</option>
                      {availableModels.map(model => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))}
                    </select>
                    <div className="connection-status">
                      {connected ? (
                        <span className="status-connected">
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M20 6L9 17l-5-5"/>
                          </svg>
                          Connected • {availableModels.length} models available
                        </span>
                      ) : (
                        <span className="status-disconnected">
                          Click Connect to fetch models from Ollama
                        </span>
                      )}
                    </div>
                  </div>
                ) : (
                  <div>
                    <input
                      type="text"
                      value={formData.models?.join(', ') || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        models: e.target.value.split(',').map(m => m.trim()).filter(Boolean)
                      })}
                      placeholder="Enter model names separated by commas, or click Connect to fetch from Ollama"
                    />
                    <div className="help-text">
                      Enter models manually or click the Connect button to fetch from Ollama
                    </div>
                  </div>
                )
              ) : (
                // Multi-select for other providers
                availableModels.length > 0 ? (
                  <div className="models-selection">
                    <select
                      multiple
                      value={formData.models || []}
                      onChange={(e) => {
                        const selectedModels = Array.from(e.target.selectedOptions, option => option.value);
                        setFormData({ ...formData, models: selectedModels });
                      }}
                      className="models-dropdown"
                      size={Math.min(availableModels.length, 8)}
                    >
                      {availableModels.map(model => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))}
                    </select>
                    <div className="models-info">
                      <span className="selected-count">
                        {formData.models?.length || 0} of {availableModels.length} models selected
                      </span>
                      <div className="models-actions">
                        <button
                          type="button"
                          className="btn-small"
                          onClick={() => setFormData({ ...formData, models: availableModels })}
                        >
                          Select All
                        </button>
                        <button
                          type="button"
                          className="btn-small"
                          onClick={() => setFormData({ ...formData, models: [] })}
                        >
                          Clear All
                        </button>
                      </div>
                    </div>
                    <div className="help-text">
                      Hold Ctrl/Cmd to select multiple models
                    </div>
                  </div>
                ) : (
                  <div className="manual-models">
                    <input
                      type="text"
                      value={formData.models?.join(', ') || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        models: e.target.value.split(',').map(m => m.trim()).filter(Boolean)
                      })}
                      placeholder="gpt-4, gpt-3.5-turbo"
                    />
                    <div className="help-text">
                      Enter models manually or fetch from provider above
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </form>

        <div className="form-actions">
          <button type="button" className="btn-secondary btn-with-icon" onClick={onCancel}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
            Cancel
          </button>
          <button type="submit" className="btn-primary btn-with-icon" form="provider-form">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
              <polyline points="17,21 17,13 7,13 7,21"/>
              <polyline points="7,3 7,8 15,8"/>
            </svg>
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;
