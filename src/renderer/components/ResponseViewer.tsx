import React, { useState, useMemo } from 'react';
import MonacoEditor from './MonacoEditor';

interface RequestResult {
  id: string;
  name: string;
  status: 'success' | 'error' | 'timeout';
  statusCode?: number;
  statusText?: string;
  responseTime: number;
  responseSize: number;
  responseHeaders: Record<string, string>;
  responseBody: any;
  error?: string;
  testResults?: TestResult[];
  cookies?: Cookie[];
}

interface TestResult {
  name: string;
  status: 'pass' | 'fail';
  message?: string;
}

interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: string;
  httpOnly?: boolean;
  secure?: boolean;
}

interface ResponseViewerProps {
  response?: RequestResult | null;
  loading?: boolean;
  requestId?: string;
  requestName?: string;
  onSaveAsExample?: (exampleData: any) => void;
}

// Helper function to map content types to Monaco Editor languages
const getMonacoLanguage = (contentType: string): string => {
  switch (contentType.toLowerCase()) {
    case 'json':
      return 'json';
    case 'xml':
      return 'xml';
    case 'html':
      return 'html';
    case 'css':
      return 'css';
    case 'javascript':
      return 'javascript';
    case 'typescript':
      return 'typescript';
    case 'yaml':
    case 'yml':
      return 'yaml';
    case 'markdown':
    case 'md':
      return 'markdown';
    case 'sql':
      return 'sql';
    case 'python':
      return 'python';
    case 'java':
      return 'java';
    case 'csharp':
    case 'c#':
      return 'csharp';
    case 'cpp':
    case 'c++':
      return 'cpp';
    case 'php':
      return 'php';
    case 'ruby':
      return 'ruby';
    case 'go':
      return 'go';
    case 'rust':
      return 'rust';
    case 'shell':
    case 'bash':
      return 'shell';
    default:
      return 'plaintext';
  }
};

const ResponseViewer: React.FC<ResponseViewerProps> = ({
  response,
  loading = false,
  requestId,
  requestName,
  onSaveAsExample
}) => {
  const [activeTab, setActiveTab] = useState('body');
  const [bodyViewMode, setBodyViewMode] = useState<'pretty' | 'raw' | 'tree'>('pretty');
  const [searchTerm, setSearchTerm] = useState('');
  const [wrapLines, setWrapLines] = useState(true);
  const [showLineNumbers, setShowLineNumbers] = useState(false);

  // Content type detection and formatting
  const contentInfo = useMemo(() => {
    if (!response) return { type: 'text', size: 0, formatted: '', isBinary: false, binaryData: null };

    const contentType = response.responseHeaders['content-type'] || response.responseHeaders['Content-Type'] || '';
    const body = response.responseBody;

    let type = 'text';
    let formatted = '';
    let size = response.responseSize;
    let isBinary = false;
    let binaryData = null;

    // Check if response is binary format from our processing
    if (typeof body === 'object' && body !== null && body.type === 'binary') {
      type = 'binary';
      isBinary = true;
      binaryData = {
        data: body.data,
        size: body.size,
        contentType: body.contentType
      };
      formatted = `Binary data (${body.size} bytes)\nContent-Type: ${body.contentType}\n\nBase64 Preview:\n${body.data.substring(0, 200)}${body.data.length > 200 ? '...' : ''}`;
    } else if (contentType.includes('application/json')) {
      type = 'json';
      try {
        const parsed = typeof body === 'string' ? JSON.parse(body) : body;
        formatted = JSON.stringify(parsed, null, 2);
      } catch {
        formatted = typeof body === 'string' ? body : JSON.stringify(body);
      }
    } else if (contentType.includes('text/html')) {
      type = 'html';
      formatted = typeof body === 'string' ? body : String(body);
    } else if (contentType.includes('text/xml') || contentType.includes('application/xml')) {
      type = 'xml';
      formatted = typeof body === 'string' ? body : String(body);
    } else if (contentType.includes('text/')) {
      type = 'text';
      formatted = typeof body === 'string' ? body : String(body);
    } else {
      // Check if it's likely binary based on content type
      const binaryTypes = ['image/', 'video/', 'audio/', 'application/pdf', 'application/zip', 'application/octet-stream'];
      if (binaryTypes.some(bType => contentType.toLowerCase().includes(bType))) {
        type = 'binary';
        isBinary = true;
        formatted = `Binary content detected\nContent-Type: ${contentType}\nSize: ${size} bytes\n\nUse "Download" to save the file.`;
      } else {
        type = 'text';
        formatted = typeof body === 'string' ? body : String(body);
      }
    }

    return { type, formatted, size, isBinary, binaryData };
  }, [response]);

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could show a toast notification here
      console.log('Copied to clipboard');
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const downloadBinaryContent = () => {
    if (!contentInfo.binaryData) return;

    try {
      // Convert base64 to blob
      const byteCharacters = atob(contentInfo.binaryData.data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: contentInfo.binaryData.contentType });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename from content type or use generic name
      const contentType = contentInfo.binaryData.contentType;
      let extension = '';
      if (contentType.includes('image/png')) extension = '.png';
      else if (contentType.includes('image/jpeg')) extension = '.jpg';
      else if (contentType.includes('image/gif')) extension = '.gif';
      else if (contentType.includes('image/svg')) extension = '.svg';
      else if (contentType.includes('application/pdf')) extension = '.pdf';
      else if (contentType.includes('application/zip')) extension = '.zip';
      else if (contentType.includes('text/')) extension = '.txt';
      else if (contentType.includes('application/json')) extension = '.json';

      link.download = `response_${Date.now()}${extension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download binary content:', error);
    }
  };

  const downloadResponse = () => {
    if (!response) return;

    const blob = new Blob([contentInfo.formatted], {
      type: contentInfo.type === 'json' ? 'application/json' : 'text/plain'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `response_${response.id}.${contentInfo.type === 'json' ? 'json' : 'txt'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const saveAsExample = () => {
    if (!response || !requestId || !onSaveAsExample) return;

    const exampleData = {
      name: `${requestName || 'Request'} - ${response.statusCode} Response`,
      requestId: requestId,
      response: {
        status: response.statusCode,
        statusText: response.statusText,
        headers: response.responseHeaders,
        body: response.responseBody,
        responseTime: response.responseTime,
        size: response.responseSize
      }
    };

    onSaveAsExample(exampleData);
  };

  const highlightSearchTerm = (text: string, term: string): string => {
    if (!term.trim()) return text;

    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  };

  const getStatusColor = (status?: number) => {
    if (!status) return '#6c757d';
    if (status >= 200 && status < 300) return '#28a745';
    if (status >= 300 && status < 400) return '#ffc107';
    if (status >= 400 && status < 500) return '#fd7e14';
    if (status >= 500) return '#dc3545';
    return '#6c757d';
  };

  // Show loading state
  if (loading) {
    return (
      <div className="response-viewer">
        <div className="response-loading">
          <div className="loading-spinner"></div>
          <p>Sending request...</p>
        </div>
      </div>
    );
  }

  // Show empty state if no response
  if (!response) {
    return (
      <div className="response-viewer">
        <div className="response-empty">
          <p>Send a request to see the response here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="response-viewer">
      <div className="response-header">
        <div className="response-tabs">
          <button
            className={`response-tab ${activeTab === 'body' ? 'active' : ''}`}
            onClick={() => setActiveTab('body')}
          >
            Body
          </button>
          <button
            className={`response-tab ${activeTab === 'cookies' ? 'active' : ''}`}
            onClick={() => setActiveTab('cookies')}
          >
            Cookies ({response.cookies?.length || 0})
          </button>
          <button
            className={`response-tab ${activeTab === 'headers' ? 'active' : ''}`}
            onClick={() => setActiveTab('headers')}
          >
            Headers ({Object.keys(response.responseHeaders).length})
          </button>
          <button
            className={`response-tab ${activeTab === 'test-results' ? 'active' : ''}`}
            onClick={() => setActiveTab('test-results')}
          >
            Test Results ({response.testResults?.length || 0})
          </button>
        </div>

        <div className="response-meta">
          <span
            className="status-badge"
            style={{ backgroundColor: getStatusColor(response.statusCode) }}
          >
            {response.statusCode} {response.statusText}
          </span>
          <span className="time-badge">{response.responseTime}ms</span>
          <span className="size-badge">{formatSize(response.responseSize)}</span>
        </div>
      </div>

      <div className="response-content">
        {activeTab === 'body' && (
          <div className="response-body-container">
            {/* Body Toolbar */}
            <div className="response-body-toolbar">
              <div className="toolbar-left">
                <span className="content-type-badge">
                  {contentInfo.type.toUpperCase()}
                </span>
                <span className="content-size">
                  {formatSize(contentInfo.size)}
                </span>
              </div>

              <div className="toolbar-center">
                <div className="view-mode-buttons">
                  <button
                    className={`view-mode-btn ${bodyViewMode === 'pretty' ? 'active' : ''}`}
                    onClick={() => setBodyViewMode('pretty')}
                    title="Pretty Print"
                  >
                    Pretty
                  </button>
                  <button
                    className={`view-mode-btn ${bodyViewMode === 'raw' ? 'active' : ''}`}
                    onClick={() => setBodyViewMode('raw')}
                    title="Raw"
                  >
                    Raw
                  </button>
                  {contentInfo.type === 'json' && (
                    <button
                      className={`view-mode-btn ${bodyViewMode === 'tree' ? 'active' : ''}`}
                      onClick={() => setBodyViewMode('tree')}
                      title="Tree View"
                    >
                      Tree
                    </button>
                  )}
                </div>
              </div>

              <div className="toolbar-right">
                <div className="toolbar-search">
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="search-input"
                  />
                  <button className="search-btn" title="Search">
                    🔍
                  </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-options">
                  <button
                    className={`option-btn ${wrapLines ? 'active' : ''}`}
                    onClick={() => setWrapLines(!wrapLines)}
                    title="Wrap Lines"
                  >
                    ↩️
                  </button>
                  <button
                    className={`option-btn ${showLineNumbers ? 'active' : ''}`}
                    onClick={() => setShowLineNumbers(!showLineNumbers)}
                    title="Show Line Numbers"
                  >
                    #
                  </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-actions">
                  {requestId && onSaveAsExample && (
                    <button
                      className="toolbar-btn"
                      onClick={saveAsExample}
                      title="Save as Example"
                    >
                      📄
                    </button>
                  )}
                  <button
                    className="toolbar-btn"
                    onClick={() => copyToClipboard(contentInfo.formatted)}
                    title="Copy Response"
                  >
                    📋
                  </button>
                  <button
                    className="toolbar-btn"
                    onClick={downloadResponse}
                    title="Download Response"
                  >
                    💾
                  </button>
                  <button
                    className="toolbar-btn"
                    onClick={() => window.print()}
                    title="Print"
                  >
                    🖨️
                  </button>
                </div>
              </div>
            </div>

            {/* Body Content */}
            <div className="response-body-content">
              {contentInfo.isBinary ? (
                <div className="binary-content-display">
                  <div className="binary-content-info">
                    <h4>Binary Content Detected</h4>
                    <p>Content-Type: {contentInfo.binaryData?.contentType}</p>
                    <p>Size: {formatSize(contentInfo.binaryData?.size || 0)}</p>

                    <div className="binary-actions">
                      <button
                        className="btn btn-primary"
                        onClick={downloadBinaryContent}
                      >
                        📥 Download File
                      </button>
                      {contentInfo.binaryData?.contentType.startsWith('image/') && (
                        <button
                          className="btn btn-secondary"
                          onClick={() => {
                            const blob = new Blob([atob(contentInfo.binaryData!.data)], {
                              type: contentInfo.binaryData!.contentType
                            });
                            const url = URL.createObjectURL(blob);
                            window.open(url, '_blank');
                          }}
                        >
                          👁️ Preview Image
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="binary-preview">
                    <h5>Base64 Preview (first 500 characters):</h5>
                    <div className="monaco-editor-container-wrapper">
                      <MonacoEditor
                        value={contentInfo.binaryData?.data.substring(0, 500) + (contentInfo.binaryData?.data.length > 500 ? '\n\n... (truncated)' : '') || ''}
                        onChange={() => {}}
                        language="plaintext"
                        readOnly={true}
                        label="Base64 Data Preview"
                        theme="vs-dark"
                        showToolbar={false}
                        height="200px"
                      />
                    </div>
                  </div>
                </div>
              ) : bodyViewMode === 'tree' && contentInfo.type === 'json' ? (
                <JsonTreeView data={JSON.parse(contentInfo.formatted)} />
              ) : contentInfo.type === 'json' && bodyViewMode === 'pretty' ? (
                <div className="monaco-editor-container-wrapper">
                  <MonacoEditor
                    value={contentInfo.formatted}
                    onChange={() => {}} // Read-only
                    language="json"
                    readOnly={true}
                    label="Response Body"
                    theme="vs-dark"
                    showToolbar={true}
                  />
                </div>
              ) : (
                <div className="monaco-editor-container-wrapper">
                  <MonacoEditor
                    value={bodyViewMode === 'pretty' ? contentInfo.formatted : (typeof response.responseBody === 'string' ? response.responseBody : JSON.stringify(response.responseBody))}
                    onChange={() => {}} // Read-only
                    language={getMonacoLanguage(contentInfo.type)}
                    readOnly={true}
                    label={`Response Body (${contentInfo.type.toUpperCase()})`}
                    theme="vs-dark"
                    showToolbar={true}
                    wordWrap={wrapLines ? 'on' : 'off'}
                    lineNumbers={showLineNumbers ? 'on' : 'off'}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'headers' && (
          <div className="response-headers">
            <div className="table-wrapper">
              <table className="table">
                <thead>
                  <tr>
                    <th style={{ width: '30%' }}>Key</th>
                    <th style={{ width: '70%' }}>Value</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(response.responseHeaders).map(([key, value]) => (
                    <tr key={key}>
                      <td>{key}</td>
                      <td>{value}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'cookies' && (
          <div className="response-cookies">
            {response.cookies && response.cookies.length > 0 ? (
              <div className="table-wrapper">
                <table className="table">
                  <thead>
                    <tr>
                      <th style={{ width: '15%' }}>Name</th>
                      <th style={{ width: '20%' }}>Value</th>
                      <th style={{ width: '25%' }}>Domain</th>
                      <th style={{ width: '10%' }}>Path</th>
                      <th style={{ width: '15%' }}>Expires</th>
                      <th style={{ width: '8%' }}>HttpOnly</th>
                      <th style={{ width: '7%' }}>Secure</th>
                    </tr>
                  </thead>
                  <tbody>
                    {response.cookies.map((cookie, index) => (
                      <tr key={index}>
                        <td>{cookie.name}</td>
                        <td>{cookie.value}</td>
                        <td>{cookie.domain || '-'}</td>
                        <td>{cookie.path || '-'}</td>
                        <td>{cookie.expires || 'Session'}</td>
                        <td>{cookie.httpOnly ? '✓' : ''}</td>
                        <td>{cookie.secure ? '✓' : ''}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="empty-state">
                <p>No cookies in response</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'test-results' && (
          <div className="test-results">
            {response.testResults && response.testResults.length > 0 ? (
              <>
                <div className="test-summary">
                  {(() => {
                    const passed = response.testResults.filter(t => t.status === 'pass').length;
                    const total = response.testResults.length;
                    const allPassed = passed === total;
                    return (
                      <h4 style={{ color: allPassed ? '#28a745' : '#dc3545' }}>
                        {allPassed ? '✓' : '✗'} {passed}/{total} tests passed
                      </h4>
                    );
                  })()}
                </div>
                <div className="test-list">
                  {response.testResults.map((test, index) => (
                    <div key={index} className={`test-item ${test.status}`}>
                      <span className="test-icon">
                        {test.status === 'pass' ? '✓' : '✗'}
                      </span>
                      <span className="test-name">{test.name}</span>
                      {test.message && (
                        <span className="test-message">{test.message}</span>
                      )}
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="empty-state">
                <p>No test results</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Simple JSON Tree View Component
const JsonTreeView: React.FC<{ data: any; level?: number }> = ({ data, level = 0 }) => {
  const [collapsed, setCollapsed] = useState<Record<string, boolean>>({});

  const toggleCollapse = (key: string) => {
    setCollapsed(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const renderValue = (value: any, key: string, currentLevel: number): React.ReactNode => {
    if (value === null) {
      return <span className="json-null">null</span>;
    }

    if (typeof value === 'boolean') {
      return <span className="json-boolean">{value.toString()}</span>;
    }

    if (typeof value === 'number') {
      return <span className="json-number">{value}</span>;
    }

    if (typeof value === 'string') {
      return <span className="json-string">"{value}"</span>;
    }

    if (Array.isArray(value)) {
      const isCollapsed = collapsed[`${currentLevel}-${key}`];
      return (
        <div className="json-array">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(`${currentLevel}-${key}`)}
          >
            {isCollapsed ? '▶' : '▼'} [{value.length}]
          </span>
          {!isCollapsed && (
            <div className="json-children">
              {value.map((item, index) => (
                <div key={index} className="json-item">
                  <span className="json-key">{index}:</span>
                  {renderValue(item, `${key}-${index}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    if (typeof value === 'object') {
      const keys = Object.keys(value);
      const isCollapsed = collapsed[`${currentLevel}-${key}`];
      return (
        <div className="json-object">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(`${currentLevel}-${key}`)}
          >
            {isCollapsed ? '▶' : '▼'} {`{${keys.length}}`}
          </span>
          {!isCollapsed && (
            <div className="json-children">
              {keys.map(objKey => (
                <div key={objKey} className="json-item">
                  <span className="json-key">"{objKey}":</span>
                  {renderValue(value[objKey], `${key}-${objKey}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    return <span>{String(value)}</span>;
  };

  return (
    <div className="json-tree-view">
      {renderValue(data, 'root', level)}
    </div>
  );
};

export default ResponseViewer;
