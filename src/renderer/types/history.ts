/**
 * History-related type definitions
 */

export interface HistoryData {
  id?: string | number;
  entityId?: number;
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: string;
  response?: HistoryResponse;
  executedAt: Date;
  workspaceId: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface HistoryResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  size: number;
  time: number;
}

export interface HistoryEntry {
  id: string;
  requestId?: string;
  requestName: string;
  method: string;
  url: string;
  status: number;
  responseTime: number;
  timestamp: Date;
  workspaceId: number;
}

export interface HistoryFilter {
  method?: string;
  status?: number;
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

export interface HistoryStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  mostUsedMethods: Array<{
    method: string;
    count: number;
  }>;
}
