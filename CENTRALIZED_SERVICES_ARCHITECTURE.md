# Centralized Services Architecture

This document describes the new centralized service architecture implemented for ApiCool, which eliminates the need for tab-to-request conversion functions and provides a unified approach to CRUD operations for all item types.

## Overview

The new architecture introduces:
- **Service-based CRUD operations** for all item types (requests, collections, folders, environments, examples, history)
- **Unified TabItem structure** that can hold any type of data
- **Centralized tab management** through `TabManagementService`
- **No more conversion functions** between Request and TabItem

## Architecture Components

### 1. Base Service Interface (`BaseItemService.ts`)

All item services implement the `BaseItemService<T>` interface:

```typescript
interface BaseItemService<T> {
  create(data: Partial<T>, workspaceId: number): Promise<T>;
  update(id: string | number, data: Partial<T>): Promise<T>;
  get(id: string | number): Promise<T | null>;
  delete(id: string | number): Promise<boolean>;
  isNew(data: any): boolean;
  getDisplayName(data: any): string;
  getDefaultData(): Partial<T>;
}
```

### 2. Individual Item Services

Each item type has its own service:

- **RequestService** - Handles request CRUD operations
- **CollectionService** - Handles collection CRUD operations  
- **FolderService** - Handles folder CRUD operations
- **EnvironmentService** - Handles environment CRUD operations
- **ExampleService** - Handles example CRUD operations
- **HistoryService** - Handles history entry operations

### 3. Service Registry (`ItemServiceRegistry.ts`)

Centralized registry that:
- Registers all services by type
- Provides utility functions for working with TabItems
- Handles service initialization

### 4. Tab Management Service (`TabManagementService.ts`)

Centralized service for all tab operations:
- Save/delete/load tab items using appropriate services
- Check if items are new or have unsaved changes
- Get display names, icons, method badges
- Handle tab item data updates

## Key Benefits

### 1. No More Conversion Functions

**Before:**
```typescript
// Had to convert between Request and TabItem
const tabItem = requestToTabItem(request);
const request = tabItemToRequest(tabItem);
```

**After:**
```typescript
// Work directly with TabItem data
const result = await TabManagementService.saveTabItem(tabItem, workspaceId);
```

### 2. Unified Save Behavior

**Requests:** Show save dialog for new requests (need location selection)
**Collections/Folders/Environments/Examples:** Save directly to current workspace (no dialog needed)

```typescript
// Service automatically determines if dialog is needed
const result = await TabManagementService.saveTabItem(tabItem, workspaceId);
if (result.needsDialog) {
  // Show save dialog for requests
} else {
  // Direct save completed
}
```

### 3. Type-Safe Operations

Each service handles its specific data type with proper TypeScript interfaces:

```typescript
interface RequestData extends BaseItemData {
  method: string;
  url: string;
  headers?: Record<string, string>;
  // ...
}

interface CollectionData extends BaseItemData {
  description?: string;
  variables?: Record<string, string>;
  // ...
}
```

## Usage Examples

### Creating a New Tab Item

```typescript
// Create new request tab
const requestTab = TabManagementService.createNewTabItem('request', {
  name: 'My Request',
  method: 'GET',
  url: 'https://api.example.com'
});

// Create new collection tab  
const collectionTab = TabManagementService.createNewTabItem('collection', {
  name: 'My Collection',
  description: 'API collection'
});
```

### Saving Tab Items

```typescript
// Save any type of tab item
const result = await TabManagementService.saveTabItem(tabItem, workspaceId);

if (result.success) {
  // Update tab with saved data
  const updatedTab = TabManagementService.markTabItemAsSaved(tabItem, result.data);
} else if (result.needsDialog) {
  // Show save dialog for requests
  showSaveDialog(tabItem);
}
```

### Working with Tab Data

```typescript
// Get display information
const displayName = TabManagementService.getTabDisplayName(tabItem);
const icon = TabManagementService.getTabIcon(tabItem);
const methodBadge = TabManagementService.getMethodBadge(tabItem);

// Check status
const isNew = TabManagementService.isNewTabItem(tabItem);
const hasChanges = TabManagementService.hasUnsavedChanges(tabItem);

// Update data
const updatedTab = TabManagementService.updateTabItemData(tabItem, {
  name: 'New Name'
});
```

## Migration from Old System

### Before (App.tsx)
```typescript
// Had conversion functions and complex logic
const requestForDialog = tabItemToRequest(request);
setRequestToSave(requestForDialog);
```

### After (App.tsx)
```typescript
// Direct service usage
const requestForDialog = TabManagementService.tabItemToRequest(request);
setRequestToSave(requestForDialog);
```

### Before (ItemTabs.tsx)
```typescript
// Custom helper functions for each operation
const getTabDisplayName = (tab) => { /* complex switch logic */ };
const hasUnsavedChanges = (tab) => { /* complex logic */ };
```

### After (ItemTabs.tsx)
```typescript
// Simple service calls
const displayName = TabManagementService.getTabDisplayName(tab);
const hasChanges = TabManagementService.hasUnsavedChanges(tab);
```

## Service Initialization

Services are automatically initialized when the app starts:

```typescript
// In AppContext
useEffect(() => {
  initializeServices(); // Registers all services
}, []);
```

## Future Extensibility

Adding new item types is now straightforward:

1. Create new service implementing `BaseItemService<T>`
2. Register it in `ItemServiceRegistry`
3. Add type to TabItem union type
4. Service automatically handles all CRUD operations

This architecture provides a clean, maintainable, and extensible foundation for managing all types of items in ApiCool.
