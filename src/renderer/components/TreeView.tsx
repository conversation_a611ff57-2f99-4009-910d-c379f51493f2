import React, { useState, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { WorkspaceItem, getItemIcon, getMethodColor } from '../types/workspace';

interface TreeViewProps {
  items: WorkspaceItem[];
  allItems?: WorkspaceItem[]; // All items for recursive lookups
  onItemClick: (item: WorkspaceItem) => void;
  onItemToggle: (itemId: string) => void;
  onItemRename: (itemId: string, newName: string) => void;
  onItemDelete: (itemId: string) => void;
  onItemMove: (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => void;
  onAddNew: (parentId?: string, type?: 'folder' | 'request') => void;
  selectedItemId?: string;
  level?: number;
}

interface TreeItemProps {
  item: WorkspaceItem;
  children?: WorkspaceItem[];
  allItems?: WorkspaceItem[];
  onItemClick: (item: WorkspaceItem) => void;
  onItemToggle: (itemId: string) => void;
  onItemRename: (itemId: string, newName: string) => void;
  onItemDelete: (itemId: string) => void;
  onItemMove: (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => void;
  onAddNew: (parentId?: string, type?: 'folder' | 'request') => void;
  selectedItemId?: string;
  level: number;
}

const TreeItem: React.FC<TreeItemProps> = ({
  item,
  children = [],
  allItems,
  onItemClick,
  onItemToggle,
  onItemRename,
  onItemDelete,
  onItemMove,
  onAddNew,
  selectedItemId,
  level
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(item.name);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPos, setContextMenuPos] = useState({ x: 0, y: 0 });
  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'tree-item',
    item: { id: item.id, type: item.type, name: item.name, parentId: item.parentId },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: 'tree-item',
    drop: (draggedItem: any, monitor) => {
      if (draggedItem.id === item.id) return;
      
      const didDrop = monitor.didDrop();
      if (didDrop) return;

      // Determine drop position based on mouse position
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      if (!hoverBoundingRect) return;

      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      let position: 'before' | 'after' | 'inside' = 'after';
      
      if (item.type === 'collection' || item.type === 'folder') {
        if (hoverClientY < hoverMiddleY / 2) {
          position = 'before';
        } else if (hoverClientY > hoverMiddleY * 1.5) {
          position = 'after';
        } else {
          position = 'inside';
        }
      } else {
        position = hoverClientY < hoverMiddleY ? 'before' : 'after';
      }

      onItemMove(draggedItem.id, item.id, position);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  drag(drop(ref));

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      onItemClick(item);
    }
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (item.type === 'collection' || item.type === 'folder' || item.type === 'request') {
      onItemToggle(item.id);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenuPos({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  };

  const handleRename = () => {
    setIsEditing(true);
    setShowContextMenu(false);
  };

  const handleRenameSubmit = () => {
    if (editName.trim() && editName !== item.name) {
      onItemRename(item.id, editName.trim());
    }
    setIsEditing(false);
  };

  const handleRenameCancel = () => {
    setEditName(item.name);
    setIsEditing(false);
  };

  const handleDelete = () => {
    onItemDelete(item.id);
    setShowContextMenu(false);
  };

  const handleAddFolder = () => {
    onAddNew(item.id, 'folder');
    setShowContextMenu(false);
  };

  const handleAddRequest = () => {
    onAddNew(item.id, 'request');
    setShowContextMenu(false);
  };

  const isSelected = selectedItemId === item.id;
  const hasChildren = children.length > 0;
  const isExpanded = item.expanded && hasChildren;

  return (
    <>
      <div
        ref={ref}
        className={`tree-item ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''} ${isOver && canDrop ? 'drop-target' : ''}`}
        style={{ 
          paddingLeft: `${level * 20 + 8}px`,
          opacity: isDragging ? 0.5 : 1 
        }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        <div className="tree-item-content">
          {(item.type === 'collection' || item.type === 'folder' || item.type === 'request') && (
            <button
              className={`tree-toggle ${hasChildren ? '' : 'empty'}`}
              onClick={handleToggle}
            >
              {hasChildren ? (isExpanded ? '▼' : '▶') : ''}
            </button>
          )}
          
          <span className="tree-icon">{getItemIcon(item)}</span>
          
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleRenameSubmit}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleRenameSubmit();
                if (e.key === 'Escape') handleRenameCancel();
              }}
              autoFocus
              className="tree-edit-input"
            />
          ) : (
            <span className="tree-name">
              {item.name}
              {item.type === 'request' && item.hasUnsavedChanges && (
                <span className="tree-unsaved-indicator" title="Unsaved changes">*</span>
              )}
            </span>
          )}
          
          {item.type === 'request' && item.method && (
            <span
              className="tree-method"
              style={{ backgroundColor: getMethodColor(item.method) }}
            >
              {item.method}
            </span>
          )}

          {item.type === 'example' && (
            <span className="tree-example-badge">
              Example
            </span>
          )}
        </div>
      </div>

      {isExpanded && children.length > 0 && (
        <div className="tree-children">
          {children.map(child => {
            const childChildren = (allItems || []).filter((c: WorkspaceItem) => c.parentId === child.id);
            return (
              <TreeItem
                key={child.id}
                item={child}
                children={childChildren}
                allItems={allItems}
                onItemClick={onItemClick}
                onItemToggle={onItemToggle}
                onItemRename={onItemRename}
                onItemDelete={onItemDelete}
                onItemMove={onItemMove}
                onAddNew={onAddNew}
                selectedItemId={selectedItemId}
                level={level + 1}
              />
            );
          })}
        </div>
      )}

      {showContextMenu && (
        <>
          <div 
            className="context-menu-overlay" 
            onClick={() => setShowContextMenu(false)}
          />
          <div 
            className="context-menu"
            style={{ left: contextMenuPos.x, top: contextMenuPos.y }}
          >
            <button onClick={handleRename}>Rename</button>
            {(item.type === 'collection' || item.type === 'folder') && (
              <>
                <button onClick={handleAddFolder}>Add Folder</button>
                <button onClick={handleAddRequest}>Add Request</button>
              </>
            )}
            <button onClick={handleDelete} className="danger">Delete</button>
          </div>
        </>
      )}
    </>
  );
};

const TreeView: React.FC<TreeViewProps> = ({
  items,
  allItems,
  onItemClick,
  onItemToggle,
  onItemRename,
  onItemDelete,
  onItemMove,
  onAddNew,
  selectedItemId,
  level = 0
}) => {
  // Build tree structure
  const rootItems = items.filter(item => !item.parentId).sort((a, b) => a.order - b.order);

  const getChildren = (parentId: string): WorkspaceItem[] => {
    return (allItems || items).filter(item => item.parentId === parentId).sort((a, b) => a.order - b.order);
  };

  return (
    <div className="tree-view">
      {rootItems.map(item => (
        <TreeItem
          key={item.id}
          item={item}
          children={getChildren(item.id)}
          allItems={allItems || items}
          onItemClick={onItemClick}
          onItemToggle={onItemToggle}
          onItemRename={onItemRename}
          onItemDelete={onItemDelete}
          onItemMove={onItemMove}
          onAddNew={onAddNew}
          selectedItemId={selectedItemId}
          level={level}
        />
      ))}
    </div>
  );
};

export default TreeView;
