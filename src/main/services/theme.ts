import { nativeTheme } from 'electron';

export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  mode: ThemeMode;
  customColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    surface?: string;
    text?: string;
  };
}

export class ThemeService {
  private currentTheme: ThemeConfig;

  constructor() {
    this.currentTheme = {
      mode: 'auto'
    };

    // Listen for system theme changes
    nativeTheme.on('updated', () => {
      if (this.currentTheme.mode === 'auto') {
        this.notifyThemeChange();
      }
    });
  }

  async getCurrentTheme(): Promise<ThemeConfig> {
    return { ...this.currentTheme };
  }

  async setTheme(theme: ThemeConfig): Promise<void> {
    this.currentTheme = { ...theme };
    
    // Update Electron's native theme
    if (theme.mode === 'auto') {
      nativeTheme.themeSource = 'system';
    } else {
      nativeTheme.themeSource = theme.mode;
    }

    this.notifyThemeChange();
  }

  getSystemTheme(): 'light' | 'dark' {
    return nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
  }

  getEffectiveTheme(): 'light' | 'dark' {
    if (this.currentTheme.mode === 'auto') {
      return this.getSystemTheme();
    }
    return this.currentTheme.mode;
  }

  async getAvailableThemes(): Promise<Array<{ id: string; name: string; mode: ThemeMode }>> {
    return [
      { id: 'light', name: 'Light', mode: 'light' },
      { id: 'dark', name: 'Dark', mode: 'dark' },
      { id: 'auto', name: 'Auto (System)', mode: 'auto' }
    ];
  }

  async saveThemePreference(): Promise<void> {
    // TODO: Save to user preferences/database
    console.log('Saving theme preference:', this.currentTheme);
  }

  async loadThemePreference(): Promise<void> {
    // TODO: Load from user preferences/database
    console.log('Loading theme preference');
  }

  private notifyThemeChange(): void {
    // TODO: Notify renderer process about theme change
    console.log('Theme changed:', this.getEffectiveTheme());
  }

  // CSS Variables for themes
  getLightThemeVariables(): Record<string, string> {
    return {
      '--color-primary': '#007acc',
      '--color-secondary': '#6c757d',
      '--color-accent': '#28a745',
      '--color-background': '#ffffff',
      '--color-surface': '#f8f9fa',
      '--color-text': '#212529',
      '--color-text-secondary': '#6c757d',
      '--color-border': '#dee2e6',
      '--color-error': '#dc3545',
      '--color-warning': '#ffc107',
      '--color-success': '#28a745',
      '--color-info': '#17a2b8'
    };
  }

  getDarkThemeVariables(): Record<string, string> {
    return {
      '--color-primary': '#0d7377',
      '--color-secondary': '#6c757d',
      '--color-accent': '#20c997',
      '--color-background': '#1a1a1a',
      '--color-surface': '#2d2d2d',
      '--color-text': '#ffffff',
      '--color-text-secondary': '#adb5bd',
      '--color-border': '#495057',
      '--color-error': '#e74c3c',
      '--color-warning': '#f39c12',
      '--color-success': '#27ae60',
      '--color-info': '#3498db'
    };
  }

  getThemeVariables(): Record<string, string> {
    const effectiveTheme = this.getEffectiveTheme();
    const baseVariables = effectiveTheme === 'dark' 
      ? this.getDarkThemeVariables() 
      : this.getLightThemeVariables();

    // Apply custom colors if any
    if (this.currentTheme.customColors) {
      Object.entries(this.currentTheme.customColors).forEach(([key, value]) => {
        if (value) {
          baseVariables[`--color-${key}`] = value;
        }
      });
    }

    return baseVariables;
  }

  generateThemeCSS(): string {
    const variables = this.getThemeVariables();
    const cssVariables = Object.entries(variables)
      .map(([key, value]) => `  ${key}: ${value};`)
      .join('\n');

    return `:root {\n${cssVariables}\n}`;
  }
}
