/* Additional CSS Variables for components */
:root {
  --color-primary-dark: #005a9e;
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-background);
  overflow: hidden;
}

/* App Layout */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.app-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Header */
.app-header {
  height: 60px;
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  flex-shrink: 0;
}

.app-title {
  color: var(--color-primary);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.workspace-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.workspace-label {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.workspace-select {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  min-width: 150px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.header-action-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.header-action-btn:hover {
  background: var(--color-background-hover);
  color: var(--color-text);
}

.header-action-btn:active {
  transform: scale(0.95);
}

.header-action-btn svg {
  width: 20px;
  height: 20px;
}

/* Sidebar (Legacy - now using WorkspacePanel) */
.sidebar {
  width: 360px;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  flex-shrink: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.sidebar-content {
  flex: 1;
  padding: 8px 0;
}

.collection-item {
  margin: 0 8px;
}

.collection-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

.collection-icon {
  font-size: 16px;
  flex-shrink: 0;
  color: var(--color-text-secondary);
}

.collection-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text);
}

.collection-requests {
  margin-left: 16px;
}

.request-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: var(--border-radius);
  margin: 2px 0;
  transition: background-color 0.2s;
}

.request-item:hover {
  background-color: var(--color-background);
}

.request-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.request-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.request-method {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  flex-shrink: 0;
}

/* Main Panel */
.main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  gap: var(--spacing-lg);
}

.empty-state h2 {
  color: var(--color-text);
  margin: 0;
}

.empty-state p {
  color: var(--color-text-secondary);
  margin: 0;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.request-section {
  min-height: 200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.response-section {
  min-height: 200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Request Tabs */
.request-tabs-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  height: 40px;
  padding: 0 16px;
}

.request-tabs-left {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.request-tabs {
  display: flex;
  align-items: center;
  overflow: hidden;
  flex: 1;
}

.request-tabs-right {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 16px;
}

.request-tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  width: 200px;
  min-width: 200px;
  max-width: 200px;
  border-right: 1px solid var(--color-border);
  flex-shrink: 0;
  position: relative;
}

.request-tab:hover {
  color: var(--color-text);
  background: var(--color-background);
}

.request-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-background);
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.tab-method {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  flex-shrink: 0;
}

.tab-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  flex: 1;
}

.tab-close-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-left: 8px;
}

.tab-close {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  font-size: 14px;
  font-weight: bold;
  color: var(--color-text-secondary);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-close:hover {
  background: rgba(255, 0, 0, 0.1);
  color: var(--color-error);
}

.tab-unsaved-dot {
  color: var(--color-warning);
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.request-tab.unsaved .tab-name {
  font-style: italic;
}

.request-tab.active .tab-close {
  color: rgba(255, 255, 255, 0.8);
}

.request-tab.active .tab-close:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.request-tab.active .tab-unsaved-dot {
  color: rgba(255, 255, 255, 0.9);
}

.new-tab-btn {
  padding: 8px 12px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s;
  flex-shrink: 0;
  min-width: 40px;
}

.new-tab-btn:hover {
  color: var(--color-primary);
  background: var(--color-background);
}

/* Tab Overflow */
.tab-overflow {
  position: relative;
  display: flex;
  align-items: center;
}

.tab-overflow-btn {
  padding: 8px 12px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.tab-overflow-btn:hover {
  color: var(--color-text);
  background: var(--color-background);
}

.tab-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 250px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px 0;
}

.tab-dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid var(--color-border);
}

.tab-dropdown-item:last-child {
  border-bottom: none;
}

.tab-dropdown-item:hover {
  background: var(--color-surface);
}

.tab-dropdown-item.active {
  background: var(--color-primary);
  color: white;
}

.tab-dropdown-item.active:hover {
  background: var(--color-primary-dark);
}

.dropdown-tab-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.dropdown-tab-method {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  flex-shrink: 0;
}

.dropdown-tab-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.dropdown-tab-close {
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 14px;
  opacity: 0.6;
  color: var(--color-text-secondary);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.dropdown-tab-close:hover {
  opacity: 1;
  background: rgba(255, 0, 0, 0.1);
  color: var(--color-error);
}

.tab-dropdown-item.active .dropdown-tab-close {
  color: rgba(255, 255, 255, 0.8);
}

.tab-dropdown-item.active .dropdown-tab-close:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

/* Tab Dropdown Scrollbar */
.tab-dropdown::-webkit-scrollbar {
  width: 6px;
}

.tab-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

.tab-dropdown::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.tab-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Save Dialog */
.save-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
}

.save-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  z-index: 2001;
  min-width: 400px;
  max-width: 500px;
}

.save-dialog-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
}

.save-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.save-dialog-content {
  padding: 20px;
}

.save-dialog-content p {
  margin: 0;
  font-size: 14px;
  color: var(--color-text);
  line-height: 1.5;
}

.save-dialog-actions {
  padding: 16px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-dialog-actions .btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.save-dialog-actions .btn-primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.save-dialog-actions .btn-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.save-dialog-actions .btn-secondary {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.save-dialog-actions .btn-secondary:hover {
  background: #e0a800;
  border-color: #e0a800;
}

.save-dialog-actions .btn-outline {
  background: transparent;
  color: var(--color-text);
  border-color: var(--color-border);
}

.save-dialog-actions .btn-outline:hover {
  background: var(--color-surface);
  border-color: var(--color-text-secondary);
}

/* Dropdown unsaved indicator */
.dropdown-unsaved-indicator {
  color: var(--color-warning);
  font-size: 10px;
  margin-left: 4px;
  flex-shrink: 0;
}

/* Request Editor */
.request-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.request-url-bar {
  padding: 16px;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.url-input-group {
  display: flex;
  gap: 8px;
}

.method-select {
  width: 100px;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  font-weight: 600;
}

.method-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.url-input,
.name-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.url-input:focus,
.name-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.url-input::placeholder,
.name-input::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.send-btn {
  padding: 8px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.send-btn:hover {
  background: var(--color-primary-dark);
}

/* Content Tabs */
.content-tabs {
  display: flex;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.content-tab {
  padding: 12px 16px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.content-tab:hover {
  color: var(--color-text);
  background: var(--color-background);
}

.content-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-background);
}

.tab-content-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Tables */
.params-table,
.headers-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: var(--color-background);
  table-layout: fixed;
}

.table th,
.table td {
  padding: 8px 12px;
  text-align: left;
  border-right: 1px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.table th:last-child,
.table td:last-child {
  border-right: none;
}

.table th {
  background: var(--color-surface);
  font-weight: 600;
  color: var(--color-text);
  position: sticky;
  top: 0;
  z-index: 1;
  white-space: nowrap;
  font-size: 13px;
}

.table td {
  background: var(--color-background);
  height: 40px;
}

.table tr:hover td {
  background: var(--color-surface);
}

.table tr.empty-row {
  opacity: 0.6;
}

.table tr.empty-row:hover {
  opacity: 1;
}

.table input[type="text"] {
  width: 100%;
  border: 1px solid transparent;
  background: var(--color-background);
  padding: 6px 8px;
  font-size: 13px;
  color: var(--color-text);
  outline: none;
  border-radius: 2px;
}

.table input[type="text"]:focus {
  background: var(--color-background);
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.table input[type="text"]::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.table input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.checkbox-col {
  width: 50px;
  text-align: center;
  padding: 8px;
}

.actions-col {
  width: 50px;
  text-align: center;
  padding: 8px;
}

.key-col {
  width: 30%;
  min-width: 120px;
}

.value-col {
  width: 30%;
  min-width: 120px;
}

.description-col {
  width: 35%;
  min-width: 150px;
}

.delete-btn {
  background: none;
  border: 1px solid transparent;
  color: var(--color-danger);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.delete-btn:hover:not(:disabled) {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.delete-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.delete-btn svg {
  width: 16px;
  height: 16px;
}

.add-row-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  align-self: flex-start;
  gap: 6px;
}

.add-row-btn:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.add-row-btn svg {
  width: 16px;
  height: 16px;
}

.add-row-btn:active {
  transform: translateY(1px);
}

/* Body Editor */
.body-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.body-type-selector {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.body-type-selector label {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  color: var(--color-text);
}

.body-textarea,
.headers-textarea {
  flex: 1;
  padding: 16px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: none;
  outline: none;
  border-radius: 4px;
}

.body-textarea:focus,
.headers-textarea:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.body-textarea::placeholder,
.headers-textarea::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

/* Auth Editor */
.auth-editor {
  padding: 16px;
  background: var(--color-background);
  color: var(--color-text);
}

.auth-type-selector {
  margin-bottom: 16px;
}

.auth-type-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text);
}

.auth-type-selector select {
  width: 200px;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
}

.auth-type-selector select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

/* Script Editor */
.script-editor {
  width: 100%;
  height: 100%;
  padding: 16px;
  border: none;
  background: var(--color-background);
  color: var(--color-text);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: none;
  outline: none;
}

/* Response Viewer */
.response-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.response-loading,
.response-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.response-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  height: 40px;
  min-height: 40px;
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  color: white;
}

.time-badge,
.size-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.response-tabs {
  display: flex;
  background: none;
  border: none;
  height: 40px;
  align-items: center;
}

.response-tab {
  padding: 12px 16px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.response-tab:hover {
  color: var(--color-text);
  background: var(--color-background);
}

.response-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-background);
}

.response-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.response-body-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.response-body-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  gap: 16px;
  min-height: 44px;
  flex-wrap: wrap;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  gap: 12px;
}

.content-type-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.content-size {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.view-mode-buttons {
  display: flex;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.view-mode-btn {
  padding: 4px 12px;
  border: none;
  background: transparent;
  color: var(--color-text);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-mode-btn:hover {
  background: var(--color-surface);
}

.view-mode-btn.active {
  background: var(--color-primary);
  color: white;
}

.toolbar-btn {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

/* Enhanced Toolbar Components */
.toolbar-search {
  display: flex;
  align-items: center;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: transparent;
  padding: 6px 8px;
  font-size: 12px;
  color: var(--color-text);
  outline: none;
  width: 120px;
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.search-btn {
  border: none;
  background: transparent;
  padding: 6px 8px;
  cursor: pointer;
  color: var(--color-text-secondary);
  font-size: 12px;
  transition: color 0.2s;
}

.search-btn:hover {
  color: var(--color-primary);
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: var(--color-border);
  margin: 0 4px;
}

.toolbar-options,
.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.option-btn {
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text-secondary);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
  color: var(--color-text);
}

.option-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.toolbar-btn {
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text-secondary);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.response-body-content {
  flex: 1;
  overflow: hidden; /* Changed from auto to hidden for Monaco Editor */
  padding: 16px;
  background: var(--color-background);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.response-body-wrapper {
  position: relative;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  background: var(--color-background);
  border-radius: 4px;
  overflow: auto;
}

.response-body-wrapper.show-line-numbers {
  counter-reset: line-number;
}

.response-body-wrapper.show-line-numbers .response-body {
  padding-left: 50px;
}

.response-body-wrapper.show-line-numbers .response-body::before {
  content: counter(line-number);
  counter-increment: line-number;
  position: absolute;
  left: 0;
  width: 40px;
  text-align: right;
  color: var(--color-text-secondary);
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  padding-right: 8px;
  user-select: none;
}

.response-body-wrapper.wrap-lines .response-body {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.response-body {
  margin: 0;
  padding: 0;
  background: transparent;
  color: var(--color-text);
  border: none;
  outline: none;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  white-space: pre;
  overflow-wrap: normal;
  min-height: 100%;
}

/* Search highlighting */
.response-body mark {
  background: #ffeb3b;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: bold;
}

/* Quick Search Box */
.quick-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.quick-search-box {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.quick-search-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
  gap: 12px;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-icon {
  font-size: 18px;
  color: var(--color-text-secondary);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  color: var(--color-text);
  font-family: inherit;
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.close-btn {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: var(--color-surface);
  color: var(--color-text);
}

.quick-search-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 12px;
}

.search-result-item:hover,
.search-result-item.selected {
  background: var(--color-surface);
}

.search-result-item.selected {
  background: var(--color-primary);
  color: white;
}

.search-result-item.selected .item-description,
.search-result-item.selected .item-type {
  color: rgba(255, 255, 255, 0.8);
}

.item-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-description {
  font-size: 12px;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-type {
  font-size: 11px;
  color: var(--color-text-secondary);
  background: var(--color-surface);
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  font-weight: 600;
}

.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.search-empty p {
  margin: 0 0 8px 0;
}

.search-hint {
  font-size: 12px;
}

.search-hint code {
  background: var(--color-surface);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.quick-search-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

.search-shortcuts {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.shortcut {
  font-size: 11px;
  color: var(--color-text-secondary);
  background: var(--color-background);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
}

/* Editor Components */
.folder-editor,
.collection-editor,
.example-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.editor-icon {
  font-size: 20px;
}

.editor-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.version-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.unsaved-indicator {
  color: var(--color-warning);
  font-size: 16px;
  margin-left: 4px;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.editor-tabs {
  display: flex;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  overflow-x: auto;
}

.tab {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  font-size: 14px;
}

.tab:hover {
  background: var(--color-background);
  color: var(--color-text);
}

.tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-background);
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  color: var(--color-text);
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row .form-group {
  margin-bottom: 0;
}

.flex-1 {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--color-text);
  font-size: 14px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
  line-height: 1.5;
}

.script-editor,
.notes-editor,
.docs-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.btn {
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:hover:not(:disabled) {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--color-surface);
  color: var(--color-text);
}

.btn-danger {
  background: var(--color-error);
  color: white;
  border-color: var(--color-error);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* Variables Section */
.variables-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.variables-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-item,
.header-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.variable-key,
.header-key {
  flex: 1;
  min-width: 150px;
}

.variable-value,
.header-value {
  flex: 2;
}

/* Tags Section */
.tags-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: var(--color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0;
  font-size: 10px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.tag-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-input {
  max-width: 200px;
}

/* Script Section */
.script-section {
  margin-bottom: 24px;
}

.script-section h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.script-description {
  margin: 0 0 12px 0;
  color: var(--color-text-secondary);
  font-size: 13px;
}

/* Headers Section */
.headers-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  color: var(--color-text);
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.headers-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Body Section */
.body-section {
  margin-bottom: 24px;
}

.body-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

/* Example Summary */
.example-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
  background: var(--color-surface);
  border-radius: 8px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-item label {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  margin: 0;
}

.method-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.method-get { background: #28a745; color: white; }
.method-post { background: #ffc107; color: #212529; }
.method-put { background: #17a2b8; color: white; }
.method-patch { background: #6f42c1; color: white; }
.method-delete { background: #dc3545; color: white; }

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  width: fit-content;
}

.status-200,
.status-2xx { background: #28a745; color: white; }
.status-3xx { background: #17a2b8; color: white; }
.status-4xx { background: #ffc107; color: #212529; }
.status-5xx { background: #dc3545; color: white; }

.url-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--color-text);
  word-break: break-all;
}

.time-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--color-text);
}

/* Import/Export Dialog */
.import-export-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.import-export-dialog {
  background: var(--color-background);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.dialog-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: var(--color-background);
  color: var(--color-text);
}

.dialog-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

/* Drop Zone */
.drop-zone {
  border: 2px dashed var(--color-border);
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 24px;
}

.drop-zone:hover,
.drop-zone.dragging {
  border-color: var(--color-primary);
  background: rgba(102, 126, 234, 0.05);
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.drop-icon {
  font-size: 48px;
  opacity: 0.6;
}

.drop-zone h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.drop-zone p {
  margin: 0;
  color: var(--color-text-secondary);
}

.supported-formats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 8px;
}

.format-badge {
  background: var(--color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* GitHub Import */
.github-import {
  margin-bottom: 24px;
}

.github-import h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.github-input-group {
  display: flex;
  gap: 8px;
}

.github-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.github-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* Import Results */
.import-results {
  border-top: 1px solid var(--color-border);
  padding-top: 20px;
}

.import-results h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.result-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.result-item.success {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.result-item.error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.result-status {
  font-size: 16px;
}

.result-details {
  flex: 1;
}

.collection-name {
  font-size: 13px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

.error-message {
  font-size: 13px;
  color: var(--color-error);
  margin-top: 4px;
}

/* Export Options */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  font-weight: 600;
  color: var(--color-text);
  font-size: 14px;
}

.format-select {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.format-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.checkbox-label:hover {
  background: var(--color-surface);
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.collection-info {
  background: var(--color-surface);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.collection-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.collection-info p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* Processing Overlay */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-overlay p {
  margin: 0;
  color: var(--color-text);
  font-weight: 500;
}

/* Workspace Management */
.workspace-switcher {
  position: relative;
  min-width: 180px;
  max-width: 220px;
  flex-shrink: 0;
}

.workspace-switcher-button {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
  padding: 2px 6px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 3px;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  height: 24px;
}

.workspace-switcher-button:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.workspace-icon {
  font-size: 14px;
}

.workspace-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.workspace-arrow {
  font-size: 10px;
  color: var(--color-text-secondary);
  transition: transform 0.2s;
}

.workspace-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 280px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
  margin-top: 4px;
  overflow: hidden;
}

.workspace-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1999;
}

.workspace-dropdown-header {
  padding: 12px 16px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-weight: 600;
  font-size: 13px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
}

.workspace-list {
  max-height: 200px;
  overflow-y: auto;
}

.workspace-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid var(--color-border);
}

.workspace-item:last-child {
  border-bottom: none;
}

.workspace-item:hover {
  background: var(--color-surface);
}

.workspace-item.active {
  background: rgba(102, 126, 234, 0.1);
  color: var(--color-primary);
}

.workspace-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.workspace-item-name {
  font-weight: 500;
  font-size: 14px;
}

.workspace-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.workspace-item-desc {
  font-size: 12px;
  color: var(--color-text-secondary);
  line-height: 1.3;
}

.workspace-dropdown-actions {
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

.workspace-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
}

.workspace-action-btn:last-child {
  border-bottom: none;
}

.workspace-action-btn:hover {
  background: var(--color-background);
}

.action-icon {
  font-size: 14px;
}



/* Workspace Dialogs */
.workspace-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.workspace-dialog {
  background: var(--color-background);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
}

.workspace-management-dialog {
  background: var(--color-background);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
}

.workspace-dialog .dialog-header,
.workspace-management-dialog .dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.workspace-dialog .dialog-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.workspace-dialog .dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

.form-help {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.general-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  color: var(--color-error);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--color-error);
}

.error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-top: 4px;
}

.response-headers,
.response-cookies {
  padding: 16px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-text);
}

.response-headers .table th {
  color: var(--color-text);
  background: var(--color-surface);
}

.response-headers .table td {
  color: var(--color-text);
  background: var(--color-background);
}

.response-headers .table tr:hover td {
  background: var(--color-surface);
}

/* Test Results */
.test-results {
  padding: 16px;
  color: var(--color-text);
}

.test-summary {
  margin-bottom: 16px;
}

.test-summary h4 {
  margin-bottom: 8px;
  color: var(--color-text);
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: var(--color-surface);
  border-radius: var(--border-radius);
  color: var(--color-text);
}

.test-item.passed .test-icon {
  color: var(--color-success);
}

.test-item.failed .test-icon {
  color: var(--color-error);
}

.test-name {
  flex: 1;
}

.test-time {
  margin-left: auto;
  color: var(--color-text-secondary);
  font-size: 12px;
}

.test-message {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-left: auto;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
}

/* JSON Tree View */
.json-tree-view {
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: var(--color-background);
  color: var(--color-text);
}

.json-toggle {
  cursor: pointer;
  user-select: none;
  color: var(--color-text-secondary);
  margin-right: 8px;
}

.json-toggle:hover {
  color: var(--color-primary);
}

.json-children {
  margin-left: 20px;
  border-left: 1px solid var(--color-border);
  padding-left: 12px;
}

.json-item {
  margin: 4px 0;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.json-key {
  color: #0066cc;
  font-weight: 600;
}

.json-string {
  color: #22863a;
}

.json-number {
  color: #005cc5;
}

.json-boolean {
  color: #d73a49;
}

.json-null {
  color: #6f42c1;
  font-style: italic;
}

.json-object,
.json-array {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Simple JSON Editor */
.simple-json-editor {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
  background: var(--color-background);
}

.json-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-size: 12px;
}

.json-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.json-valid {
  color: var(--color-success);
  font-weight: 500;
}

.json-error {
  color: var(--color-error);
  font-weight: 500;
}

.json-actions {
  display: flex;
  gap: 8px;
}

.json-format-btn,
.json-minify-btn {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.json-format-btn:hover:not(:disabled),
.json-minify-btn:hover:not(:disabled) {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.json-format-btn:disabled,
.json-minify-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.json-editor-content {
  position: relative;
}

.json-textarea {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.json-textarea.invalid {
  background: rgba(220, 53, 69, 0.05);
}

/* Hybrid JSON Editor */
.hybrid-json-editor {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

/* Body Editor Enhancements */
.body-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  height: 100%;
  min-height: 0;
}

.body-format-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.body-format-selector select {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 12px;
}

.body-format-selector select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.body-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.body-form {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Input Focus Improvements */
.params-table input:focus,
.headers-table input:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
  border-color: var(--color-primary);
}

.params-table input,
.headers-table input {
  transition: border-color 0.2s, outline 0.2s;
}

/* Prevent input clearing on focus */
.params-table td,
.headers-table td {
  position: relative;
}

.params-table input,
.headers-table input {
  width: 100%;
  box-sizing: border-box;
  background: var(--color-background);
  color: var(--color-text);
}

/* Keyboard Shortcuts Indicator */
.keyboard-shortcuts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  color: var(--color-text-secondary);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s;
}

.keyboard-shortcuts.visible {
  opacity: 1;
}

.keyboard-shortcuts h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--color-text);
}

.keyboard-shortcuts ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.keyboard-shortcuts li {
  margin: 4px 0;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.keyboard-shortcuts .shortcut {
  font-family: monospace;
  background: var(--color-background);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid var(--color-border);
}

/* Simple JSON Editor */
.simple-json-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.json-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--color-border);
  flex-shrink: 0;
}

.json-editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.json-status {
  font-size: 12px;
}

.json-error {
  color: #ff6b6b;
  font-weight: 500;
}

.json-valid {
  color: #51cf66;
  font-weight: 500;
}

.json-actions {
  display: flex;
  gap: 8px;
}

.json-format-btn,
.json-minify-btn {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-surface);
  color: var(--color-text);
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.json-format-btn:hover,
.json-minify-btn:hover {
  background: var(--color-background);
}

.json-format-btn:disabled,
.json-minify-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.json-textarea {
  font-family: Monaco, Menlo, "Ubuntu Mono", monospace !important;
  width: 100%;
  height: 100%;
  min-height: 200px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.4;
  background: var(--color-background);
  color: var(--color-text);
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.json-textarea:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

.json-textarea.invalid {
  border-color: #ff6b6b !important;
}

/* Buttons */
.btn {
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:hover {
  background: var(--color-surface);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

/* Tree View Styles */
.tree-view {
  flex: 1;
  overflow-y: auto;
  padding: 4px 0;
}

.tree-item {
  position: relative;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  margin: 1px 4px;
  transition: all 0.2s;
  color: var(--color-text);
}

.tree-item:hover {
  background: var(--color-surface);
}

.tree-item.selected {
  background: var(--color-primary);
  color: white;
}

.tree-item.selected:hover {
  background: var(--color-primary-dark);
}

.tree-item.dragging {
  opacity: 0.5;
}

.tree-item.drop-target {
  background: rgba(0, 122, 204, 0.1);
  border: 2px dashed var(--color-primary);
}

.tree-item-content {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  min-height: 32px;
}

.tree-toggle {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 10px;
  border-radius: 2px;
  transition: all 0.2s;
}

.tree-toggle:hover {
  background: var(--color-surface);
  color: var(--color-text);
}

.tree-toggle.empty {
  visibility: hidden;
}

.tree-item.selected .tree-toggle {
  color: rgba(255, 255, 255, 0.8);
}

.tree-item.selected .tree-toggle:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.tree-icon {
  font-size: 16px;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.tree-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--color-text);
}

.tree-unsaved-indicator {
  color: var(--color-warning);
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
}

.tree-method {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  flex-shrink: 0;
  margin-left: auto;
}

.tree-example-badge {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  background: #6f42c1;
  color: white;
  flex-shrink: 0;
  margin-left: auto;
}

.tree-edit-input {
  flex: 1;
  background: var(--color-background);
  border: 1px solid var(--color-primary);
  border-radius: 2px;
  padding: 2px 6px;
  font-size: 14px;
  color: var(--color-text);
  outline: none;
}

.tree-children {
  position: relative;
}

.tree-children::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: var(--color-border);
  opacity: 0.5;
}

/* Context Menu */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.context-menu {
  position: fixed;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  min-width: 150px;
  padding: 4px 0;
}

.context-menu button {
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  color: var(--color-text);
  cursor: pointer;
  transition: background 0.2s;
}

.context-menu button:hover {
  background: var(--color-surface);
}

.context-menu button.danger {
  color: var(--color-error);
}

.context-menu button.danger:hover {
  background: rgba(220, 53, 69, 0.1);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.sidebar-actions {
  display: flex;
  gap: 4px;
}

.sidebar-action-btn {
  padding: 4px 8px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.sidebar-action-btn:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* Drag and Drop Indicators */
.drop-indicator {
  height: 2px;
  background: var(--color-primary);
  margin: 2px 0;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s;
}

.drop-indicator.active {
  opacity: 1;
}

/* Tree View Scrollbar */
.tree-view::-webkit-scrollbar {
  width: 6px;
}

.tree-view::-webkit-scrollbar-track {
  background: transparent;
}

.tree-view::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.tree-view::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Toast Notification */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--color-primary);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  font-size: 14px;
  font-weight: 500;
  animation: slideInRight 0.3s ease-out;
  max-width: 400px;
  word-wrap: break-word;
}

.toast-notification.error {
  background: var(--color-danger);
}

.toast-notification.success {
  background: var(--color-success);
}

.toast-notification.warning {
  background: var(--color-warning);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Dialog Overlay */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

/* Base Dialog Styles */
.dialog {
  background: var(--color-background);
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Request Save Dialog */
.request-save-dialog {
  width: 700px;
  max-height: 700px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border);
}

.dialog-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.dialog-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.dialog-close:hover {
  background: var(--color-surface);
  color: var(--color-text);
}

.dialog-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid var(--color-border);
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
}

.form-group input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input[type="text"]:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

/* Monaco Editor Styles */
.monaco-editor-wrapper {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
  background: var(--color-background);
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex children */
}

.monaco-editor-container {
  flex: 1;
  min-height: 0; /* Important for flex children */
  position: relative;
}

.monaco-editor-wrapper .monaco-editor {
  background: var(--color-background) !important;
}

.monaco-editor-wrapper .monaco-editor .margin {
  background: var(--color-surface) !important;
}

.monaco-editor-wrapper .monaco-editor .monaco-editor-background {
  background: var(--color-background) !important;
}

/* Remove line highlighting */
.monaco-editor-wrapper .monaco-editor .current-line {
  background: transparent !important;
}

.monaco-editor-wrapper .monaco-editor .current-line-exact {
  background: transparent !important;
}

.monaco-editor-wrapper .monaco-editor .line-highlight {
  background: transparent !important;
}

.monaco-editor-wrapper .monaco-editor .line-numbers {
  color: var(--color-text-secondary) !important;
}

.monaco-editor-wrapper .monaco-editor .mtk1 {
  color: var(--color-text) !important;
}

/* Monaco Editor Toolbar */
.monaco-editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-size: 12px;
  color: var(--color-text-secondary);
}

.monaco-editor-toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.monaco-editor-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.monaco-editor-format-btn {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.monaco-editor-format-btn:hover {
  background: var(--color-background);
  color: var(--color-text);
  border-color: var(--color-primary);
}

.monaco-editor-format-btn:active {
  background: var(--color-primary);
  color: white;
}

.monaco-editor-language-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.monaco-editor-size-info {
  font-size: 10px;
  color: var(--color-text-secondary);
}

/* Monaco Editor Container Wrapper */
.monaco-editor-container-wrapper {
  height: 100%;
  min-height: 400px; /* Minimum height for usability */
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Ensure body content areas are flexible */
.body-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-height: 0;
}

.body-content .body-format-selector {
  flex-shrink: 0;
  margin-bottom: 8px;
}

.body-content .monaco-editor-container-wrapper {
  flex: 1;
  min-height: 300px;
}

/* Monaco Editor in response body */
.response-body-content .monaco-editor-container-wrapper {
  flex: 1;
  min-height: 400px;
}

.hierarchy-container {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  background: var(--color-background);
}

.hierarchy-list {
  padding: 4px 0;
}

.hierarchy-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 32px;
}

.hierarchy-item:hover {
  background: var(--color-surface);
}

.hierarchy-item.selected {
  background: var(--color-primary);
  color: white;
}

.hierarchy-toggle {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 10px;
  border-radius: 2px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.hierarchy-toggle:hover {
  background: var(--color-surface);
  color: var(--color-text);
}

.hierarchy-item.selected .hierarchy-toggle {
  color: rgba(255, 255, 255, 0.8);
}

.hierarchy-item.selected .hierarchy-toggle:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.hierarchy-spacer {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.hierarchy-icon {
  font-size: 16px;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.hierarchy-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.loading-state,
.empty-state {
  padding: 20px;
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Workspace Panel Layout */
.workspace-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
  color: var(--color-text);
}

/* Left Sidebar */
.left-sidebar {
  width: 60px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  flex-shrink: 0;
}

.sidebar-section-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
  margin: 2px 4px;
  text-decoration: none;
}

.sidebar-section-btn:hover {
  background: var(--color-background);
  color: var(--color-text);
}

.sidebar-section-btn.active {
  background: var(--color-primary);
  color: white;
}

.sidebar-section-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.sidebar-section-label {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

/* Workspace Content */
.workspace-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.workspace-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0;
  color: var(--color-text);
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  flex-shrink: 0;
  min-height: 40px;
  box-sizing: border-box;
  overflow: visible;
  position: relative;
  z-index: 10;
}

.workspace-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin: 0;
  border-bottom: 0px;
  background: var(--color-surface);
  width: 100%;
  box-sizing: border-box;
  gap: 8px;
}

.workspace-action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-shrink: 0;
}

.workspace-action-btn {
  padding: 2px 8px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 3px;
  color: var(--color-text);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  height: 24px;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.workspace-action-btn:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.workspace-panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Collections Panel */
.collections-panel {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Environments Panel */
.environments-panel {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.environments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.environment-item {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s;
}

.environment-item:hover {
  border-color: var(--color-primary);
}

.environment-item.active {
  border-color: var(--color-primary);
  background: rgba(0, 122, 204, 0.05);
}

.environment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.environment-icon {
  font-size: 16px;
}

.environment-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  flex: 1;
}

.environment-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.environment-variables {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.environment-variable {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.variable-key {
  color: var(--color-text-secondary);
  font-weight: 500;
  min-width: 80px;
}

.variable-value {
  color: var(--color-text);
  font-family: 'Courier New', monospace;
  background: var(--color-background);
  padding: 2px 6px;
  border-radius: 3px;
  flex: 1;
}

/* History Panel */
.history-panel {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.history-item:hover {
  border-color: var(--color-primary);
  background: rgba(0, 122, 204, 0.05);
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.history-method {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  flex-shrink: 0;
}

.history-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  background: var(--color-background);
}

.history-duration {
  font-size: 11px;
  color: var(--color-text-secondary);
  margin-left: auto;
}

.history-url {
  font-size: 13px;
  color: var(--color-text);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Courier New', monospace;
}

.history-time {
  font-size: 11px;
  color: var(--color-text-secondary);
}

/* Panel Scrollbars */
.environments-panel::-webkit-scrollbar,
.history-panel::-webkit-scrollbar {
  width: 6px;
}

.environments-panel::-webkit-scrollbar-track,
.history-panel::-webkit-scrollbar-track {
  background: transparent;
}

.environments-panel::-webkit-scrollbar-thumb,
.history-panel::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.environments-panel::-webkit-scrollbar-thumb:hover,
.history-panel::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Resizable Splitter */
.resizable-splitter {
  background: var(--color-border);
  transition: background-color 0.2s ease;
  flex-shrink: 0;
  position: relative;
}

.resizable-splitter.horizontal {
  width: 1px;
  cursor: col-resize;
  min-height: 100%;
}

.resizable-splitter.vertical {
  height: 1px;
  cursor: row-resize;
  min-width: 100%;
}

.resizable-splitter:hover,
.resizable-splitter.hovered {
  background: var(--color-primary);
}

.resizable-splitter.dragging {
  background: var(--color-primary);
}

/* Resizable Layout */
.resizable-layout {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.resizable-layout.vertical {
  flex-direction: column;
}

.resizable-panel {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Allow workspace dropdown to show outside the left panel */
.resizable-panel:first-child {
  overflow: visible;
}

.resizable-panel.horizontal {
  flex-direction: row;
}

/* Environment Selector */
.environment-selector {
  position: relative;
}

.environment-selector-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text);
  cursor: pointer;
  font-size: 13px;
  min-width: 120px;
  transition: all 0.2s;
}

.environment-selector-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.environment-icon {
  font-size: 12px;
}

.environment-name {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.environment-arrow {
  font-size: 10px;
  color: var(--color-text-secondary);
  transition: transform 0.2s;
}

.environment-selector-btn:hover .environment-arrow {
  transform: rotate(180deg);
}

.environment-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 250px;
  max-height: 300px;
  overflow: hidden;
  margin-top: 4px;
}

.environment-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  font-weight: 600;
  font-size: 13px;
  color: var(--color-text);
  background: var(--color-background);
}

.environment-list {
  max-height: 200px;
  overflow-y: auto;
}

.environment-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid var(--color-border-light);
}

.environment-option:hover {
  background: var(--color-background);
}

.environment-option.active {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.environment-option-icon {
  font-size: 12px;
}

.environment-option-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
}

.environment-option-variables {
  font-size: 11px;
  color: var(--color-text-secondary);
}

.environment-dropdown-footer {
  padding: 8px;
  border-top: 1px solid var(--color-border);
  background: var(--color-background);
}

.environment-manage-btn {
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.environment-manage-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

/* Tab Search Dropdown */
.tab-search-dropdown {
  position: relative;
}

.tab-search-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.tab-search-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
  color: var(--color-text);
}

.tab-search-icon {
  font-size: 12px;
}

.tab-search-shortcut {
  font-size: 10px;
  color: var(--color-text-secondary);
}

.tab-search-dropdown-panel {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 350px;
  max-height: 400px;
  overflow: hidden;
  margin-top: 4px;
}

.tab-search-header {
  padding: 12px;
  border-bottom: 1px solid var(--color-border);
}

.tab-search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 13px;
  outline: none;
}

.tab-search-input:focus {
  border-color: var(--color-primary);
}

.tab-search-results {
  max-height: 300px;
  overflow-y: auto;
}

.tab-search-section {
  border-bottom: 1px solid var(--color-border);
}

.tab-search-section:last-child {
  border-bottom: none;
}

.tab-search-section-title {
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  color: var(--color-text-secondary);
  background: var(--color-background);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tab-search-item {
  padding: 10px 12px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid var(--color-border-light);
}

.tab-search-item:hover {
  background: var(--color-background);
}

.tab-search-item.active {
  background: var(--color-primary-light);
}

.tab-search-item-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tab-search-method {
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 40px;
  text-align: center;
}

.tab-search-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text);
}

.tab-search-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 16px;
  padding: 2px 4px;
  border-radius: 2px;
  transition: all 0.2s;
}

.tab-search-close:hover {
  background: var(--color-danger);
  color: white;
}

.tab-search-url {
  font-size: 11px;
  color: var(--color-text-secondary);
  margin-left: 48px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-search-empty {
  padding: 20px 12px;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 13px;
}

/* Tab Context Menu */
.tab-context-menu {
  position: fixed;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
  overflow: hidden;
  color: var(--color-text);
}

.tab-context-menu-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.1);
  border-bottom: 1px solid var(--color-border);
  font-size: 12px;
  color: var(--color-warning);
  font-weight: 500;
}

.warning-icon {
  font-size: 14px;
}

.tab-context-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  color: var(--color-text);
}

.tab-context-menu-item:hover:not(.disabled) {
  background: var(--color-background);
  color: var(--color-text);
}

.tab-context-menu-item.disabled {
  color: var(--color-text-secondary);
  cursor: not-allowed;
  opacity: 0.5;
}

.tab-context-menu-item.dangerous {
  color: var(--color-danger);
}

.tab-context-menu-item.dangerous:hover:not(.disabled) {
  background: rgba(220, 53, 69, 0.1);
  color: var(--color-danger);
}

.menu-item-label {
  flex: 1;
  color: inherit;
}

.menu-item-shortcut {
  font-size: 11px;
  color: var(--color-text-secondary);
  margin-left: 16px;
}

.tab-context-menu-separator {
  height: 1px;
  background: var(--color-border);
  margin: 4px 0;
}

/* Confirmation Dialog */
.confirmation-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirmation-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  z-index: 2001;
  min-width: 400px;
  max-width: 500px;
  overflow: hidden;
}

.confirmation-dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border);
}

.confirmation-dialog-icon {
  font-size: 24px;
}

.confirmation-dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.confirmation-dialog-content {
  padding: 16px 24px;
}

.confirmation-dialog-message {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text);
}

.confirmation-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
}

.btn {
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  outline: none;
}

.btn:focus {
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.btn-primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--color-background);
  border-color: var(--color-border);
  color: var(--color-text);
}

.btn-secondary:hover {
  background: var(--color-surface);
  border-color: var(--color-text-secondary);
}

.btn-warning {
  background: var(--color-warning);
  border-color: var(--color-warning);
  color: white;
}

.btn-warning:hover {
  background: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.btn-danger {
  background: var(--color-danger);
  border-color: var(--color-danger);
  color: white;
}

.btn-danger:hover {
  background: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

/* Environment Editor Styles */
.environment-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.form-actions {
  display: flex;
  gap: 8px;
}

.form-section {
  padding: 20px;
  border-bottom: 1px solid var(--color-border);
}

.form-section:last-child {
  border-bottom: none;
}

.environment-layout {
  display: flex;
  height: 100%;
  flex: 1;
}

.environment-sidebar {
  width: 300px;
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  background: var(--color-surface);
}

.environment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.environment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.environment-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.environment-item {
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--color-background);
}

.environment-item:hover {
  border-color: var(--color-primary);
  background: var(--color-background-hover);
}

.environment-item.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.environment-item.active {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.environment-item.creating {
  border-style: dashed;
}

.environment-info {
  margin-bottom: 8px;
}

.environment-name {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.active-badge {
  background: var(--color-success);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.environment-description {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

.environment-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 3px;
}

.environment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.environment-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.form-actions {
  display: flex;
  gap: 8px;
}

.form-section {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--color-text);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 14px;
  background: var(--color-background);
  color: var(--color-text);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.variables-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.variables-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

.variables-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.variables-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.variables-table {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--color-border);
  border-radius: 6px;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 1fr 1fr 100px;
  gap: 8px;
  padding: 12px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: var(--color-text-secondary);
  position: sticky;
  top: 0;
  z-index: 1;
}

/* Column-specific styling for environment variables table */
.col-enabled,
.col-key,
.col-value,
.col-description,
.col-actions {
  display: flex;
  align-items: center;
  padding: 4px;
}

.col-enabled {
  justify-content: center;
}

.col-actions {
  justify-content: center;
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 1fr 1fr 100px;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid var(--color-border);
  align-items: center;
  background: var(--color-background);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: var(--color-surface);
}

.table-row input[type="text"] {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  background: var(--color-background);
  color: var(--color-text);
}

.table-row input[type="text"]:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.table-row input[type="text"]::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.table-row input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
  cursor: pointer;
}

.table-row input[type="checkbox"]:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.environment-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Add row button styling for environment editor */
.variables-section .btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

/* History Viewer Styles */
.history-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

.history-layout {
  display: flex;
  height: 100%;
  flex: 1;
}

.history-sidebar {
  width: 350px;
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  background: var(--color-surface);
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.history-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: var(--color-text-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--color-text);
}

.stat-value.success {
  color: var(--color-success);
}

.stat-value.error {
  color: var(--color-danger);
}

.history-search {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  background: var(--color-background);
  color: var(--color-text);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.history-item {
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--color-background);
  color: var(--color-text);
}

.history-item:hover {
  border-color: var(--color-primary);
  background: var(--color-background-hover);
}

.history-item.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.history-item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.method-badge {
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 40px;
  text-align: center;
}

.method-badge.blue { background: #007acc; }
.method-badge.green { background: #28a745; }
.method-badge.orange { background: #fd7e14; }
.method-badge.red { background: #dc3545; }
.method-badge.purple { background: #6f42c1; }
.method-badge.gray { background: #6c757d; }

.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.status-badge.green { background: #28a745; }
.status-badge.orange { background: #fd7e14; }
.status-badge.red { background: #dc3545; }
.status-badge.gray { background: #6c757d; }

.history-name {
  flex: 1;
  font-weight: 600;
  font-size: 13px;
}

.history-item-url {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: var(--color-text-secondary);
}

.response-time {
  color: var(--color-text);
  font-weight: 500;
}

.environment-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.history-empty {
  padding: 20px;
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.details-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.details-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.details-actions {
  display: flex;
  gap: 8px;
}

.details-content {
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
}

.detail-item {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.detail-item strong {
  color: var(--color-text);
  margin-right: 8px;
}

.headers-list {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 12px;
}

.header-item {
  margin-bottom: 6px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.header-item:last-child {
  margin-bottom: 0;
}

.code-block {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-block {
  background: var(--color-danger-light);
  border: 1px solid var(--color-danger);
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
  color: var(--color-danger);
}

.history-empty-details {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Environments List Styles */
.environments-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

.environments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.environments-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.environments-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.environments-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  text-align: center;
}

.environments-empty p {
  margin-bottom: 16px;
  color: var(--color-text);
}

.environments-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.environment-item {
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  transition: all 0.2s;
  cursor: pointer;
  color: var(--color-text);
}

.environment-item:hover {
  border-color: var(--color-primary);
  background: var(--color-background-hover);
}

.environment-item.active {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.environment-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.environment-name {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text);
}

.environment-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.btn-icon {
  background: none;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
  color: var(--color-text);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.btn-icon:hover {
  background: var(--color-background-hover);
  border-color: var(--color-primary);
}

.btn-icon.btn-primary {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.btn-icon.btn-primary:hover {
  background: var(--color-primary);
  color: white;
}

.btn-icon.btn-danger {
  border-color: var(--color-danger);
  color: var(--color-danger);
}

.btn-icon.btn-danger:hover {
  background: var(--color-danger);
  color: white;
}

/* Button with icon styling */
.btn svg {
  margin-right: 6px;
  flex-shrink: 0;
}

.btn-icon svg {
  width: 16px;
  height: 16px;
}

.environment-variables-summary {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

/* History List Styles */
.history-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  flex-wrap: wrap;
}

/* History Entry Viewer Styles */
.history-entry-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
  padding: 16px;
}

.history-entry-viewer.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.history-entry-viewer.error {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-danger);
}

/* Settings Modal Styles */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-content {
  background: var(--color-background);
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  height: 80%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--color-border);
}

.settings-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text);
}

.settings-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 4px;
  border-radius: 4px;
}

.settings-close:hover {
  background: var(--color-background-hover);
}

.settings-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.settings-sidebar {
  width: 200px;
  border-right: 1px solid var(--color-border);
  background: var(--color-background-secondary);
}

.settings-tabs {
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.settings-tab {
  background: none;
  border: none;
  padding: 12px 16px;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 4px;
  color: var(--color-text);
  font-size: 14px;
}

.settings-tab:hover {
  background: var(--color-background-hover);
}

.settings-tab.active {
  background: var(--color-primary);
  color: white;
}

.settings-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.settings-section h3 {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group p,
.setting-group span,
.setting-group .description {
  color: var(--color-text);
}

.setting-group .help-text {
  color: var(--color-text-secondary);
  font-size: 12px;
  margin-top: 4px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text);
}

.setting-group input,
.setting-group select,
.setting-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.setting-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.setting-group input[type="range"] {
  width: 100%;
}

.providers-section {
  margin-top: 32px;
}

.providers-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.providers-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.providers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.provider-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--color-text);
}

.provider-type {
  font-size: 12px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
}

.provider-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  min-width: 32px;
  height: 32px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover:not(:disabled) {
  background: var(--color-background-hover);
  color: var(--color-text);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-icon.btn-danger {
  color: var(--color-danger);
}

.btn-icon.btn-danger:hover:not(:disabled) {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.btn-icon svg {
  flex-shrink: 0;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Button Styles */
.btn-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary.btn-with-icon {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px rgba(0, 122, 204, 0.2);
}

.btn-primary.btn-with-icon:hover:not(:disabled) {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
}

.btn-primary.btn-with-icon:disabled {
  background: var(--color-text-secondary);
  border-color: var(--color-text-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary.btn-with-icon {
  background: var(--color-background);
  color: var(--color-text);
  border-color: var(--color-border);
}

.btn-secondary.btn-with-icon:hover:not(:disabled) {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-danger.btn-with-icon {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.btn-danger.btn-with-icon:hover:not(:disabled) {
  background: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-with-icon svg {
  flex-shrink: 0;
}

/* Settings Footer */
.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

/* About Actions */
.about-actions {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--color-border);
}

.about-actions .btn-danger.btn-with-icon {
  background: transparent;
  color: var(--color-danger);
  border-color: var(--color-danger);
}

.about-actions .btn-danger.btn-with-icon:hover {
  background: var(--color-danger);
  color: white;
}

/* Provider Editor Enhancements */
.input-with-button {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-with-button input {
  flex: 1;
}

.input-with-button .btn-small {
  padding: 8px 12px;
  min-width: auto;
  height: auto;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.models-selection {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 12px;
  background: var(--color-surface);
}

.models-dropdown {
  width: 100%;
  min-height: 120px;
  max-height: 200px;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 13px;
  font-family: monospace;
  margin-bottom: 12px;
  resize: vertical;
}

.models-dropdown option {
  padding: 4px 8px;
  background: var(--color-background);
  color: var(--color-text);
}

.models-dropdown option:checked {
  background: var(--color-primary);
  color: white;
}

.models-dropdown:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.models-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid var(--color-border);
}

.selected-count {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.models-actions {
  display: flex;
  gap: 8px;
}

.models-actions .btn-small {
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 4px;
}

.manual-models .help-text {
  margin-top: 4px;
  font-size: 11px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-small:hover:not(:disabled) {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
}

.btn-small:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.about-content {
  line-height: 1.6;
  color: var(--color-text);
}

/* Provider Editor Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000; /* Higher than settings modal */
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--color-background);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  display: flex;
  align-items: center;
  gap: 12px;
}

.provider-type-badge {
  background: var(--color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.btn-secondary.connected {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.models-dropdown-single {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-bg-secondary);
  color: var(--color-text);
  font-family: var(--font-mono);
  font-size: 13px;
}

.connection-status {
  margin-top: 8px;
  font-size: 12px;
}

.status-connected {
  color: var(--color-success);
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-disconnected {
  color: var(--color-text-secondary);
}

.error-message {
  background: rgba(220, 53, 69, 0.1);
  color: var(--color-error);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--color-error);
}

.modal-header button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-header button:hover {
  background: var(--color-background-hover);
  color: var(--color-text);
}

.modal-content form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text);
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
  border-radius: 0 0 12px 12px;
}

.form-actions button {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--color-border);
}

.form-actions .btn-primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.form-actions .btn-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.form-actions .btn-secondary {
  background: var(--color-background);
  color: var(--color-text);
}

.form-actions .btn-secondary:hover {
  background: var(--color-background-hover);
}

.about-content p {
  margin-bottom: 12px;
}

.about-actions {
  margin-top: 32px;
}

.settings-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--color-border);
}

.settings-loading,
.settings-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
}

/* Chat Panel Styles */
.chat-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
  border-left: 1px solid var(--color-border);
  overflow: hidden;
}

.resizable-panel.chat-panel {
  flex-shrink: 0;
}

.chat-panel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.chat-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.chat-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.chat-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-session-dropdown {
  position: relative;
}

.session-selector {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 6px 8px;
  color: var(--color-text);
  font-size: 12px;
  min-width: 150px;
  max-width: 200px;
}

.session-selector:focus {
  outline: none;
  border-color: var(--color-primary);
}

.chat-conversations {
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 8px;
}

.conversations-header {
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-secondary);
  background: var(--color-surface);
}

.conversations-list {
  max-height: 150px;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--color-text);
}

.conversation-item:hover {
  background: var(--color-background-hover);
}

.conversation-item.active {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.conversation-title {
  flex: 1;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-delete {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s;
  opacity: 0;
}

.conversation-item:hover .conversation-delete {
  opacity: 1;
}

.conversation-delete:hover {
  background: var(--color-danger-light);
  color: var(--color-danger);
}

/* Binary Content Display */
.binary-content-display {
  padding: 20px;
  background: var(--color-background);
  border-radius: 6px;
  border: 1px solid var(--color-border);
}

.binary-content-info {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-surface);
  border-radius: 6px;
  border-left: 4px solid var(--color-warning);
}

.binary-content-info h4 {
  margin: 0 0 8px 0;
  color: var(--color-text);
  font-size: 16px;
  font-weight: 600;
}

.binary-content-info p {
  margin: 4px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.binary-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.binary-actions .btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.binary-actions .btn-primary {
  background: var(--color-primary);
  color: white;
}

.binary-actions .btn-primary:hover {
  background: var(--color-primary-dark);
}

.binary-actions .btn-secondary {
  background: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.binary-actions .btn-secondary:hover {
  background: var(--color-background-hover);
  border-color: var(--color-primary);
}

.binary-preview {
  margin-top: 16px;
}

.binary-preview h5 {
  margin: 0 0 8px 0;
  color: var(--color-text);
  font-size: 14px;
  font-weight: 600;
}

.binary-preview .monaco-editor-container-wrapper {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.chat-panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Legacy Chat Styles */
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-background);
  display: flex;
  z-index: 999;
}

.chat-sidebar {
  width: 100%;
  background: var(--color-background);
  display: flex;
  flex-direction: column;
  max-height: 200px;
  border-bottom: 1px solid var(--color-border);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chat-header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chat-sessions {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.chat-session-item {
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 4px;
  position: relative;
  transition: background-color 0.2s;
  background: var(--color-background);
  color: var(--color-text);
}

.chat-session-item:hover {
  background: var(--color-background-hover);
}

.chat-session-item.active {
  background: var(--color-primary-light);
  border-left: 3px solid var(--color-primary);
}

.session-title {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text);
}

.session-date {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.session-delete {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-session-item:hover .session-delete {
  opacity: 1;
}

.session-delete:hover {
  background: var(--color-danger);
  color: white;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
  color: var(--color-text);
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background-secondary);
}

.provider-selector,
.model-selector {
  flex: 1;
}

.provider-selector select,
.model-selector select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 12px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.message-role {
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: var(--color-text);
}

.message.user .message-role {
  color: var(--color-primary);
}

.message.assistant .message-role {
  color: var(--color-success);
}

.message-time {
  font-size: 11px;
  color: var(--color-text-secondary);
}

.message-content {
  background: var(--color-surface);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.message.user .message-content {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.message-images {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
}

.generated-request {
  margin-top: 12px;
  padding: 12px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
}

.request-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.request-json {
  background: var(--color-background-secondary);
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: var(--color-text-secondary);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}

/* Chat Input Area */
.chat-input {
  border-top: 1px solid var(--color-border);
  background: var(--color-background);
}

/* Selected Images */
.selected-images {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.selected-images-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.images-count {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.clear-images-btn {
  background: none;
  border: none;
  color: var(--color-danger);
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.clear-images-btn:hover {
  background: var(--color-danger-light);
}

.selected-images-grid {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-image {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--color-border);
  transition: transform 0.2s, box-shadow 0.2s;
}

.selected-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.selected-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-danger);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.remove-image-btn:hover {
  transform: scale(1.1);
  background: var(--color-danger-dark);
}

/* Main Input Row */
.input-main-row {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 16px;
  background: var(--color-background);
}

.input-field-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: flex-end;
}

.chat-textarea {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  padding-right: 32px; /* Space for clear button */
  border: 1px solid var(--color-border);
  border-radius: 22px;
  background: var(--color-surface);
  color: var(--color-text);
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  transition: border-color 0.2s, box-shadow 0.2s;
  overflow-y: auto;

  /* Hide scrollbar */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.chat-textarea::-webkit-scrollbar {
  display: none;
}

.chat-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.chat-textarea::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.clear-input-btn {
  position: absolute;
  right: 8px;
  bottom: 12px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.clear-input-btn:hover {
  background: var(--color-background-hover);
  color: var(--color-text);
}

/* Send Button */
.send-btn {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 122, 204, 0.2);
}

.send-btn:hover:not(:disabled) {
  background: var(--color-primary-dark);
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 122, 204, 0.4);
}

.send-btn:disabled {
  background: var(--color-text-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.send-icon {
  transform: rotate(0deg);
  transition: transform 0.2s;
}

.send-btn:hover:not(:disabled) .send-icon {
  transform: rotate(-10deg);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-spinner svg {
  display: block;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Bottom Actions Row */
.input-bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-btn {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-btn:hover:not(:disabled) {
  background: var(--color-background-hover);
  color: var(--color-text);
  transform: scale(1.05);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: var(--color-text-secondary);
}

.input-hints {
  display: flex;
  align-items: center;
  gap: 4px;
}

.input-hints kbd {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 3px;
  padding: 1px 4px;
  font-size: 9px;
  font-family: monospace;
  color: var(--color-text);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 14px;
  text-align: center;
}

.input-stats {
  display: flex;
  gap: 6px;
  align-items: center;
}

.char-count,
.image-count {
  font-size: 10px;
  color: var(--color-text-secondary);
  padding: 2px 6px;
  background: var(--color-background);
  border-radius: 10px;
  border: 1px solid var(--color-border);
}

.char-count {
  opacity: 0.8;
}

.image-count {
  color: var(--color-primary);
  font-weight: 500;
  border-color: var(--color-primary);
  background: rgba(0, 122, 204, 0.1);
}

/* Bottom border to connect all app borders */
.app-bottom-border {
  height: 1px;
  background-color: var(--color-border);
  width: 100%;
  flex-shrink: 0;
}
