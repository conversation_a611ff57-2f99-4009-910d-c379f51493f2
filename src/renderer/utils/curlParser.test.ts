import { parseCurlCommand, isCurlCommand } from './curlParser';

describe('curlParser', () => {
  describe('isCurlCommand', () => {
    it('should detect curl commands', () => {
      expect(isCurlCommand('curl https://api.example.com')).toBe(true);
      expect(isCurlCommand('curl -X POST https://api.example.com')).toBe(true);
      expect(isCurlCommand('CURL https://api.example.com')).toBe(true);
      expect(isCurlCommand('  curl https://api.example.com  ')).toBe(true);
      expect(isCurlCommand('curl')).toBe(true);
    });

    it('should not detect non-curl commands', () => {
      expect(isCurlCommand('wget https://api.example.com')).toBe(false);
      expect(isCurlCommand('https://api.example.com')).toBe(false);
      expect(isCurlCommand('some curl command')).toBe(false);
      expect(isCurlCommand('')).toBe(false);
    });
  });

  describe('parseCurlCommand', () => {
    it('should parse simple GET request', () => {
      const curl = 'curl https://api.example.com/users';
      const result = parseCurlCommand(curl);
      
      expect(result).toEqual({
        method: 'GET',
        url: 'https://api.example.com/users',
        headers: {},
        name: 'GET users'
      });
    });

    it('should parse POST request with data', () => {
      const curl = 'curl -X POST -d \'{"name":"John"}\' https://api.example.com/users';
      const result = parseCurlCommand(curl);
      
      expect(result).toEqual({
        method: 'POST',
        url: 'https://api.example.com/users',
        headers: {},
        body: '{"name":"John"}',
        name: 'POST users'
      });
    });

    it('should parse request with headers', () => {
      const curl = 'curl -H "Content-Type: application/json" -H "Authorization: Bearer token123" https://api.example.com/users';
      const result = parseCurlCommand(curl);
      
      expect(result).toEqual({
        method: 'GET',
        url: 'https://api.example.com/users',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token123'
        },
        name: 'GET users'
      });
    });

    it('should parse complex Chrome copy as curl', () => {
      const curl = `curl 'https://jsonplaceholder.typicode.com/posts/1' \\
  -H 'accept: application/json, text/plain, */*' \\
  -H 'accept-language: en-US,en;q=0.9' \\
  -H 'cache-control: no-cache' \\
  -H 'pragma: no-cache' \\
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \\
  --compressed`;
      
      const result = parseCurlCommand(curl);
      
      expect(result?.method).toBe('GET');
      expect(result?.url).toBe('https://jsonplaceholder.typicode.com/posts/1');
      expect(result?.headers['accept']).toBe('application/json, text/plain, */*');
      expect(result?.headers['user-agent']).toContain('Mozilla/5.0');
      expect(result?.headers['Accept-Encoding']).toBe('gzip, deflate, br');
    });

    it('should parse POST with JSON data', () => {
      const curl = `curl -X POST 'https://api.example.com/users' \\
  -H 'Content-Type: application/json' \\
  -d '{"name":"John Doe","email":"<EMAIL>"}'`;
      
      const result = parseCurlCommand(curl);
      
      expect(result).toEqual({
        method: 'POST',
        url: 'https://api.example.com/users',
        headers: {
          'Content-Type': 'application/json'
        },
        body: '{"name":"John Doe","email":"<EMAIL>"}',
        name: 'POST users'
      });
    });

    it('should handle basic auth', () => {
      const curl = 'curl -u "username:password" https://api.example.com/users';
      const result = parseCurlCommand(curl);
      
      expect(result?.headers['Authorization']).toBe('Basic ' + btoa('username:password'));
    });

    it('should handle cookies', () => {
      const curl = 'curl -b "session=abc123; theme=dark" https://api.example.com/users';
      const result = parseCurlCommand(curl);
      
      expect(result?.headers['Cookie']).toBe('session=abc123; theme=dark');
    });

    it('should return null for invalid curl', () => {
      expect(parseCurlCommand('not a curl command')).toBe(null);
      expect(parseCurlCommand('curl')).toBe(null); // No URL
      expect(parseCurlCommand('')).toBe(null);
    });

    it('should generate appropriate names', () => {
      expect(parseCurlCommand('curl https://api.example.com')?.name).toBe('GET api.example.com');
      expect(parseCurlCommand('curl https://api.example.com/users/123')?.name).toBe('GET 123');
      expect(parseCurlCommand('curl -X DELETE https://api.example.com/users/123')?.name).toBe('DELETE 123');
    });
  });
});
