<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ApiCool - Advanced API Testing Tool</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: filesystem: https://cdn.jsdelivr.net; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://cdn.jsdelivr.net; connect-src 'self' https: http: ws: wss:; style-src 'self' 'unsafe-inline' blob: https://cdn.jsdelivr.net; font-src 'self' data: blob: https://cdn.jsdelivr.net; worker-src 'self' blob: https://cdn.jsdelivr.net;" />

    <!-- Monaco Editor CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/editor/editor.main.css">
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/loader.js"></script>
    <script>
        // Configure Monaco Editor loader for CDN
        require.config({
            paths: {
                'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'
            }
        });

        // Set up Monaco Environment for web workers
        window.MonacoEnvironment = {
            getWorkerUrl: function (moduleId, label) {
                if (label === 'json') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/json/json.worker.js';
                }
                if (label === 'css' || label === 'scss' || label === 'less') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/css/css.worker.js';
                }
                if (label === 'html' || label === 'handlebars' || label === 'razor') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/html/html.worker.js';
                }
                if (label === 'typescript' || label === 'javascript') {
                    return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/typescript/ts.worker.js';
                }
                return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js';
            }
        };

        // Initialize Monaco Editor
        require(['vs/editor/editor.main'], function() {
            console.log('Monaco Editor loaded successfully from CDN');
            window.monacoLoaded = true;
            // Dispatch custom event to notify components
            window.dispatchEvent(new CustomEvent('monacoLoaded'));
        });
    </script>



    <style>
        /* CSS Variables for theming */
        :root {
            --color-primary: #007acc;
            --color-primary-dark: #005a9e;
            --color-primary-light: rgba(0, 122, 204, 0.1);
            --color-secondary: #6c757d;
            --color-accent: #28a745;
            --color-background: #ffffff;
            --color-background-hover: #f8f9fa;
            --color-background-secondary: #f1f3f4;
            --color-surface: #f8f9fa;
            --color-text: #212529;
            --color-text-secondary: #6c757d;
            --color-border: #dee2e6;
            --color-border-light: #e9ecef;
            --color-error: #dc3545;
            --color-danger: #dc3545;
            --color-danger-dark: #c82333;
            --color-warning: #ffc107;
            --color-warning-dark: #e0a800;
            --color-warning-light: rgba(255, 193, 7, 0.1);
            --color-success: #28a745;
            --color-success-light: rgba(40, 167, 69, 0.1);
            --color-info: #17a2b8;
            --color-danger-light: rgba(220, 53, 69, 0.1);
        }

        /* Dark theme variables */
        [data-theme="dark"] {
            --color-primary: #0d7377;
            --color-primary-dark: #0a5d61;
            --color-primary-light: rgba(13, 115, 119, 0.1);
            --color-secondary: #6c757d;
            --color-accent: #20c997;
            --color-background: #1a1a1a;
            --color-background-hover: #2a2a2a;
            --color-background-secondary: #242424;
            --color-surface: #2d2d2d;
            --color-text: #ffffff;
            --color-text-secondary: #adb5bd;
            --color-border: #495057;
            --color-border-light: #3a3a3a;
            --color-error: #e74c3c;
            --color-danger: #e74c3c;
            --color-danger-dark: #c0392b;
            --color-warning: #f39c12;
            --color-warning-dark: #d68910;
            --color-warning-light: rgba(243, 156, 18, 0.1);
            --color-success: #27ae60;
            --color-success-light: rgba(39, 174, 96, 0.1);
            --color-info: #3498db;
            --color-danger-light: rgba(231, 76, 60, 0.1);
        }

        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--color-background);
            color: var(--color-text);
            height: 100vh;
            overflow: hidden;
        }

        #root {
            height: 100vh;
            width: 100vw;
        }
    </style>
    <script>
        // Initialize theme from system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');

        // Listen for theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        });
    </script>
</head>
<body>
    <div id="root"></div>
</body>
</html>
