import { useState, useEffect, useCallback } from 'react';
import { uiStateManager, UIState } from '../services/UIStateManager';

/**
 * Hook to use UI state with automatic persistence
 */
export function useUIState() {
  const [state, setState] = useState<Partial<UIState>>(() => 
    uiStateManager.getCurrentState()
  );

  useEffect(() => {
    // Load initial state
    const initialState = uiStateManager.loadState();
    setState(initialState);

    // Subscribe to state changes
    const unsubscribe = uiStateManager.subscribe((newState) => {
      setState(newState);
    });

    return unsubscribe;
  }, []);

  const updateState = useCallback((updates: Partial<UIState>) => {
    uiStateManager.updateState(updates);
    uiStateManager.saveState();
  }, []);

  const forceSave = useCallback(() => {
    uiStateManager.forceSave();
  }, []);

  return {
    state,
    updateState,
    forceSave,
  };
}

/**
 * Hook for managing tab state
 */
export function useTabState() {
  const { state, updateState } = useUIState();

  const addTab = useCallback((tab: {
    id: string;
    entityId?: number;
    name: string;
    method: string;
    url: string;
    headers?: Record<string, string>;
    params?: Record<string, string>;
    body?: string;
    hasUnsavedChanges?: boolean;
  }) => {
    const openTabs = [...(state.openTabs || [])];
    const existingIndex = openTabs.findIndex(t => t.id === tab.id);
    
    if (existingIndex >= 0) {
      // Update existing tab
      openTabs[existingIndex] = { ...openTabs[existingIndex], ...tab };
    } else {
      // Add new tab
      openTabs.push(tab);
    }
    
    updateState({
      openTabs,
      activeTab: tab.id,
    });
  }, [state.openTabs, updateState]);

  const removeTab = useCallback((tabId: string) => {
    const openTabs = (state.openTabs || []).filter(tab => tab.id !== tabId);
    let activeTab = state.activeTab;
    
    // If removing active tab, switch to another tab
    if (activeTab === tabId && openTabs.length > 0) {
      const currentIndex = (state.openTabs || []).findIndex(tab => tab.id === tabId);
      const nextIndex = Math.min(currentIndex, openTabs.length - 1);
      activeTab = openTabs[nextIndex]?.id || '';
    } else if (openTabs.length === 0) {
      activeTab = '';
    }
    
    updateState({
      openTabs,
      activeTab,
    });
  }, [state.openTabs, state.activeTab, updateState]);

  const setActiveTab = useCallback((tabId: string) => {
    updateState({ activeTab: tabId });
  }, [updateState]);

  const updateTab = useCallback((tabId: string, updates: Partial<{
    entityId: number;
    name: string;
    method: string;
    url: string;
    headers: Record<string, string>;
    params: Record<string, string>;
    body: string;
    hasUnsavedChanges: boolean;
  }>) => {
    const openTabs = (state.openTabs || []).map(tab =>
      tab.id === tabId ? { ...tab, ...updates } : tab
    );
    
    updateState({ openTabs });
  }, [state.openTabs, updateState]);

  return {
    openTabs: state.openTabs || [],
    activeTab: state.activeTab || '',
    addTab,
    removeTab,
    setActiveTab,
    updateTab,
  };
}

/**
 * Hook for managing collection tree state
 */
export function useCollectionTreeState() {
  const { state, updateState } = useUIState();

  const toggleCollection = useCallback((collectionId: string) => {
    const expandedCollections = state.expandedCollections || [];
    const isExpanded = expandedCollections.includes(collectionId);
    
    const newExpanded = isExpanded
      ? expandedCollections.filter(id => id !== collectionId)
      : [...expandedCollections, collectionId];
    
    updateState({ expandedCollections: newExpanded });
  }, [state.expandedCollections, updateState]);

  const toggleFolder = useCallback((folderId: string) => {
    const expandedFolders = state.expandedFolders || [];
    const isExpanded = expandedFolders.includes(folderId);
    
    const newExpanded = isExpanded
      ? expandedFolders.filter(id => id !== folderId)
      : [...expandedFolders, folderId];
    
    updateState({ expandedFolders: newExpanded });
  }, [state.expandedFolders, updateState]);

  const setExpandedCollections = useCallback((collectionIds: string[]) => {
    updateState({ expandedCollections: collectionIds });
  }, [updateState]);

  const setExpandedFolders = useCallback((folderIds: string[]) => {
    updateState({ expandedFolders: folderIds });
  }, [updateState]);

  return {
    expandedCollections: state.expandedCollections || [],
    expandedFolders: state.expandedFolders || [],
    toggleCollection,
    toggleFolder,
    setExpandedCollections,
    setExpandedFolders,
  };
}

/**
 * Hook for managing panel state
 */
export function usePanelState() {
  const { state, updateState } = useUIState();

  const setLeftPanelWidth = useCallback((width: number) => {
    updateState({ leftPanelWidth: width });
  }, [updateState]);

  const setWorkspaceSection = useCallback((section: 'collections' | 'environments' | 'history') => {
    updateState({ workspaceSection: section });
  }, [updateState]);

  const setChatOpen = useCallback((isOpen: boolean) => {
    updateState({ isChatOpen: isOpen });
  }, [updateState]);

  return {
    leftPanelWidth: state.leftPanelWidth || 360,
    workspaceSection: state.workspaceSection || 'collections',
    isChatOpen: state.isChatOpen || false,
    setLeftPanelWidth,
    setWorkspaceSection,
    setChatOpen,
  };
}

/**
 * Hook for managing response viewer state
 */
export function useResponseViewerState() {
  const { state, updateState } = useUIState();

  const setActiveTab = useCallback((tab: 'body' | 'headers' | 'cookies' | 'test-results') => {
    updateState({
      responseViewerState: {
        ...state.responseViewerState,
        activeTab: tab,
      },
    });
  }, [state.responseViewerState, updateState]);

  const setBodyViewMode = useCallback((mode: 'pretty' | 'raw' | 'tree') => {
    updateState({
      responseViewerState: {
        ...state.responseViewerState,
        bodyViewMode: mode,
      },
    });
  }, [state.responseViewerState, updateState]);

  const setWrapLines = useCallback((wrap: boolean) => {
    updateState({
      responseViewerState: {
        ...state.responseViewerState,
        wrapLines: wrap,
      },
    });
  }, [state.responseViewerState, updateState]);

  const setShowLineNumbers = useCallback((show: boolean) => {
    updateState({
      responseViewerState: {
        ...state.responseViewerState,
        showLineNumbers: show,
      },
    });
  }, [state.responseViewerState, updateState]);

  return {
    responseViewerState: state.responseViewerState || {
      activeTab: 'body',
      bodyViewMode: 'pretty',
      wrapLines: true,
      showLineNumbers: true,
    },
    setActiveTab,
    setBodyViewMode,
    setWrapLines,
    setShowLineNumbers,
  };
}

/**
 * Hook for managing request editor state
 */
export function useRequestEditorState() {
  const { state, updateState } = useUIState();

  const setActiveTab = useCallback((tab: 'params' | 'headers' | 'body' | 'auth' | 'pre-script' | 'tests') => {
    updateState({
      requestEditorState: {
        ...state.requestEditorState,
        activeTab: tab,
      },
    });
  }, [state.requestEditorState, updateState]);

  const setBodyType = useCallback((type: 'none' | 'raw' | 'form-data' | 'x-www-form-urlencoded') => {
    updateState({
      requestEditorState: {
        ...state.requestEditorState,
        bodyType: type,
      },
    });
  }, [state.requestEditorState, updateState]);

  const setBodyLanguage = useCallback((language: string) => {
    updateState({
      requestEditorState: {
        ...state.requestEditorState,
        bodyLanguage: language,
      },
    });
  }, [state.requestEditorState, updateState]);

  return {
    requestEditorState: state.requestEditorState || {
      activeTab: 'params',
      bodyType: 'none',
      bodyLanguage: 'json',
    },
    setActiveTab,
    setBodyType,
    setBodyLanguage,
  };
}
