import React, { useEffect, useRef } from 'react';

interface TabContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  tabId: string;
  tabIndex: number;
  totalTabs: number;
  hasUnsavedChanges: boolean;
  onClose: () => void;
  onCloseTab: (tabId: string) => void;
  onCloseTabsToLeft: (tabIndex: number) => void;
  onCloseTabsToRight: (tabIndex: number) => void;
  onCloseAllTabs: () => void;
  onForceCloseAll: () => void;
  onDuplicateTab?: (tabId: string) => void;
  onRenameTab?: (tabId: string) => void;
}

const TabContextMenu: React.FC<TabContextMenuProps> = ({
  isVisible,
  position,
  tabId,
  tabIndex,
  totalTabs,
  hasUnsavedChanges,
  onClose,
  onCloseTab,
  onCloseTabsToLeft,
  onCloseTabsToRight,
  onCloseAllTabs,
  onForceCloseAll,
  onDuplicateTab,
  onRenameTab
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  const handleMenuAction = (action: () => void) => {
    action();
    onClose();
  };

  const menuItems = [
    {
      label: 'Close Tab',
      action: () => onCloseTab(tabId),
      disabled: false,
      shortcut: 'Ctrl+W'
    },
    {
      label: 'Close Tabs to the Left',
      action: () => onCloseTabsToLeft(tabIndex),
      disabled: tabIndex === 0,
      shortcut: ''
    },
    {
      label: 'Close Tabs to the Right',
      action: () => onCloseTabsToRight(tabIndex),
      disabled: tabIndex === totalTabs - 1,
      shortcut: ''
    },
    {
      label: 'Close All Tabs',
      action: onCloseAllTabs,
      disabled: totalTabs === 0,
      shortcut: 'Ctrl+Shift+W'
    },
    {
      label: 'Force Close All',
      action: onForceCloseAll,
      disabled: totalTabs === 0,
      shortcut: '',
      dangerous: true
    }
  ];

  // Add optional actions if provided
  if (onDuplicateTab) {
    menuItems.unshift({
      label: 'Duplicate Tab',
      action: () => onDuplicateTab(tabId),
      disabled: false,
      shortcut: 'Ctrl+Shift+D'
    });
  }

  if (onRenameTab) {
    menuItems.unshift({
      label: 'Rename Tab',
      action: () => onRenameTab(tabId),
      disabled: false,
      shortcut: 'F2'
    });
  }

  // Add separator before close actions
  const separatorIndex = menuItems.findIndex(item => item.label === 'Close Tab');
  if (separatorIndex > 0) {
    menuItems.splice(separatorIndex, 0, { 
      label: 'separator', 
      action: () => {}, 
      disabled: false, 
      shortcut: '' 
    });
  }

  return (
    <div
      ref={menuRef}
      className="tab-context-menu"
      style={{
        left: position.x,
        top: position.y
      }}
    >
      {hasUnsavedChanges && (
        <div className="tab-context-menu-warning">
          <span className="warning-icon">⚠️</span>
          <span>This tab has unsaved changes</span>
        </div>
      )}
      
      {menuItems.map((item, index) => {
        if (item.label === 'separator') {
          return <div key={index} className="tab-context-menu-separator" />;
        }

        return (
          <div
            key={index}
            className={`tab-context-menu-item ${item.disabled ? 'disabled' : ''} ${item.dangerous ? 'dangerous' : ''}`}
            onClick={item.disabled ? undefined : () => handleMenuAction(item.action)}
          >
            <span className="menu-item-label">{item.label}</span>
            {item.shortcut && (
              <span className="menu-item-shortcut">{item.shortcut}</span>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default TabContextMenu;
