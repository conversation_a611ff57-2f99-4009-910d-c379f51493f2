<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workspace Switching Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .tabs {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .tab {
            padding: 8px 16px;
            background: #e0e0e0;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .tab.active {
            background: #007cba;
            color: white;
        }
        .collections {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Workspace Switching Test</h1>
        <p>This test simulates the workspace switching functionality to verify that tabs are closed and collections are reloaded.</p>
        
        <div class="section">
            <h3>Current Workspace</h3>
            <div id="currentWorkspace">Workspace A</div>
        </div>

        <div class="section">
            <h3>Open Tabs</h3>
            <div class="tabs" id="tabsContainer">
                <div class="tab active">GET /api/users</div>
                <div class="tab">POST /api/login</div>
                <div class="tab">PUT /api/profile</div>
            </div>
            <button onclick="addTab()">Add Tab</button>
        </div>

        <div class="section">
            <h3>Collections Panel</h3>
            <div class="collections" id="collectionsPanel">
                <div>📁 User Management</div>
                <div>📁 Authentication</div>
                <div>📁 Profile Settings</div>
            </div>
        </div>

        <div class="section">
            <h3>Workspace Switcher</h3>
            <button onclick="switchWorkspace('Workspace A')">Switch to Workspace A</button>
            <button onclick="switchWorkspace('Workspace B')">Switch to Workspace B</button>
            <button onclick="switchWorkspace('Workspace C')">Switch to Workspace C</button>
        </div>

        <div class="section">
            <h3>Event Log</h3>
            <div class="log" id="eventLog"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let currentWorkspace = 'Workspace A';
        let tabCounter = 1;

        // Mock workspace data
        const workspaceData = {
            'Workspace A': {
                collections: ['📁 User Management', '📁 Authentication', '📁 Profile Settings']
            },
            'Workspace B': {
                collections: ['📁 Orders', '📁 Products', '📁 Inventory']
            },
            'Workspace C': {
                collections: ['📁 Reports', '📁 Analytics', '📁 Dashboard']
            }
        };

        function log(message) {
            const logElement = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        function addTab() {
            const tabsContainer = document.getElementById('tabsContainer');
            const newTab = document.createElement('div');
            newTab.className = 'tab';
            newTab.textContent = `GET /api/test${tabCounter++}`;
            tabsContainer.appendChild(newTab);
            log(`Added new tab: ${newTab.textContent}`);
        }

        function closeAllTabs() {
            const tabsContainer = document.getElementById('tabsContainer');
            tabsContainer.innerHTML = '';
            log('🗂️ All tabs closed');
        }

        function reloadCollections(workspace) {
            const collectionsPanel = document.getElementById('collectionsPanel');
            const collections = workspaceData[workspace].collections;
            collectionsPanel.innerHTML = collections.map(collection => `<div>${collection}</div>`).join('');
            log(`📁 Collections reloaded for ${workspace}`);
        }

        function switchWorkspace(newWorkspace) {
            if (newWorkspace === currentWorkspace) {
                log(`Already in ${newWorkspace}`);
                return;
            }

            log(`🔄 Switching from ${currentWorkspace} to ${newWorkspace}`);
            
            // Update current workspace
            currentWorkspace = newWorkspace;
            document.getElementById('currentWorkspace').textContent = newWorkspace;
            
            // Simulate the workspace-switched event
            log('📡 Dispatching workspace-switched event');
            
            // Close all tabs (simulating the App component listener)
            closeAllTabs();
            
            // Reload collections (simulating the CollectionsPanel listener)
            reloadCollections(newWorkspace);
            
            log(`✅ Workspace switch to ${newWorkspace} completed`);
        }

        // Initialize
        log('🚀 Test environment initialized');
        log('📝 Current workspace: ' + currentWorkspace);
        log('📋 Open tabs: 3');
        log('📁 Collections loaded for ' + currentWorkspace);
    </script>
</body>
</html>
