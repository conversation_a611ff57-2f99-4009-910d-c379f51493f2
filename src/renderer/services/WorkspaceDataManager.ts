import { WorkspaceData } from '../types/workspace';
import { generateSeedData } from '../data/seedData';

/**
 * Manages workspace data (collections, folders, requests) for the current active workspace
 * This service bridges the gap between workspace management and collections data
 */
export class WorkspaceDataManager {
  private static readonly WORKSPACE_DATA_KEY = 'apicool-workspace-data';
  
  /**
   * Get workspace data for a specific workspace ID
   */
  static getWorkspaceData(workspaceId: string): WorkspaceData | null {
    try {
      const allWorkspaceData = this.getAllWorkspaceData();
      return allWorkspaceData[workspaceId] || null;
    } catch (error) {
      console.error('Failed to get workspace data:', error);
      return null;
    }
  }

  /**
   * Get current active workspace data
   */
  static getCurrentWorkspaceData(): WorkspaceData {
    try {
      // For now, we'll use the legacy localStorage key for backward compatibility
      const savedWorkspace = localStorage.getItem('apicool-workspace');
      if (savedWorkspace) {
        const parsed = JSON.parse(savedWorkspace);
        // Convert date strings back to Date objects
        parsed.items = parsed.items.map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        }));
        return parsed;
      }
    } catch (error) {
      console.error('Failed to load current workspace data:', error);
    }
    
    // Return seed data if no workspace data exists
    return generateSeedData();
  }

  /**
   * Save workspace data for a specific workspace
   */
  static saveWorkspaceData(workspaceId: string, data: WorkspaceData): void {
    try {
      const allWorkspaceData = this.getAllWorkspaceData();
      allWorkspaceData[workspaceId] = data;
      localStorage.setItem(this.WORKSPACE_DATA_KEY, JSON.stringify(allWorkspaceData));
      
      // Also update the legacy key for backward compatibility
      localStorage.setItem('apicool-workspace', JSON.stringify(data));
      
      // Dispatch event for components to listen to
      window.dispatchEvent(new CustomEvent('workspace-data-updated', {
        detail: { workspaceId, data }
      }));
    } catch (error) {
      console.error('Failed to save workspace data:', error);
    }
  }

  /**
   * Create workspace data for a new workspace
   * New workspaces get seed data to start with some example collections
   */
  static createWorkspaceData(workspaceId: string, workspaceName: string): WorkspaceData {
    // Generate seed data for new workspaces so they have example collections
    const seedData = generateSeedData();
    const workspaceData: WorkspaceData = {
      id: workspaceId,
      name: workspaceName,
      items: seedData.items
    };

    this.saveWorkspaceData(workspaceId, workspaceData);
    return workspaceData;
  }

  /**
   * Switch to a different workspace and load its data
   */
  static switchToWorkspace(workspaceId: string, workspaceName: string): WorkspaceData {
    let workspaceData = this.getWorkspaceData(workspaceId);

    if (!workspaceData) {
      // Create workspace data if it doesn't exist
      workspaceData = this.createWorkspaceData(workspaceId, workspaceName);
    }

    // Update the legacy localStorage key for backward compatibility
    localStorage.setItem('apicool-workspace', JSON.stringify(workspaceData));

    // Dispatch event for components to listen to
    window.dispatchEvent(new CustomEvent('workspace-switched', {
      detail: { workspaceId, workspaceName, data: workspaceData }
    }));

    return workspaceData;
  }

  /**
   * Copy data from one workspace to another
   */
  static copyWorkspaceData(fromWorkspaceId: string, toWorkspaceId: string): boolean {
    try {
      const sourceData = this.getWorkspaceData(fromWorkspaceId);
      if (!sourceData) return false;
      
      // Create a deep copy of the data with new IDs
      const copiedData: WorkspaceData = {
        ...sourceData,
        id: toWorkspaceId,
        items: sourceData.items.map(item => ({
          ...item,
          id: `${item.id}-copy-${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }))
      };
      
      this.saveWorkspaceData(toWorkspaceId, copiedData);
      return true;
    } catch (error) {
      console.error('Failed to copy workspace data:', error);
      return false;
    }
  }

  /**
   * Delete workspace data
   */
  static deleteWorkspaceData(workspaceId: string): void {
    try {
      const allWorkspaceData = this.getAllWorkspaceData();
      delete allWorkspaceData[workspaceId];
      localStorage.setItem(this.WORKSPACE_DATA_KEY, JSON.stringify(allWorkspaceData));
      
      // Dispatch event
      window.dispatchEvent(new CustomEvent('workspace-data-deleted', {
        detail: { workspaceId }
      }));
    } catch (error) {
      console.error('Failed to delete workspace data:', error);
    }
  }

  /**
   * Get all workspace data
   */
  private static getAllWorkspaceData(): Record<string, WorkspaceData> {
    try {
      const stored = localStorage.getItem(this.WORKSPACE_DATA_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to load all workspace data:', error);
      return {};
    }
  }

  /**
   * Initialize workspace data management
   * This should be called when the app starts
   */
  static initialize(): void {
    // Listen for workspace changes and switch data accordingly
    window.addEventListener('workspace-changed', ((event: CustomEvent) => {
      const { workspace } = event.detail;
      this.switchToWorkspace(workspace.id, workspace.name);
    }) as EventListener);

    // Listen for workspace creation and create workspace data
    window.addEventListener('workspace-created', ((event: CustomEvent) => {
      const { workspace } = event.detail;
      this.createWorkspaceData(workspace.id, workspace.name);
    }) as EventListener);

    // Listen for workspace deletion and clean up data
    window.addEventListener('workspace-deleted', ((event: CustomEvent) => {
      const { workspaceId } = event.detail;
      this.deleteWorkspaceData(workspaceId);
    }) as EventListener);
  }
}

export default WorkspaceDataManager;
