// Import/Export type definitions

export interface ImportResult {
  success: boolean;
  collections: ImportedCollection[];
  errors: string[];
  warnings: string[];
}

export interface ImportedCollection {
  id: string;
  name: string;
  description?: string;
  version?: string;
  folders: ImportedFolder[];
  requests: ImportedRequest[];
  variables: Record<string, string>;
  auth?: ImportedAuth;
}

export interface ImportedFolder {
  id: string;
  name: string;
  description?: string;
  requests: ImportedRequest[];
  subfolders: ImportedFolder[];
  auth?: ImportedAuth;
  variables: Record<string, string>;
}

export interface ImportedRequest {
  id: string;
  name: string;
  description?: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  params: Record<string, string>;
  body?: string;
  auth?: ImportedAuth;
  tests?: string;
  preRequestScript?: string;
  examples?: ImportedExample[];
}

export interface ImportedExample {
  id: string;
  name: string;
  description?: string;
  request: {
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: string;
  };
  response: {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    body: string;
    responseTime?: number;
  };
}

export interface ImportedAuth {
  type: 'none' | 'bearer' | 'basic' | 'api-key' | 'oauth2';
  bearer?: {
    token: string;
  };
  basic?: {
    username: string;
    password: string;
  };
  apikey?: {
    key: string;
    value: string;
    in: 'header' | 'query';
  };
  oauth2?: {
    accessToken: string;
    tokenType?: string;
  };
}

// Postman format interfaces
export interface PostmanCollection {
  info: {
    name: string;
    description?: string;
    version?: string;
    schema: string;
  };
  item: PostmanItem[];
  variable?: PostmanVariable[];
  auth?: PostmanAuth;
}

export interface PostmanItem {
  name: string;
  description?: string;
  item?: PostmanItem[]; // For folders
  request?: PostmanRequest;
  response?: PostmanResponse[];
}

export interface PostmanRequest {
  method: string;
  header?: PostmanHeader[];
  url: PostmanUrl | string;
  body?: PostmanBody;
  auth?: PostmanAuth;
  description?: string;
}

export interface PostmanResponse {
  name: string;
  originalRequest: PostmanRequest;
  status: string;
  code: number;
  header: PostmanHeader[];
  body: string;
  _postman_previewlanguage?: string;
}

export interface PostmanHeader {
  key: string;
  value: string;
  disabled?: boolean;
  description?: string;
}

export interface PostmanUrl {
  raw: string;
  protocol?: string;
  host?: string[];
  port?: string;
  path?: string[];
  query?: PostmanQuery[];
}

export interface PostmanQuery {
  key: string;
  value: string;
  disabled?: boolean;
  description?: string;
}

export interface PostmanBody {
  mode: 'raw' | 'formdata' | 'urlencoded' | 'binary' | 'graphql';
  raw?: string;
  formdata?: PostmanFormData[];
  urlencoded?: PostmanUrlEncoded[];
  options?: {
    raw?: {
      language: string;
    };
  };
}

export interface PostmanFormData {
  key: string;
  value: string;
  type: 'text' | 'file';
  disabled?: boolean;
  description?: string;
}

export interface PostmanUrlEncoded {
  key: string;
  value: string;
  disabled?: boolean;
  description?: string;
}

export interface PostmanVariable {
  key: string;
  value: string;
  type?: string;
  disabled?: boolean;
  description?: string;
}

export interface PostmanAuth {
  type: string;
  bearer?: PostmanBearer[];
  basic?: PostmanBasic[];
  apikey?: PostmanApiKey[];
  oauth2?: PostmanOAuth2[];
}

export interface PostmanBearer {
  key: 'token';
  value: string;
  type: 'string';
}

export interface PostmanBasic {
  key: 'username' | 'password';
  value: string;
  type: 'string';
}

export interface PostmanApiKey {
  key: 'key' | 'value' | 'in';
  value: string;
  type: 'string';
}

export interface PostmanOAuth2 {
  key: string;
  value: string;
  type: 'string';
}

// OpenAPI/Swagger format interfaces
export interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  servers?: OpenAPIServer[];
  paths: Record<string, OpenAPIPath>;
  components?: {
    schemas?: Record<string, any>;
    securitySchemes?: Record<string, OpenAPISecurityScheme>;
  };
  security?: OpenAPISecurity[];
}

export interface OpenAPIServer {
  url: string;
  description?: string;
  variables?: Record<string, OpenAPIServerVariable>;
}

export interface OpenAPIServerVariable {
  default: string;
  description?: string;
  enum?: string[];
}

export interface OpenAPIPath {
  get?: OpenAPIOperation;
  post?: OpenAPIOperation;
  put?: OpenAPIOperation;
  delete?: OpenAPIOperation;
  patch?: OpenAPIOperation;
  head?: OpenAPIOperation;
  options?: OpenAPIOperation;
}

export interface OpenAPIOperation {
  summary?: string;
  description?: string;
  operationId?: string;
  parameters?: OpenAPIParameter[];
  requestBody?: OpenAPIRequestBody;
  responses: Record<string, OpenAPIResponse>;
  security?: OpenAPISecurity[];
  tags?: string[];
}

export interface OpenAPIParameter {
  name: string;
  in: 'query' | 'header' | 'path' | 'cookie';
  required?: boolean;
  description?: string;
  schema: any;
  example?: any;
}

export interface OpenAPIRequestBody {
  description?: string;
  required?: boolean;
  content: Record<string, OpenAPIMediaType>;
}

export interface OpenAPIMediaType {
  schema: any;
  example?: any;
  examples?: Record<string, OpenAPIExample>;
}

export interface OpenAPIExample {
  summary?: string;
  description?: string;
  value: any;
}

export interface OpenAPIResponse {
  description: string;
  headers?: Record<string, OpenAPIHeader>;
  content?: Record<string, OpenAPIMediaType>;
}

export interface OpenAPIHeader {
  description?: string;
  required?: boolean;
  schema: any;
  example?: any;
}

export interface OpenAPISecurityScheme {
  type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
  description?: string;
  name?: string;
  in?: 'query' | 'header' | 'cookie';
  scheme?: string;
  bearerFormat?: string;
  flows?: any;
  openIdConnectUrl?: string;
}

export interface OpenAPISecurity {
  [key: string]: string[];
}

// Export format
export interface ExportOptions {
  format: 'postman' | 'openapi' | 'insomnia' | 'har';
  includeExamples: boolean;
  includeTests: boolean;
  includeVariables: boolean;
  includeAuth: boolean;
}

export interface ExportResult {
  success: boolean;
  data?: any;
  filename: string;
  error?: string;
}
