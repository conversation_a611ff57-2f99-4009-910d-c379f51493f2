import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define IPC channels directly to avoid import issues
const IPC_CHANNELS = {
  // Workspace operations
  WORKSPACE_GET_ALL: 'workspace:getAll',
  WORKSPACE_CREATE: 'workspace:create',
  WORKSPACE_UPDATE: 'workspace:update',
  WORKSPACE_DELETE: 'workspace:delete',
  WORKSPACE_GET_STATS: 'workspace:getStats',
  WORKSPACE_GET_ITEMS_LIGHT: 'workspace:getItemsLight',
  WORKSPACE_GET_ITEM_DETAILS: 'workspace:getItemDetails',
  WORKSPACE_GET_COLLECTION_DETAILS: 'workspace:getCollectionDetails',
  WORKSPACE_GET_FOLDER_DETAILS: 'workspace:getFolderDetails',
  WORKSPACE_GET_REQUEST_DETAILS: 'workspace:getRequestDetails',
  WORKSPACE_GET_EXAMPLE_DETAILS: 'workspace:getExampleDeta<PERSON>',
  WORKSPACE_CREATE_EXAMPLE: 'workspace:createExample',
  WORKSPACE_CREATE_COLLECTION: 'workspace:createCollection',
  WORKSPACE_UPDATE_COLLECTION: 'workspace:updateCollection',
  WORKSPACE_CREATE_FOLDER: 'workspace:createFolder',
  WORKSPACE_UPDATE_FOLDER: 'workspace:updateFolder',
  WORKSPACE_CREATE_REQUEST: 'workspace:createRequest',
  WORKSPACE_UPDATE_REQUEST: 'workspace:updateRequest',
  WORKSPACE_ADD_ITEM_TO_HIERARCHY: 'workspace:addItemToHierarchy',
  WORKSPACE_DELETE_ITEM: 'workspace:deleteItem',
  WORKSPACE_UPDATE_ITEM_HIERARCHY: 'workspace:updateItemHierarchy',
  WORKSPACE_REORDER_ITEMS: 'workspace:reorderItems',

  // Environment channels
  ENVIRONMENT_GET_ALL: 'environment:getAll',
  ENVIRONMENT_GET_ACTIVE: 'environment:getActive',
  ENVIRONMENT_CREATE: 'environment:create',
  ENVIRONMENT_UPDATE: 'environment:update',
  ENVIRONMENT_SET_ACTIVE: 'environment:setActive',
  ENVIRONMENT_DELETE: 'environment:delete',
  ENVIRONMENT_DUPLICATE: 'environment:duplicate',

  // History channels
  HISTORY_GET_ALL: 'history:getAll',
  HISTORY_GET_ENTRY: 'history:getEntry',
  HISTORY_CREATE: 'history:create',
  HISTORY_DELETE: 'history:delete',
  HISTORY_CLEAR: 'history:clear',
  HISTORY_GET_STATS: 'history:getStats',
  HISTORY_SEARCH: 'history:search',

  // Hierarchy operations
  HIERARCHY_GET_ITEMS: 'hierarchy:getItems',
  HIERARCHY_UPDATE_ITEM: 'hierarchy:updateItem',
  HIERARCHY_CREATE_ITEM: 'hierarchy:createItem',
  HIERARCHY_DELETE_ITEM: 'hierarchy:deleteItem',

  // Request operations
  REQUEST_EXECUTE: 'request:execute',
  REQUEST_SAVE: 'request:save',
  REQUEST_GET: 'request:get',
  REQUEST_DELETE: 'request:delete',
  REQUEST_DUPLICATE: 'request:duplicate',

  // Collection operations
  COLLECTION_IMPORT: 'collection:import',
  COLLECTION_EXPORT: 'collection:export',
  COLLECTION_RUN: 'collection:run',
  COLLECTION_CREATE: 'collection:create',
  COLLECTION_DELETE: 'collection:delete',

  // AI operations
  AI_GENERATE_REQUEST: 'ai:generateRequest',
  AI_SET_PROVIDER: 'ai:setProvider',
  AI_GET_PROVIDERS: 'ai:getProviders',
  AI_TEST_PROVIDER: 'ai:testProvider',

  // Authentication operations
  AUTH_LOGIN: 'auth:login',
  AUTH_LOGOUT: 'auth:logout',
  AUTH_REGISTER: 'auth:register',
  AUTH_GET_CURRENT_USER: 'auth:getCurrentUser',
  AUTH_CHANGE_PASSWORD: 'auth:changePassword',

  // Theme operations
  THEME_GET: 'theme:get',
  THEME_SET: 'theme:set',
  THEME_GET_VARIABLES: 'theme:getVariables',

  // Internationalization operations
  I18N_GET_LANGUAGE: 'i18n:getLanguage',
  I18N_SET_LANGUAGE: 'i18n:setLanguage',
  I18N_GET_TRANSLATIONS: 'i18n:getTranslations',
  I18N_GET_SUPPORTED_LANGUAGES: 'i18n:getSupportedLanguages',

  // Backup operations
  BACKUP_CREATE: 'backup:create',
  BACKUP_RESTORE: 'backup:restore',
  BACKUP_LIST: 'backup:list',
  BACKUP_CONFIGURE: 'backup:configure',

  // Logging operations
  LOG_GET_LOGS: 'log:getLogs',
  LOG_SET_LEVEL: 'log:setLevel',
  LOG_CLEAR: 'log:clear',

  // File operations
  FILE_OPEN_DIALOG: 'file:openDialog',
  FILE_SAVE_DIALOG: 'file:saveDialog',
  FILE_READ: 'file:read',
  FILE_WRITE: 'file:write',

  // Window operations
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_CLOSE: 'window:close',
  WINDOW_TOGGLE_DEVTOOLS: 'window:toggleDevTools'
} as const;

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Workspace operations
  workspace: {
    getAll: () => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_ALL),
    create: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_CREATE, data),
    update: (id: number, data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_UPDATE, id, data),
    delete: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_DELETE, id),
    getStats: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_STATS, id),
    getItemsLight: (workspaceId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_ITEMS_LIGHT, workspaceId),
    getItemDetails: (itemId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_ITEM_DETAILS, itemId),
    getCollectionDetails: (collectionId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_COLLECTION_DETAILS, collectionId),
    getFolderDetails: (folderId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_FOLDER_DETAILS, folderId),
    getRequestDetails: (requestId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_REQUEST_DETAILS, requestId),
    getExampleDetails: (exampleId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_GET_EXAMPLE_DETAILS, exampleId),
    createExample: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_CREATE_EXAMPLE, data),
    createCollection: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_CREATE_COLLECTION, data),
    updateCollection: (collectionId: number, data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_UPDATE_COLLECTION, collectionId, data),
    createFolder: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_CREATE_FOLDER, data),
    updateFolder: (folderId: number, data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_UPDATE_FOLDER, folderId, data),
    createRequest: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_CREATE_REQUEST, data),
    updateRequest: (requestId: number, data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_UPDATE_REQUEST, requestId, data),
    addItemToHierarchy: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_ADD_ITEM_TO_HIERARCHY, data),
    deleteItem: (itemId: number) => ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_DELETE_ITEM, itemId),
    updateItemHierarchy: (itemId: number, newParentId: number | null, newOrder: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_UPDATE_ITEM_HIERARCHY, itemId, newParentId, newOrder),
    reorderItems: (items: { id: number; order: number }[]) =>
      ipcRenderer.invoke(IPC_CHANNELS.WORKSPACE_REORDER_ITEMS, items)
  },

  environment: {
    getAll: (workspaceId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_GET_ALL, workspaceId),
    getActive: (workspaceId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_GET_ACTIVE, workspaceId),
    create: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_CREATE, data),
    update: (id: number, data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_UPDATE, id, data),
    setActive: (id: number, workspaceId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_SET_ACTIVE, id, workspaceId),
    delete: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_DELETE, id),
    duplicate: (id: number, newName: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.ENVIRONMENT_DUPLICATE, id, newName)
  },

  history: {
    getAll: (workspaceId: number, limit?: number, offset?: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_GET_ALL, workspaceId, limit, offset),
    getEntry: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_GET_ENTRY, id),
    create: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_CREATE, data),
    delete: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_DELETE, id),
    clear: (workspaceId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_CLEAR, workspaceId),
    getStats: (workspaceId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_GET_STATS, workspaceId),
    search: (workspaceId: number, query: string, limit?: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.HISTORY_SEARCH, workspaceId, query, limit)
  },

  // AI operations
  ai: {
    sendMessage: (request: any) =>
      ipcRenderer.invoke('ai:sendMessage', request),
    getProviders: () =>
      ipcRenderer.invoke('ai:getProviders'),
    getProvider: (id: string) =>
      ipcRenderer.invoke('ai:getProvider', id),
    updateProvider: (provider: any) =>
      ipcRenderer.invoke('ai:updateProvider', provider),
    testConnection: (provider: any) =>
      ipcRenderer.invoke('ai:testConnection', provider),
    fetchModels: (providerConfig: any) =>
      ipcRenderer.invoke('ai:fetchModels', providerConfig),
    reloadProviders: () =>
      ipcRenderer.invoke('ai:reloadProviders'),
    sendStreamMessage: (request: any) =>
      ipcRenderer.invoke('ai:sendStreamMessage', request),
    onStreamChunk: (callback: (chunk: any) => void) => {
      ipcRenderer.on('ai:streamChunk', (event, chunk) => callback(chunk));
    },
    removeStreamChunkListener: () => {
      ipcRenderer.removeAllListeners('ai:streamChunk');
    },
    generateRequest: (prompt: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.AI_GENERATE_REQUEST, prompt),
    setProvider: (provider: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.AI_SET_PROVIDER, provider),
    testProvider: (provider: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.AI_TEST_PROVIDER, provider)
  },

  // Chat operations
  chat: {
    getSessions: (workspaceId: number) =>
      ipcRenderer.invoke('chat:getSessions', workspaceId),
    getSession: (sessionId: number) =>
      ipcRenderer.invoke('chat:getSession', sessionId),
    createSession: (data: any) =>
      ipcRenderer.invoke('chat:createSession', data),
    updateSession: (sessionId: number, title: string) =>
      ipcRenderer.invoke('chat:updateSession', sessionId, title),
    deleteSession: (sessionId: number) =>
      ipcRenderer.invoke('chat:deleteSession', sessionId),
    addMessage: (data: any) =>
      ipcRenderer.invoke('chat:addMessage', data),
    updateMessage: (messageId: number, data: any) =>
      ipcRenderer.invoke('chat:updateMessage', messageId, data),
    deleteMessage: (messageId: number) =>
      ipcRenderer.invoke('chat:deleteMessage', messageId),
    getMessages: (sessionId: number) =>
      ipcRenderer.invoke('chat:getMessages', sessionId),
    clearSession: (sessionId: number) =>
      ipcRenderer.invoke('chat:clearSession', sessionId)
  },

  // Settings operations
  settings: {
    getAll: () =>
      ipcRenderer.invoke('settings:getAll'),
    get: (key: string) =>
      ipcRenderer.invoke('settings:get', key),
    update: (key: string, value: any) =>
      ipcRenderer.invoke('settings:update', key, value),
    updateMultiple: (settings: any) =>
      ipcRenderer.invoke('settings:updateMultiple', settings),
    reset: () =>
      ipcRenderer.invoke('settings:reset'),
    getTheme: () =>
      ipcRenderer.invoke('settings:getTheme'),
    updateTheme: (theme: string) =>
      ipcRenderer.invoke('settings:updateTheme', theme),
    getAI: () =>
      ipcRenderer.invoke('settings:getAI'),
    updateAI: (aiSettings: any) =>
      ipcRenderer.invoke('settings:updateAI', aiSettings),
    updateAIProvider: (provider: any) =>
      ipcRenderer.invoke('settings:updateAIProvider', provider),
    removeAIProvider: (providerId: string) =>
      ipcRenderer.invoke('settings:removeAIProvider', providerId),
    export: () =>
      ipcRenderer.invoke('settings:export'),
    import: (settings: any) =>
      ipcRenderer.invoke('settings:import', settings)
  },

  // Hierarchy operations
  hierarchy: {
    getItems: (workspaceId: number) => ipcRenderer.invoke(IPC_CHANNELS.HIERARCHY_GET_ITEMS, workspaceId),
    updateItem: (itemId: number, parentId: number | null, order: number) => 
      ipcRenderer.invoke(IPC_CHANNELS.HIERARCHY_UPDATE_ITEM, itemId, parentId, order),
    createItem: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.HIERARCHY_CREATE_ITEM, data),
    deleteItem: (itemId: number) => ipcRenderer.invoke(IPC_CHANNELS.HIERARCHY_DELETE_ITEM, itemId)
  },

  // Request operations
  request: {
    execute: (requestData: any, environmentVariables?: any, collectionVariables?: any, workspaceId?: number, environmentName?: string) =>
      ipcRenderer.invoke('request:execute', requestData, environmentVariables, collectionVariables, workspaceId, environmentName),
    save: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.REQUEST_SAVE, data),
    get: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.REQUEST_GET, id),
    delete: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.REQUEST_DELETE, id),
    duplicate: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.REQUEST_DUPLICATE, id),
    getVariableState: () => ipcRenderer.invoke('request:getVariableState'),
    setTimeout: (timeout: number) => ipcRenderer.invoke('request:setTimeout', timeout)
  },

  // Collection operations
  collection: {
    import: (filePath: string) => ipcRenderer.invoke(IPC_CHANNELS.COLLECTION_IMPORT, filePath),
    export: (collectionId: number, filePath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.COLLECTION_EXPORT, collectionId, filePath),
    run: (requests: any[], environmentVariables?: any, collectionVariables?: any, options?: any) =>
      ipcRenderer.invoke('collection:run', requests, environmentVariables, collectionVariables, options),
    create: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.COLLECTION_CREATE, data),
    delete: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.COLLECTION_DELETE, id)
  },



  // Authentication operations
  auth: {
    login: (credentials: any) => ipcRenderer.invoke(IPC_CHANNELS.AUTH_LOGIN, credentials),
    logout: () => ipcRenderer.invoke(IPC_CHANNELS.AUTH_LOGOUT),
    register: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.AUTH_REGISTER, data),
    getCurrentUser: () => ipcRenderer.invoke(IPC_CHANNELS.AUTH_GET_CURRENT_USER),
    changePassword: (oldPassword: string, newPassword: string) => 
      ipcRenderer.invoke(IPC_CHANNELS.AUTH_CHANGE_PASSWORD, oldPassword, newPassword)
  },

  // Theme operations
  theme: {
    get: () => ipcRenderer.invoke(IPC_CHANNELS.THEME_GET),
    set: (theme: any) => ipcRenderer.invoke(IPC_CHANNELS.THEME_SET, theme),
    getVariables: () => ipcRenderer.invoke(IPC_CHANNELS.THEME_GET_VARIABLES)
  },

  // Internationalization operations
  i18n: {
    getLanguage: () => ipcRenderer.invoke(IPC_CHANNELS.I18N_GET_LANGUAGE),
    setLanguage: (language: string) => ipcRenderer.invoke(IPC_CHANNELS.I18N_SET_LANGUAGE, language),
    getTranslations: (language?: string) => ipcRenderer.invoke(IPC_CHANNELS.I18N_GET_TRANSLATIONS, language),
    getSupportedLanguages: () => ipcRenderer.invoke(IPC_CHANNELS.I18N_GET_SUPPORTED_LANGUAGES)
  },

  // Backup operations
  backup: {
    create: () => ipcRenderer.invoke(IPC_CHANNELS.BACKUP_CREATE),
    restore: (filePath: string) => ipcRenderer.invoke(IPC_CHANNELS.BACKUP_RESTORE, filePath),
    list: () => ipcRenderer.invoke(IPC_CHANNELS.BACKUP_LIST),
    configure: (config: any) => ipcRenderer.invoke(IPC_CHANNELS.BACKUP_CONFIGURE, config)
  },

  // Logging operations
  log: {
    getLogs: (level?: string, limit?: number) => ipcRenderer.invoke(IPC_CHANNELS.LOG_GET_LOGS, level, limit),
    setLevel: (level: string) => ipcRenderer.invoke(IPC_CHANNELS.LOG_SET_LEVEL, level),
    clear: () => ipcRenderer.invoke(IPC_CHANNELS.LOG_CLEAR)
  },

  // File operations
  file: {
    openDialog: (options: any) => ipcRenderer.invoke(IPC_CHANNELS.FILE_OPEN_DIALOG, options),
    saveDialog: (options: any) => ipcRenderer.invoke(IPC_CHANNELS.FILE_SAVE_DIALOG, options),
    read: (filePath: string) => ipcRenderer.invoke(IPC_CHANNELS.FILE_READ, filePath),
    write: (filePath: string, data: any) => ipcRenderer.invoke(IPC_CHANNELS.FILE_WRITE, filePath, data)
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE),
    maximize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE),
    close: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE),
    toggleDevTools: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS)
  },

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, callback);
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  // One-time event listeners
  once: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.once(channel, callback);
  },

  // Menu event handling
  onMenuEvent: (channel: string, callback: () => void) => {
    const validChannels = [
      'menu:new-request',
      'menu:new-collection',
      'menu:import-postman',
      'menu:import-openapi',
      'menu:export-collection',
      'menu:settings',
      'menu:view-collections',
      'menu:view-environments',
      'menu:view-history',
      'menu:about',
      'menu:documentation',
      'menu:shortcuts'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  removeMenuEvent: (channel: string, callback: () => void) => {
    const validChannels = [
      'menu:new-request',
      'menu:new-collection',
      'menu:import-postman',
      'menu:import-openapi',
      'menu:export-collection',
      'menu:settings',
      'menu:view-collections',
      'menu:view-environments',
      'menu:view-history',
      'menu:about',
      'menu:documentation',
      'menu:shortcuts'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback);
    }
  }
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type definitions for TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}
