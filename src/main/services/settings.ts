import { PrismaClient } from '@prisma/client';
import { AIProvider } from './ai-service';

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  ai: {
    defaultProvider: string;
    providers: AIProvider[];
    defaultModel: string;
    temperature: number;
    maxTokens: number;
  };
  general: {
    autoSave: boolean;
    confirmDelete: boolean;
    showLineNumbers: boolean;
    wordWrap: boolean;
  };
  network: {
    timeout: number;
    followRedirects: boolean;
    validateSSL: boolean;
  };
}

export class SettingsService {
  private prisma: PrismaClient;
  private defaultSettings: AppSettings;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.defaultSettings = {
      theme: 'auto',
      ai: {
        defaultProvider: 'openai',
        providers: [
          {
            id: 'openai',
            name: 'OpenAI',
            type: 'openai',
            baseUrl: 'https://api.openai.com/v1',
            models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4-vision-preview']
          },
          {
            id: 'ollama',
            name: '<PERSON><PERSON><PERSON> (Local)',
            type: 'ollama',
            baseUrl: 'http://localhost:11434',
            models: ['llama2', 'codellama', 'mistral', 'llama2:13b', 'llama2:70b']
          },
          {
            id: 'gemini',
            name: 'Google Gemini',
            type: 'gemini',
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
            models: ['gemini-pro', 'gemini-pro-vision']
          },
          {
            id: 'anthropic',
            name: 'Anthropic Claude',
            type: 'anthropic',
            baseUrl: 'https://api.anthropic.com/v1',
            models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307']
          }
        ],
        defaultModel: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2048
      },
      general: {
        autoSave: true,
        confirmDelete: true,
        showLineNumbers: true,
        wordWrap: false
      },
      network: {
        timeout: 30000,
        followRedirects: true,
        validateSSL: true
      }
    };
  }

  /**
   * Get all settings
   */
  async getSettings(): Promise<AppSettings> {
    try {
      const settings = await this.prisma.appSettings.findMany();
      
      if (settings.length === 0) {
        // Initialize with default settings
        await this.initializeDefaultSettings();
        return this.defaultSettings;
      }

      // Merge stored settings with defaults
      const result = { ...this.defaultSettings };
      
      for (const setting of settings) {
        this.setNestedValue(result, setting.key, setting.value);
      }

      return result;
    } catch (error) {
      console.error('Error fetching settings:', error);
      return this.defaultSettings;
    }
  }

  /**
   * Get a specific setting value
   */
  async getSetting(key: string): Promise<any> {
    try {
      const setting = await this.prisma.appSettings.findUnique({
        where: { key }
      });

      if (!setting) {
        return this.getNestedValue(this.defaultSettings, key);
      }

      return setting.value;
    } catch (error) {
      console.error('Error fetching setting:', error);
      return this.getNestedValue(this.defaultSettings, key);
    }
  }

  /**
   * Update a setting
   */
  async updateSetting(key: string, value: any): Promise<void> {
    try {
      await this.prisma.appSettings.upsert({
        where: { key },
        update: { value },
        create: { key, value }
      });
    } catch (error) {
      console.error('Error updating setting:', error);
      throw new Error('Failed to update setting');
    }
  }

  /**
   * Update multiple settings
   */
  async updateSettings(settings: Partial<AppSettings>): Promise<void> {
    try {
      const flatSettings = this.flattenObject(settings);
      
      for (const [key, value] of Object.entries(flatSettings)) {
        await this.updateSetting(key, value);
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      throw new Error('Failed to update settings');
    }
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings(): Promise<void> {
    try {
      await this.prisma.appSettings.deleteMany();
      await this.initializeDefaultSettings();
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw new Error('Failed to reset settings');
    }
  }

  /**
   * Get theme setting
   */
  async getTheme(): Promise<'light' | 'dark' | 'auto'> {
    return await this.getSetting('theme') || 'auto';
  }

  /**
   * Update theme setting
   */
  async updateTheme(theme: 'light' | 'dark' | 'auto'): Promise<void> {
    await this.updateSetting('theme', theme);
  }

  /**
   * Get AI settings
   */
  async getAISettings(): Promise<AppSettings['ai']> {
    const settings = await this.getSettings();
    return settings.ai;
  }

  /**
   * Update AI settings
   */
  async updateAISettings(aiSettings: Partial<AppSettings['ai']>): Promise<void> {
    const flatSettings = this.flattenObject({ ai: aiSettings }, 'ai');
    
    for (const [key, value] of Object.entries(flatSettings)) {
      await this.updateSetting(key, value);
    }
  }

  /**
   * Add or update AI provider
   */
  async updateAIProvider(provider: AIProvider): Promise<void> {
    const settings = await this.getAISettings();
    const existingIndex = settings.providers.findIndex(p => p.id === provider.id);
    
    if (existingIndex >= 0) {
      settings.providers[existingIndex] = provider;
    } else {
      settings.providers.push(provider);
    }
    
    await this.updateSetting('ai.providers', settings.providers);
  }

  /**
   * Remove AI provider
   */
  async removeAIProvider(providerId: string): Promise<void> {
    const settings = await this.getAISettings();
    settings.providers = settings.providers.filter(p => p.id !== providerId);
    await this.updateSetting('ai.providers', settings.providers);
  }

  private async initializeDefaultSettings(): Promise<void> {
    const flatSettings = this.flattenObject(this.defaultSettings);
    
    for (const [key, value] of Object.entries(flatSettings)) {
      await this.prisma.appSettings.create({
        data: { key, value }
      });
    }
  }

  private flattenObject(obj: any, prefix = ''): Record<string, any> {
    const flattened: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        Object.assign(flattened, this.flattenObject(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    }
    
    return flattened;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * Export settings
   */
  async exportSettings(): Promise<AppSettings> {
    return await this.getSettings();
  }

  /**
   * Import settings
   */
  async importSettings(settings: AppSettings): Promise<void> {
    await this.updateSettings(settings);
  }
}
