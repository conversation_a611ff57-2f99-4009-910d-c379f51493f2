import React, { useState } from 'react';

interface HistoryItem {
  id: string;
  method: string;
  url: string;
  status: number;
  timestamp: Date;
  duration: number;
}

const HistoryPanel: React.FC = () => {
  const [history] = useState<HistoryItem[]>([
    {
      id: 'hist-1',
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/users',
      status: 200,
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      duration: 245
    },
    {
      id: 'hist-2',
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/users',
      status: 201,
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      duration: 567
    },
    {
      id: 'hist-3',
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/users/1',
      status: 200,
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      duration: 123
    },
    {
      id: 'hist-4',
      method: 'DELETE',
      url: 'https://jsonplaceholder.typicode.com/users/1',
      status: 404,
      timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
      duration: 89
    }
  ]);

  const getMethodColor = (method: string): string => {
    switch (method) {
      case 'GET': return '#28a745';
      case 'POST': return '#007bff';
      case 'PUT': return '#ffc107';
      case 'DELETE': return '#dc3545';
      case 'PATCH': return '#6f42c1';
      default: return '#6c757d';
    }
  };

  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return '#28a745';
    if (status >= 300 && status < 400) return '#ffc107';
    if (status >= 400 && status < 500) return '#fd7e14';
    if (status >= 500) return '#dc3545';
    return '#6c757d';
  };

  const formatTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="history-panel">
      <div className="history-list">
        {history.map(item => (
          <div key={item.id} className="history-item">
            <div className="history-header">
              <span 
                className="history-method"
                style={{ backgroundColor: getMethodColor(item.method) }}
              >
                {item.method}
              </span>
              <span 
                className="history-status"
                style={{ color: getStatusColor(item.status) }}
              >
                {item.status}
              </span>
              <span className="history-duration">{item.duration}ms</span>
            </div>
            <div className="history-url" title={item.url}>
              {item.url}
            </div>
            <div className="history-time">
              {formatTime(item.timestamp)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HistoryPanel;
