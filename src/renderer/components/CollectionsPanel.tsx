import React, { useState, useEffect } from 'react';
import TreeView from './TreeView';
import { WorkspaceItem, WorkspaceData } from '../types/workspace';
import { useAppContext } from '../contexts/AppContext';
import { generateSeedData } from '../data/seedData';

interface CollectionsPanelProps {
  onOpenRequest: (request: any) => void;
  onOpenEditor?: (type: 'folder' | 'collection' | 'example' | 'request', entityId: string, name: string, hierarchyId?: string, entityData?: any) => void;
}

const CollectionsPanel: React.FC<CollectionsPanelProps> = ({ onOpenRequest, onOpenEditor }) => {
  // Use AppContext for tab management and tree expansion state
  const { state, setActiveTab, toggleCollection, toggleFolder } = useAppContext();
  const [workspace, setWorkspace] = useState<WorkspaceData | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | undefined>();
  const [loading, setLoading] = useState<boolean>(true);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<number | null>(null);

  // Get expansion state from AppContext
  const { expandedCollections, expandedFolders } = state;

  useEffect(() => {
    // Load current workspace data
    loadCurrentWorkspaceData();
  }, []);

  const loadCurrentWorkspaceData = async () => {
    try {
      setLoading(true);

      // Get current workspace ID from localStorage or default to 1
      const activeWorkspaceId = getCurrentWorkspaceId();
      setCurrentWorkspaceId(activeWorkspaceId);

      if (activeWorkspaceId && window.electronAPI) {
        // Use new lightweight loading from database
        const items = await window.electronAPI.workspace.getItemsLight(activeWorkspaceId);

        const transformedItems = transformDatabaseItemsToWorkspaceItems(items);

        const workspaceData: WorkspaceData = {
          id: activeWorkspaceId.toString(),
          name: 'Current Workspace', // TODO: Get actual workspace name
          items: transformedItems
        };

        setWorkspace(workspaceData);
      } else {
        // Fallback to seed data for development
        const seedData = generateSeedData();
        setWorkspace(seedData);
      }
    } catch (error) {
      console.error('Failed to load workspace data:', error);
      // Fallback to seed data
      const seedData = generateSeedData();
      setWorkspace(seedData);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentWorkspaceId = (): number => {
    try {
      // Try to get from workspace manager first
      const activeWorkspace = localStorage.getItem('apicool-active-workspace');
      if (activeWorkspace) {
        const parsed = parseInt(activeWorkspace, 10);
        if (!isNaN(parsed)) {
          return parsed;
        }
      }

      // Default to workspace ID 1
      return 1;
    } catch (error) {
      console.error('Failed to get current workspace ID:', error);
      return 1;
    }
  };

  const reloadWorkspaceData = async () => {
    try {
      // Save current expansion state
      const expansionState = new Map<string, boolean>();

      if (workspace) {
        workspace.items.forEach(item => {
          if (item.type === 'collection' || item.type === 'folder') {
            expansionState.set(item.id, item.expanded || false);
          }
        });
      }

      console.log('Saved expansion state:', expansionState);

      // Reload data
      const activeWorkspaceId = getCurrentWorkspaceId();

      if (activeWorkspaceId && window.electronAPI) {
        const items = await window.electronAPI.workspace.getItemsLight(activeWorkspaceId);
        const transformedItems = transformDatabaseItemsToWorkspaceItems(items, expansionState);

        const workspaceData: WorkspaceData = {
          id: activeWorkspaceId.toString(),
          name: 'Current Workspace',
          items: transformedItems
        };

        console.log('Reloaded workspace with preserved state:', workspaceData);
        setWorkspace(workspaceData);
      }
    } catch (error) {
      console.error('Failed to reload workspace data:', error);
    }
  };

  const transformDatabaseItemsToWorkspaceItems = (dbItems: any[], expansionState?: Map<string, boolean>): WorkspaceItem[] => {
    // Flatten the hierarchical structure to a flat array for TreeView
    const flattenItems = (items: any[]): WorkspaceItem[] => {
      const result: WorkspaceItem[] = [];

      for (const item of items) {
        const itemId = item.id.toString();

        // Determine expansion state from AppContext
        let isExpanded = false;
        if (item.type.toLowerCase() === 'collection') {
          isExpanded = expandedCollections.includes(itemId);
        } else if (item.type.toLowerCase() === 'folder') {
          isExpanded = expandedFolders.includes(itemId);
        }

        // Use expansion state from parameter if provided (for reload scenarios)
        if (expansionState?.has(itemId)) {
          isExpanded = expansionState.get(itemId) || false;
        }

        // Check if this item has unsaved changes in any open tab
        let hasUnsavedChanges = false;
        if (item.type.toLowerCase() === 'request') {
          // Check if there's an open tab for this hierarchy item with unsaved changes
          const openTab = state.openTabs.find(tab => tab.id === itemId);
          hasUnsavedChanges = openTab?.hasUnsavedChanges || false;
        }

        const workspaceItem: WorkspaceItem = {
          id: itemId,
          name: item.entity?.name || 'Unnamed Item',
          type: item.type.toLowerCase() as 'collection' | 'folder' | 'request' | 'example',
          parentId: item.parentId?.toString(),
          order: item.order,
          method: item.entity?.method as any,
          url: item.entity?.url,
          description: item.entity?.description,
          expanded: isExpanded,
          hasUnsavedChanges: hasUnsavedChanges,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        };

        result.push(workspaceItem);

        // Recursively flatten children
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          result.push(...flattenItems(item.children));
        }
      }

      return result;
    };

    return flattenItems(dbItems);
  };

  // Save workspace to localStorage whenever it changes
  useEffect(() => {
    if (workspace) {
      localStorage.setItem('apicool-workspace', JSON.stringify(workspace));
    }
  }, [workspace]);

  // Listen for workspace changes and updates
  useEffect(() => {
    const handleWorkspaceUpdate = (event: CustomEvent) => {
      const updatedWorkspace = event.detail;
      // Convert date strings back to Date objects
      updatedWorkspace.items = updatedWorkspace.items.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      }));
      setWorkspace(updatedWorkspace);
    };

    const handleWorkspaceSwitched = async (event: Event) => {
      const customEvent = event as CustomEvent;
      const { workspaceId } = customEvent.detail;
      setSelectedItemId(undefined); // Clear selection when switching workspaces

      // Reload data for the new workspace
      try {
        setLoading(true);
        const numericWorkspaceId = parseInt(workspaceId, 10);
        setCurrentWorkspaceId(numericWorkspaceId);

        if (window.electronAPI) {
          const items = await window.electronAPI.workspace.getItemsLight(numericWorkspaceId);

          const workspaceData: WorkspaceData = {
            id: workspaceId,
            name: customEvent.detail.workspaceName || 'Workspace',
            items: transformDatabaseItemsToWorkspaceItems(items)
          };

          setWorkspace(workspaceData);
        }
      } catch (error) {
        console.error('Failed to load workspace data after switch:', error);
      } finally {
        setLoading(false);
      }
    };

    const handleWorkspaceDataUpdated = (event: CustomEvent) => {
      const { data } = event.detail;
      setWorkspace(data);
    };

    const handleWorkspaceReloadPreserveState = async () => {
      await reloadWorkspaceData();
    };

    const handleItemSaved = async (event: CustomEvent) => {
      const { type, item, parentId, workspaceId } = event.detail;

      console.log('handleItemSaved called with:', { type, item, parentId, workspaceId, currentWorkspaceId });

      // Only handle items for the current workspace
      if (workspaceId !== currentWorkspaceId) {
        console.log('Workspace ID mismatch, ignoring event');
        return;
      }

      try {
        // Add the new item to the current workspace without full reload
        if (workspace) {
          // Get the highest order for proper positioning
          const maxOrder = Math.max(0, ...workspace.items.map(i => i.order || 0));

          const newWorkspaceItem: WorkspaceItem = {
            id: item.id.toString(),
            name: item.name || 'Unnamed Item',
            type: type,
            parentId: parentId?.toString() || null,
            order: item.order || (maxOrder + 1),
            method: item.method || undefined,
            url: item.url || undefined,
            description: item.description || undefined,
            expanded: false,
            createdAt: new Date(item.createdAt || Date.now()),
            updatedAt: new Date(item.updatedAt || Date.now())
          };

          // Add the new item to the workspace
          const updatedWorkspace = {
            ...workspace,
            items: [...workspace.items, newWorkspaceItem]
          };

          setWorkspace(updatedWorkspace);
          console.log(`Successfully added new ${type} to tree:`, newWorkspaceItem);
          console.log('Updated workspace items count:', updatedWorkspace.items.length);
        } else {
          console.log('No workspace available to add item to');
        }
      } catch (error) {
        console.error('Failed to add new item to tree:', error);
        console.log('Falling back to full reload');
        // Fallback to full reload if adding fails
        await reloadWorkspaceData();
      }
    };

    window.addEventListener('workspace-updated', handleWorkspaceUpdate as EventListener);
    window.addEventListener('workspace-switched', handleWorkspaceSwitched);
    window.addEventListener('workspace-data-updated', handleWorkspaceDataUpdated as EventListener);
    window.addEventListener('workspace-reload-preserve-state', handleWorkspaceReloadPreserveState);
    window.addEventListener('item-saved', handleItemSaved as EventListener);

    return () => {
      window.removeEventListener('workspace-updated', handleWorkspaceUpdate as EventListener);
      window.removeEventListener('workspace-switched', handleWorkspaceSwitched);
      window.removeEventListener('workspace-data-updated', handleWorkspaceDataUpdated as EventListener);
      window.removeEventListener('workspace-reload-preserve-state', handleWorkspaceReloadPreserveState);
      window.removeEventListener('item-saved', handleItemSaved as EventListener);
    };
  }, []);

  // Helper function to check if a tab is already open
  const findExistingTab = (itemId: string) => {
    return state.openTabs.find(tab => tab.id === itemId);
  };



  // Unified function to handle all item clicks using onOpenEditor
  const handleItemClick = async (item: WorkspaceItem) => {
    setSelectedItemId(item.id);

    if (!onOpenEditor) {
      console.error('onOpenEditor is not available');
      alert('Editor functionality is not available');
      return;
    }

    // For requests, check if tab is already open
    if (item.type === 'request') {
      const existingTab = findExistingTab(item.id);
      if (existingTab) {
        setActiveTab(item.id);
        console.log('Activated existing tab:', item.id);
        return;
      }
    }

    // Load full item details for all types
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const itemDetails = await window.electronAPI.workspace.getItemDetails(parseInt(item.id));
      const entityId = itemDetails.entity?.id;

      if (!entityId) {
        throw new Error(`No entity found for item: ${item.name}`);
      }

      // Use onOpenEditor for all item types uniformly
      const editorType = item.type as 'folder' | 'collection' | 'example' | 'request';
      onOpenEditor(editorType, entityId.toString(), item.name, item.id, itemDetails.entity);
      console.log(`Opened ${item.type}:`, item.id);

    } catch (error) {
      console.error('Failed to open item:', error);
      // Show error message to user
      alert(`Failed to open ${item.name}: ${error.message || 'Unknown error'}`);
    }
  };

  const handleItemToggle = (itemId: string) => {
    if (!workspace) return;

    // Find the item to determine its type
    const item = workspace.items.find(i => i.id === itemId);
    if (!item) return;

    // Update AppContext state based on item type
    if (item.type === 'collection') {
      toggleCollection(itemId);
    } else if (item.type === 'folder') {
      toggleFolder(itemId);
    }

    // Update local workspace state for immediate UI feedback
    setWorkspace(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId
            ? { ...item, expanded: !item.expanded }
            : item
        )
      };
    });
  };

  const handleItemRename = (itemId: string, newName: string) => {
    if (!workspace) return;
    
    setWorkspace(prev => {
      if (!prev) return prev;
      
      const updatedWorkspace = {
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId
            ? { ...item, name: newName, updatedAt: new Date() }
            : item
        )
      };
      
      // Also update any open tabs with the same ID
      const openTabs = JSON.parse(localStorage.getItem('apicool-open-tabs') || '[]');
      const updatedTabs = openTabs.map((tab: any) => 
        tab.id === itemId ? { ...tab, name: newName } : tab
      );
      localStorage.setItem('apicool-open-tabs', JSON.stringify(updatedTabs));
      
      // Trigger tab update event
      window.dispatchEvent(new CustomEvent('tabs-updated', { detail: updatedTabs }));
      
      return updatedWorkspace;
    });
  };

  const handleItemDelete = async (itemId: string) => {
    if (!workspace) return;

    try {
      // Check if this is a database item (numeric ID) or temporary item
      const isNumericId = /^\d+$/.test(itemId);

      if (isNumericId && window.electronAPI) {
        // Delete from database
        await window.electronAPI.workspace.deleteItem(parseInt(itemId));

        // Reload workspace data while preserving expansion state
        await reloadWorkspaceData();
      } else {
        // Handle temporary items (local state only)
        const getDescendants = (parentId: string): string[] => {
          const children = workspace.items.filter(item => item.parentId === parentId);
          const descendants = children.map(child => child.id);
          children.forEach(child => {
            descendants.push(...getDescendants(child.id));
          });
          return descendants;
        };

        const toDelete = [itemId, ...getDescendants(itemId)];

        setWorkspace(prev => {
          if (!prev) return prev;

          return {
            ...prev,
            items: prev.items.filter(item => !toDelete.includes(item.id))
          };
        });
      }

      // Clear selection if deleted item was selected
      if (selectedItemId === itemId) {
        setSelectedItemId(undefined);
      }

      // Close any open tabs for the deleted item and all its descendants
      await closeTabsForDeletedItems(itemId);

    } catch (error) {
      console.error('Failed to delete item:', error);
      // TODO: Show error toast
    }
  };

  const closeTabsForDeletedItems = async (deletedItemId: string) => {
    if (!workspace) return;

    // Find all descendant items that will be deleted
    const getDescendants = (parentId: string): string[] => {
      const children = workspace.items.filter(item => item.parentId === parentId);
      const descendants = children.map(child => child.id);
      children.forEach(child => {
        descendants.push(...getDescendants(child.id));
      });
      return descendants;
    };

    const allDeletedIds = [deletedItemId, ...getDescendants(deletedItemId)];

    // Close tabs for all deleted items
    const openTabs = JSON.parse(localStorage.getItem('apicool-open-tabs') || '[]');
    const updatedTabs = openTabs.filter((tab: any) => !allDeletedIds.includes(tab.id));

    // Update localStorage and notify
    localStorage.setItem('apicool-open-tabs', JSON.stringify(updatedTabs));

    // Also update active tab if it was deleted
    const activeTab = localStorage.getItem('apicool-active-tab');
    if (activeTab && allDeletedIds.includes(activeTab)) {
      const newActiveTab = updatedTabs.length > 0 ? updatedTabs[0].id : '';
      localStorage.setItem('apicool-active-tab', newActiveTab);
    }

    // Dispatch events to update the UI
    window.dispatchEvent(new CustomEvent('tabs-updated', { detail: updatedTabs }));

    console.log(`Closed tabs for deleted items: ${allDeletedIds.join(', ')}`);
  };

  const handleItemMove = async (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => {
    if (!workspace) return;

    const dragItem = workspace.items.find(item => item.id === dragId);
    const hoverItem = workspace.items.find(item => item.id === hoverId);

    if (!dragItem || !hoverItem) return;

    // Prevent dropping item into itself or its descendants
    const isDescendant = (parentId: string, childId: string): boolean => {
      const children = workspace.items.filter(item => item.parentId === parentId);
      return children.some(child =>
        child.id === childId || isDescendant(child.id, childId)
      );
    };

    if (dragId === hoverId || isDescendant(dragId, hoverId)) return;

    // Check if these are database items (numeric IDs)
    const isDragItemFromDB = /^\d+$/.test(dragId);
    const isHoverItemFromDB = /^\d+$/.test(hoverId);

    if (!isDragItemFromDB || !isHoverItemFromDB) {
      // Handle temporary items with local state only
      handleLocalItemMove(dragId, hoverId, position);
      return;
    }

    try {
      // Calculate new hierarchy values
      let newParentId: number | null;
      let newOrder: number;

      if (position === 'inside') {
        newParentId = parseInt(hoverId);
        const siblings = workspace.items.filter(item => item.parentId === hoverId);
        newOrder = siblings.length > 0 ? Math.max(...siblings.map(s => s.order)) + 1 : 1;
      } else {
        newParentId = hoverItem.parentId ? parseInt(hoverItem.parentId) : null;
        newOrder = position === 'before' ? hoverItem.order : hoverItem.order + 1;

        // Get all siblings that need reordering
        const siblings = workspace.items.filter(item =>
          item.parentId === hoverItem.parentId &&
          item.id !== dragId &&
          /^\d+$/.test(item.id)
        );

        const itemsToReorder: { id: number; order: number }[] = [];

        siblings.forEach(sibling => {
          if (position === 'before' && sibling.order >= hoverItem.order) {
            itemsToReorder.push({ id: parseInt(sibling.id), order: sibling.order + 1 });
          } else if (position === 'after' && sibling.order > hoverItem.order) {
            itemsToReorder.push({ id: parseInt(sibling.id), order: sibling.order + 1 });
          }
        });

        // Reorder siblings in database if needed
        if (itemsToReorder.length > 0 && window.electronAPI) {
          await window.electronAPI.workspace.reorderItems(itemsToReorder);
        }
      }

      // Update the dragged item in database
      if (window.electronAPI) {
        await window.electronAPI.workspace.updateItemHierarchy(
          parseInt(dragId),
          newParentId,
          newOrder
        );

        // Reload workspace data while preserving expansion state
        await reloadWorkspaceData();
      }
    } catch (error) {
      console.error('Failed to move item:', error);
      // TODO: Show error toast
    }
  };

  const handleLocalItemMove = (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => {
    setWorkspace(prev => {
      if (!prev) return prev;

      const dragItem = prev.items.find(item => item.id === dragId);
      const hoverItem = prev.items.find(item => item.id === hoverId);

      if (!dragItem || !hoverItem) return prev;

      let newParentId: string | undefined;
      let newOrder: number;

      if (position === 'inside') {
        newParentId = hoverId;
        const siblings = prev.items.filter(item => item.parentId === hoverId);
        newOrder = siblings.length > 0 ? Math.max(...siblings.map(s => s.order)) + 1 : 0;
      } else {
        newParentId = hoverItem.parentId;
        const siblings = prev.items.filter(item => item.parentId === newParentId);
        newOrder = position === 'before' ? hoverItem.order : hoverItem.order + 1;

        // Adjust orders of other items
        siblings.forEach(sibling => {
          if (sibling.id !== dragId) {
            if (position === 'before' && sibling.order >= hoverItem.order) {
              sibling.order += 1;
            } else if (position === 'after' && sibling.order > hoverItem.order) {
              sibling.order += 1;
            }
          }
        });
      }

      return {
        ...prev,
        items: prev.items.map(item =>
          item.id === dragId
            ? { ...item, parentId: newParentId, order: newOrder, updatedAt: new Date() }
            : item
        )
      };
    });
  };

  const handleAddNew = (parentId?: string, type: 'folder' | 'request' = 'request') => {
    if (!workspace) return;
    
    const siblings = workspace.items.filter(item => item.parentId === parentId);
    const maxOrder = siblings.length > 0 ? Math.max(...siblings.map(s => s.order)) : -1;
    
    const newItem: WorkspaceItem = {
      id: `${type}-${Date.now()}`,
      name: type === 'folder' ? 'New Folder' : 'New Request',
      type,
      parentId,
      order: maxOrder + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...(type === 'folder' ? { expanded: true } : {
        method: 'GET' as const,
        url: '',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        params: {},
        body: ''
      })
    };
    
    setWorkspace(prev => {
      if (!prev) return prev;
      
      return {
        ...prev,
        items: [...prev.items, newItem]
      };
    });
    
    setSelectedItemId(newItem.id);
    
    // If it's a request, automatically open it in a new tab
    if (type === 'request') {
      onOpenRequest({
        id: newItem.id,
        name: newItem.name,
        method: newItem.method,
        url: newItem.url,
        headers: newItem.headers,
        params: newItem.params,
        body: newItem.body
      });
    }
  };

  if (loading || !workspace) {
    return (
      <div className="collections-panel">
        <div style={{ padding: '16px', textAlign: 'center', color: 'var(--color-text-secondary)' }}>
          Loading workspace...
        </div>
      </div>
    );
  }

  // Get root items (items with no parent) for TreeView
  const rootItems = workspace.items.filter(item => !item.parentId);

  return (
    <div className="collections-panel">
      <TreeView
        items={rootItems}
        allItems={workspace.items}
        onItemClick={handleItemClick}
        onItemToggle={handleItemToggle}
        onItemRename={handleItemRename}
        onItemDelete={handleItemDelete}
        onItemMove={handleItemMove}
        onAddNew={handleAddNew}
        selectedItemId={selectedItemId}
      />
    </div>
  );
};

export default CollectionsPanel;
