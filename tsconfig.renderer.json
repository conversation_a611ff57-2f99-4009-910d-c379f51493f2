{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "node", "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "noEmit": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true}, "include": ["src/renderer/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/main/**/*"]}