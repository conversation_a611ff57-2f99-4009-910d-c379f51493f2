import { PrismaClient } from '@prisma/client';

export interface Item {
  id: number;
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  entityId: number;
  workspaceId: number;
  parentId: number | null;
  order: number;
  collection?: any;
  folder?: any;
  request?: any;
  example?: any;
  children?: Item[];
}

export interface CreateItemData {
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  entityId: number;
  workspaceId: number;
  parentId?: number | null;
  order?: number;
}

export class HierarchyService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async getWorkspaceItems(workspaceId: number): Promise<Item[]> {
    try {
      const items = await this.prisma.item.findMany({
        where: { workspaceId },
        orderBy: [
          { parentId: 'asc' },
          { order: 'asc' }
        ]
      });

      // Fetch related entities and attach them
      const itemsWithEntities = await this.attachEntities(items);

      // Build hierarchy
      return this.buildHierarchy(itemsWithEntities);
    } catch (error) {
      console.error('Error fetching workspace items:', error);
      throw new Error('Failed to fetch workspace items');
    }
  }

  private async attachEntities(items: any[]): Promise<any[]> {
    const itemsWithEntities = [];

    for (const item of items) {
      const itemWithEntity = { ...item };

      // Fetch the related entity based on type
      switch (item.type) {
        case 'COLLECTION':
          itemWithEntity.collection = await this.prisma.collection.findUnique({
            where: { id: item.entityId }
          });
          break;
        case 'FOLDER':
          itemWithEntity.folder = await this.prisma.folder.findUnique({
            where: { id: item.entityId }
          });
          break;
        case 'REQUEST':
          itemWithEntity.request = await this.prisma.request.findUnique({
            where: { id: item.entityId }
          });
          break;
        case 'EXAMPLE':
          itemWithEntity.example = await this.prisma.example.findUnique({
            where: { id: item.entityId }
          });
          break;
      }

      itemsWithEntities.push(itemWithEntity);
    }

    return itemsWithEntities;
  }

  private buildHierarchy(items: any[]): Item[] {
    const itemMap = new Map<number, Item>();
    const rootItems: Item[] = [];

    // First pass: create item objects
    items.forEach(item => {
      const hierarchyItem: Item = {
        id: item.id,
        type: item.type,
        entityId: item.entityId,
        workspaceId: item.workspaceId,
        parentId: item.parentId,
        order: item.order,
        children: []
      };

      // Attach entity data based on type
      if (item.collection) hierarchyItem.collection = item.collection;
      if (item.folder) hierarchyItem.folder = item.folder;
      if (item.request) hierarchyItem.request = item.request;
      if (item.example) hierarchyItem.example = item.example;

      itemMap.set(item.id, hierarchyItem);
    });

    // Second pass: build parent-child relationships
    items.forEach(item => {
      const hierarchyItem = itemMap.get(item.id);
      if (hierarchyItem) {
        if (item.parentId) {
          const parent = itemMap.get(item.parentId);
          if (parent) {
            parent.children = parent.children || [];
            parent.children.push(hierarchyItem);
          }
        } else {
          rootItems.push(hierarchyItem);
        }
      }
    });

    return rootItems;
  }

  async createItem(data: CreateItemData): Promise<Item> {
    try {
      // Get the next order number for the parent
      const nextOrder = await this.getNextOrderNumber(data.workspaceId, data.parentId || null);

      const item = await this.prisma.item.create({
        data: {
          type: data.type,
          entityId: data.entityId,
          workspaceId: data.workspaceId,
          parentId: data.parentId,
          order: data.order ?? nextOrder
        }
      });

      // Attach the entity data
      const itemWithEntity = await this.attachEntities([item]);
      return this.mapToItem(itemWithEntity[0]);


    } catch (error) {
      console.error('Error creating item:', error);
      throw new Error('Failed to create item');
    }
  }

  async updateItemHierarchy(itemId: number, parentId: number | null, order: number): Promise<void> {
    try {
      await this.prisma.$transaction(async (tx:any) => {
        // Validate the move
        await this.validateMove(tx, itemId, parentId);

        // Get current item
        const currentItem = await tx.item.findUnique({
          where: { id: itemId }
        });

        if (!currentItem) {
          throw new Error('Item not found');
        }

        // Update siblings' order in the new parent
        await tx.item.updateMany({
          where: {
            workspaceId: currentItem.workspaceId,
            parentId: parentId,
            order: { gte: order },
            id: { not: itemId }
          },
          data: {
            order: { increment: 1 }
          }
        });

        // Update the item
        await tx.item.update({
          where: { id: itemId },
          data: {
            parentId: parentId,
            order: order
          }
        });

        // Reorder siblings in the old parent
        if (currentItem.parentId !== parentId) {
          await tx.item.updateMany({
            where: {
              workspaceId: currentItem.workspaceId,
              parentId: currentItem.parentId,
              order: { gt: currentItem.order }
            },
            data: {
              order: { decrement: 1 }
            }
          });
        }
      });
    } catch (error) {
      console.error('Error updating item hierarchy:', error);
      throw new Error('Failed to update item hierarchy');
    }
  }

  private async validateMove(tx: any, itemId: number, parentId: number | null): Promise<void> {
    if (!parentId) return; // Moving to root is always valid

    // Check for cycles
    const item = await tx.item.findUnique({
      where: { id: itemId }
    });

    if (!item) {
      throw new Error('Item not found');
    }

    // Check if trying to move item under itself or its descendants
    const isDescendant = await this.isDescendant(tx, parentId, itemId);
    if (isDescendant) {
      throw new Error('Cannot move item under itself or its descendants');
    }

    // Validate business rules (e.g., Examples can only be under Requests)
    if (item.type === 'EXAMPLE') {
      const parent = await tx.item.findUnique({
        where: { id: parentId }
      });
      if (!parent || parent.type !== 'REQUEST') {
        throw new Error('Examples can only be placed under Requests');
      }
    }
  }

  private async isDescendant(tx: any, ancestorId: number, descendantId: number): Promise<boolean> {
    const descendants = await this.getAllDescendants(tx, ancestorId);
    return descendants.some(d => d.id === descendantId);
  }

  private async getAllDescendants(tx: any, itemId: number): Promise<any[]> {
    const children = await tx.item.findMany({
      where: { parentId: itemId }
    });

    let descendants = [...children];
    for (const child of children) {
      const childDescendants = await this.getAllDescendants(tx, child.id);
      descendants = descendants.concat(childDescendants);
    }

    return descendants;
  }

  private async getNextOrderNumber(workspaceId: number, parentId: number | null): Promise<number> {
    const maxOrder = await this.prisma.item.aggregate({
      where: {
        workspaceId,
        parentId
      },
      _max: {
        order: true
      }
    });

    return (maxOrder._max.order || 0) + 1;
  }

  private mapToItem(item: any): Item {
    const hierarchyItem: Item = {
      id: item.id,
      type: item.type,
      entityId: item.entityId,
      workspaceId: item.workspaceId,
      parentId: item.parentId,
      order: item.order
    };

    if (item.collection) hierarchyItem.collection = item.collection;
    if (item.folder) hierarchyItem.folder = item.folder;
    if (item.request) hierarchyItem.request = item.request;
    if (item.example) hierarchyItem.example = item.example;

    return hierarchyItem;
  }

  async deleteItem(itemId: number): Promise<void> {
    try {
      await this.prisma.$transaction(async (tx:any) => {
        // Get all descendants
        const descendants = await this.getAllDescendants(tx, itemId);
        
        // Delete all descendants first
        for (const descendant of descendants) {
          await tx.item.delete({
            where: { id: descendant.id }
          });
        }

        // Delete the item itself
        await tx.item.delete({
          where: { id: itemId }
        });
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      throw new Error('Failed to delete item');
    }
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
