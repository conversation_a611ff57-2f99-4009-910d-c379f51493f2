import React, { useState, useRef, useEffect } from 'react';

interface Request {
  id: string;
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  params?: Record<string, string>;
  body?: string;
  hasUnsavedChanges?: boolean;
}

interface TabSearchDropdownProps {
  tabs: Request[];
  activeTab: string;
  onTabSelect: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onEnsureTabVisible?: (tabId: string) => void;
}

const TabSearchDropdown: React.FC<TabSearchDropdownProps> = ({
  tabs,
  activeTab,
  onTabSelect,
  onTabClose,
  onEnsureTabVisible
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const filteredTabs = tabs.filter(tab =>
    tab.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tab.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tab.method.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleTabSelect = (tabId: string) => {
    // Ensure the tab is visible in the tab header before selecting
    if (onEnsureTabVisible) {
      onEnsureTabVisible(tabId);
    }

    onTabSelect(tabId);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose(tabId);
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET': return '#28a745';
      case 'POST': return '#007bff';
      case 'PUT': return '#ffc107';
      case 'DELETE': return '#dc3545';
      case 'PATCH': return '#6f42c1';
      default: return '#6c757d';
    }
  };

  return (
    <div className="tab-search-dropdown" ref={dropdownRef}>
      <button
        className="tab-search-btn"
        onClick={() => setIsOpen(!isOpen)}
        title="Search tabs (⌘⇧A)"
      >
        <span className="tab-search-icon">🔍</span>
        <span className="tab-search-shortcut">⌘⇧A</span>
      </button>

      {isOpen && (
        <div className="tab-search-dropdown-panel">
          <div className="tab-search-header">
            <input
              ref={searchInputRef}
              type="text"
              className="tab-search-input"
              placeholder="Search tabs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="tab-search-results">
            {filteredTabs.length === 0 ? (
              <div className="tab-search-empty">
                {searchTerm ? 'No tabs match your search' : 'No open tabs'}
              </div>
            ) : (
              <>
                <div className="tab-search-section">
                  <div className="tab-search-section-title">Open Tabs</div>
                  {filteredTabs.map(tab => (
                    <div
                      key={tab.id}
                      className={`tab-search-item ${activeTab === tab.id ? 'active' : ''}`}
                      onClick={() => handleTabSelect(tab.id)}
                    >
                      <div className="tab-search-item-main">
                        <span
                          className="tab-search-method"
                          style={{ backgroundColor: getMethodColor(tab.method) }}
                        >
                          {tab.method}
                        </span>
                        <span className="tab-search-name">{tab.name}</span>
                        <button
                          className="tab-search-close"
                          onClick={(e) => handleTabClose(e, tab.id)}
                          title="Close tab"
                        >
                          ×
                        </button>
                      </div>
                      {tab.url && (
                        <div className="tab-search-url">{tab.url}</div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Recently Closed Section - Placeholder for future implementation */}
                <div className="tab-search-section">
                  <div className="tab-search-section-title">Recently Closed</div>
                  <div className="tab-search-empty">No recently closed tabs</div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TabSearchDropdown;
