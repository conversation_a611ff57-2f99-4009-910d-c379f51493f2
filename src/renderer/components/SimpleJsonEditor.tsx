import React, { useState, useEffect } from 'react';

interface SimpleJsonEditorProps {
  value: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  height?: string | number;
  placeholder?: string;
}

const SimpleJsonEditor: React.FC<SimpleJsonEditorProps> = ({
  value,
  onChange,
  readOnly = false,
  height = '300px',
  placeholder = ''
}) => {
  const [localValue, setLocalValue] = useState(value);
  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);

    // Validate JSON if not empty
    if (newValue.trim()) {
      try {
        JSON.parse(newValue);
        setIsValid(true);
      } catch {
        setIsValid(false);
      }
    } else {
      setIsValid(true);
    }

    if (onChange) {
      onChange(newValue);
    }
  };

  const formatJson = () => {
    if (localValue.trim()) {
      try {
        const parsed = JSON.parse(localValue);
        const formatted = JSON.stringify(parsed, null, 2);
        setLocalValue(formatted);
        setIsValid(true);
        if (onChange) {
          onChange(formatted);
        }
      } catch (error) {
        console.warn('Invalid JSON, cannot format');
      }
    }
  };

  const minifyJson = () => {
    if (localValue.trim()) {
      try {
        const parsed = JSON.parse(localValue);
        const minified = JSON.stringify(parsed);
        setLocalValue(minified);
        setIsValid(true);
        if (onChange) {
          onChange(minified);
        }
      } catch (error) {
        console.warn('Invalid JSON, cannot minify');
      }
    }
  };

  return (
    <div className="simple-json-editor">
      <div className="json-editor-toolbar">
        <div className="json-status">
          {!isValid && (
            <span className="json-error">Invalid JSON</span>
          )}
          {isValid && localValue.trim() && (
            <span className="json-valid">Valid JSON</span>
          )}
        </div>
        <div className="json-actions">
          <button
            type="button"
            onClick={formatJson}
            disabled={readOnly || !localValue.trim()}
            className="json-format-btn"
          >
            Format
          </button>
          <button
            type="button"
            onClick={minifyJson}
            disabled={readOnly || !localValue.trim()}
            className="json-minify-btn"
          >
            Minify
          </button>
        </div>
      </div>
      <div className="json-editor-content">
        <textarea
          value={localValue}
          onChange={handleChange}
          readOnly={readOnly}
          placeholder={placeholder}
          className={`json-textarea ${!isValid ? 'invalid' : ''}`}
          style={{
            width: '100%',
            height: typeof height === 'string' ? height : `${height}px`,
            border: '1px solid var(--color-border)',
            borderRadius: '4px',
            padding: '12px',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, monospace',
            fontSize: '13px',
            lineHeight: '1.5',
            resize: 'vertical',
            background: 'var(--color-background)',
            color: 'var(--color-text)',
            outline: 'none',
            transition: 'border-color 0.2s ease',
            minHeight: '100px'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = 'var(--color-primary)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--color-border)';
          }}
        />
      </div>
    </div>
  );
};

export default SimpleJsonEditor;
