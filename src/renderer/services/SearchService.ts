import { WorkspaceItem, WorkspaceData } from '../types/workspace';
import WorkspaceManager from './WorkspaceManager';

export interface SearchResult {
  id: string;
  type: 'collection' | 'folder' | 'request' | 'example';
  name: string;
  description?: string;
  method?: string;
  url?: string;
  path: string[]; // Breadcrumb path to the item
  workspaceId?: string;
  workspaceName?: string;
}

export class SearchService {
  /**
   * Search all items across all workspaces
   */
  static async searchAllItems(query: string): Promise<SearchResult[]> {
    if (!query.trim()) {
      return [];
    }

    const searchTerm = query.toLowerCase();
    const results: SearchResult[] = [];

    try {
      // Get current workspace ID
      const activeWorkspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);

      // Get hierarchy from database
      const hierarchy = await window.electronAPI.hierarchy.getItems(activeWorkspaceId);

      // Search in all items
      hierarchy.forEach(item => {
        const itemResults = this.searchInHierarchyItem(item, searchTerm, activeWorkspaceId.toString(), 'Current Workspace');
        results.push(...itemResults);
      });
    } catch (error) {
      console.error('Failed to load workspace data for search:', error);
    }

    // Sort results by relevance (exact matches first, then partial matches)
    return results.sort((a, b) => {
      const aExact = a.name.toLowerCase() === searchTerm;
      const bExact = b.name.toLowerCase() === searchTerm;

      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;

      // Then by name length (shorter names first for partial matches)
      return a.name.length - b.name.length;
    });
  }

  /**
   * Search within a hierarchy item (database format)
   */
  static searchInHierarchyItem(item: any, searchTerm: string, workspaceId: string, workspaceName: string, path: string[] = []): SearchResult[] {
    const results: SearchResult[] = [];

    // Get the entity data based on item type
    const entity = item.collection || item.folder || item.request || item.example;
    if (!entity) return results;

    const itemName = entity.name || 'Untitled';
    const itemDescription = entity.description || '';

    // Check if this item matches the search
    const nameMatch = itemName.toLowerCase().includes(searchTerm);
    const descriptionMatch = itemDescription.toLowerCase().includes(searchTerm);
    const urlMatch = entity.url && entity.url.toLowerCase().includes(searchTerm);

    if (nameMatch || descriptionMatch || urlMatch) {
      results.push({
        id: item.id.toString(),
        name: itemName,
        type: item.type.toLowerCase() as 'collection' | 'folder' | 'request' | 'example',
        description: itemDescription,
        method: entity.method,
        url: entity.url,
        path: [...path],
        workspaceId,
        workspaceName
      });
    }

    // Search in children recursively
    if (item.children && item.children.length > 0) {
      const currentPath = [...path, itemName];
      item.children.forEach((child: any) => {
        const childResults = this.searchInHierarchyItem(child, searchTerm, workspaceId, workspaceName, currentPath);
        results.push(...childResults);
      });
    }

    return results;
  }

  /**
   * Search within a specific workspace
   */
  static async searchInWorkspace(workspaceId: string, query: string): Promise<SearchResult[]> {
    if (!query.trim()) {
      return [];
    }

    const searchTerm = query.toLowerCase();
    const results: SearchResult[] = [];

    try {
      // Get hierarchy from database
      const hierarchy = await window.electronAPI.hierarchy.getItems(parseInt(workspaceId));

      hierarchy.forEach(item => {
        const itemResults = this.searchInHierarchyItem(item, searchTerm, workspaceId, 'Current Workspace');
        results.push(...itemResults);
      });
    } catch (error) {
      console.error('Failed to load workspace data for search:', error);
    }

    return results.sort((a, b) => {
      const aExact = a.name.toLowerCase() === searchTerm;
      const bExact = b.name.toLowerCase() === searchTerm;

      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;

      return a.name.length - b.name.length;
    });
  }

  /**
   * Search within a collection recursively
   */
  private static searchInCollection(
    item: WorkspaceItem, 
    searchTerm: string, 
    workspaceId: string, 
    workspaceName: string,
    path: string[] = []
  ): SearchResult[] {
    const results: SearchResult[] = [];
    const currentPath = [...path];

    // Check if current item matches
    if (this.itemMatches(item, searchTerm)) {
      results.push({
        id: item.id,
        type: item.type as any,
        name: item.name,
        description: item.description,
        method: item.method,
        url: item.url,
        path: currentPath,
        workspaceId,
        workspaceName
      });
    }

    // Add current item to path for children
    currentPath.push(item.name);

    // Search in children recursively
    if (item.children) {
      item.children.forEach(child => {
        const childResults = this.searchInCollection(child, searchTerm, workspaceId, workspaceName, currentPath);
        results.push(...childResults);
      });
    }

    return results;
  }

  /**
   * Check if an item matches the search term
   */
  private static itemMatches(item: WorkspaceItem, searchTerm: string): boolean {
    const name = item.name.toLowerCase();
    const description = (item.description || '').toLowerCase();
    const url = (item.url || '').toLowerCase();
    const method = (item.method || '').toLowerCase();

    return (
      name.includes(searchTerm) ||
      description.includes(searchTerm) ||
      url.includes(searchTerm) ||
      method.includes(searchTerm)
    );
  }

  /**
   * Get recent items (most recently accessed/modified)
   */
  static async getRecentItems(limit: number = 10): Promise<SearchResult[]> {
    const results: SearchResult[] = [];

    try {
      // Get current workspace ID
      const activeWorkspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);

      // Get hierarchy from database
      const hierarchy = await window.electronAPI.hierarchy.getItems(activeWorkspaceId);

      hierarchy.forEach(item => {
        const itemResults = this.flattenHierarchyItem(item, activeWorkspaceId.toString(), 'Current Workspace');
        results.push(...itemResults);
      });
    } catch (error) {
      console.error('Failed to load workspace data for recent items:', error);
    }

    // Sort by last modified/accessed (using actual timestamps from database)
    return results
      .sort((a, b) => a.name.localeCompare(b.name))
      .slice(0, limit);
  }

  /**
   * Flatten a hierarchy item to get all items (database format)
   */
  private static flattenHierarchyItem(
    item: any,
    workspaceId: string,
    workspaceName: string,
    path: string[] = []
  ): SearchResult[] {
    const results: SearchResult[] = [];

    // Get the entity data based on item type
    const entity = item.collection || item.folder || item.request || item.example;
    if (!entity) return results;

    const itemName = entity.name || 'Untitled';

    // Add this item to results
    results.push({
      id: item.id.toString(),
      name: itemName,
      type: item.type.toLowerCase() as 'collection' | 'folder' | 'request' | 'example',
      description: entity.description || '',
      method: entity.method,
      url: entity.url,
      path: [...path],
      workspaceId,
      workspaceName
    });

    // Flatten children recursively
    if (item.children && item.children.length > 0) {
      const currentPath = [...path, itemName];
      item.children.forEach((child: any) => {
        const childResults = this.flattenHierarchyItem(child, workspaceId, workspaceName, currentPath);
        results.push(...childResults);
      });
    }

    return results;
  }

  /**
   * Flatten a collection to get all items (legacy format - kept for compatibility)
   */
  private static flattenCollection(
    item: WorkspaceItem,
    workspaceId: string,
    workspaceName: string,
    path: string[] = []
  ): SearchResult[] {
    const results: SearchResult[] = [];
    const currentPath = [...path];

    results.push({
      id: item.id,
      type: item.type as any,
      name: item.name,
      description: item.description,
      method: item.method,
      url: item.url,
      path: currentPath,
      workspaceId,
      workspaceName
    });

    currentPath.push(item.name);

    if (item.children) {
      item.children.forEach(child => {
        const childResults = this.flattenCollection(child, workspaceId, workspaceName, currentPath);
        results.push(...childResults);
      });
    }

    return results;
  }

  /**
   * Search by type
   */
  static async searchByType(type: 'collection' | 'folder' | 'request' | 'example', query?: string): Promise<SearchResult[]> {
    const allItems = await this.getRecentItems(1000); // Get all items
    let filtered = allItems.filter(item => item.type === type);

    if (query && query.trim()) {
      const searchTerm = query.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        (item.description || '').toLowerCase().includes(searchTerm)
      );
    }

    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get suggestions based on partial input
   */
  static async getSuggestions(query: string, limit: number = 5): Promise<SearchResult[]> {
    if (!query.trim()) {
      return await this.getRecentItems(limit);
    }

    const results = await this.searchAllItems(query);
    return results.slice(0, limit);
  }
}

export default SearchService;
