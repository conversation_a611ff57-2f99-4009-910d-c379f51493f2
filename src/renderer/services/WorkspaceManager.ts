import {
  Workspace,
  WorkspaceCreateRequest,
  WorkspaceUpdateRequest,
  WorkspaceStats,
  WorkspaceExportData,
  WorkspaceImportResult,
  createDefaultWorkspace
} from '../types/workspace-management';

export class WorkspaceManager {
  private static readonly STORAGE_KEY = 'apicool_workspaces';
  private static readonly ACTIVE_WORKSPACE_KEY = 'apicool_active_workspace';
  
  // Get all workspaces
  static getWorkspaces(): Workspace[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) {
        // Create default workspace if none exist
        const defaultWorkspace = createDefaultWorkspace();
        this.saveWorkspaces([defaultWorkspace]);
        this.setActiveWorkspace(defaultWorkspace.id);
        return [defaultWorkspace];
      }
      
      const workspaces = JSON.parse(stored);
      // Convert date strings back to Date objects
      return workspaces.map((ws: any) => ({
        ...ws,
        createdAt: new Date(ws.createdAt),
        updatedAt: new Date(ws.updatedAt)
      }));
    } catch (error) {
      console.error('Failed to load workspaces:', error);
      const defaultWorkspace = createDefaultWorkspace();
      return [defaultWorkspace];
    }
  }

  // Get workspace by ID
  static getWorkspace(id: string): Workspace | null {
    const workspaces = this.getWorkspaces();
    return workspaces.find(ws => ws.id === id) || null;
  }

  // Get active workspace
  static getActiveWorkspace(): Workspace {
    const activeId = localStorage.getItem(this.ACTIVE_WORKSPACE_KEY);
    if (activeId) {
      const workspace = this.getWorkspace(activeId);
      if (workspace) return workspace;
    }
    
    // Fallback to first workspace or create default
    const workspaces = this.getWorkspaces();
    return workspaces[0] || createDefaultWorkspace();
  }

  // Set active workspace
  static setActiveWorkspace(id: string): boolean {
    const workspace = this.getWorkspace(id);
    if (workspace) {
      localStorage.setItem(this.ACTIVE_WORKSPACE_KEY, id);
      // Dispatch event for components to listen to
      window.dispatchEvent(new CustomEvent('workspace-changed', { 
        detail: { workspace } 
      }));
      return true;
    }
    return false;
  }

  // Create new workspace
  static createWorkspace(request: WorkspaceCreateRequest): Workspace {
    const workspaces = this.getWorkspaces();

    const newWorkspace: Workspace = {
      id: this.generateId(),
      name: request.name,
      description: request.description,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      settings: request.copyFrom
        ? { ...this.getWorkspace(request.copyFrom)?.settings || createDefaultWorkspace().settings }
        : { ...createDefaultWorkspace().settings },
      collections: [],
      environments: []
    };

    workspaces.push(newWorkspace);
    this.saveWorkspaces(workspaces);

    // Automatically switch to the new workspace
    this.setActiveWorkspace(newWorkspace.id);

    // Dispatch event
    window.dispatchEvent(new CustomEvent('workspace-created', {
      detail: { workspace: newWorkspace }
    }));

    return newWorkspace;
  }

  // Update workspace
  static updateWorkspace(id: string, request: WorkspaceUpdateRequest): Workspace | null {
    const workspaces = this.getWorkspaces();
    const index = workspaces.findIndex(ws => ws.id === id);
    
    if (index === -1) return null;
    
    const workspace = workspaces[index];
    const updatedWorkspace: Workspace = {
      ...workspace,
      ...request,
      updatedAt: new Date(),
      settings: request.settings 
        ? { ...workspace.settings, ...request.settings }
        : workspace.settings
    };
    
    workspaces[index] = updatedWorkspace;
    this.saveWorkspaces(workspaces);
    
    // Dispatch event
    window.dispatchEvent(new CustomEvent('workspace-updated', { 
      detail: { workspace: updatedWorkspace } 
    }));
    
    return updatedWorkspace;
  }

  // Delete workspace
  static deleteWorkspace(id: string): boolean {
    const workspaces = this.getWorkspaces();
    const workspace = workspaces.find(ws => ws.id === id);
    
    if (!workspace || workspace.isDefault) {
      return false; // Cannot delete default workspace
    }
    
    const filteredWorkspaces = workspaces.filter(ws => ws.id !== id);
    this.saveWorkspaces(filteredWorkspaces);
    
    // If deleted workspace was active, switch to default
    const activeId = localStorage.getItem(this.ACTIVE_WORKSPACE_KEY);
    if (activeId === id) {
      const defaultWorkspace = filteredWorkspaces.find(ws => ws.isDefault) || filteredWorkspaces[0];
      if (defaultWorkspace) {
        this.setActiveWorkspace(defaultWorkspace.id);
      }
    }
    
    // Dispatch event
    window.dispatchEvent(new CustomEvent('workspace-deleted', { 
      detail: { workspaceId: id } 
    }));
    
    return true;
  }

  // Get workspace statistics
  static getWorkspaceStats(id: string): WorkspaceStats | null {
    const workspace = this.getWorkspace(id);
    if (!workspace) return null;
    
    // TODO: Calculate actual stats from collections and environments
    return {
      collectionsCount: workspace.collections.length,
      requestsCount: 0, // Calculate from collections
      environmentsCount: workspace.environments.length,
      lastActivity: workspace.updatedAt
    };
  }

  // Export workspace
  static exportWorkspace(id: string): WorkspaceExportData | null {
    const workspace = this.getWorkspace(id);
    if (!workspace) return null;
    
    // TODO: Get actual collections and environments data
    return {
      workspace,
      collections: [], // Get from storage
      environments: [], // Get from storage
      exportedAt: new Date(),
      version: '1.0.0'
    };
  }

  // Import workspace
  static importWorkspace(data: WorkspaceExportData): WorkspaceImportResult {
    try {
      const workspaces = this.getWorkspaces();
      
      // Check if workspace with same name exists
      const existingWorkspace = workspaces.find(ws => ws.name === data.workspace.name);
      if (existingWorkspace) {
        return {
          success: false,
          errors: [`Workspace with name "${data.workspace.name}" already exists`],
          warnings: []
        };
      }
      
      // Create new workspace with new ID
      const importedWorkspace: Workspace = {
        ...data.workspace,
        id: this.generateId(),
        isDefault: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      workspaces.push(importedWorkspace);
      this.saveWorkspaces(workspaces);
      
      // TODO: Import collections and environments
      
      return {
        success: true,
        workspace: importedWorkspace,
        errors: [],
        warnings: []
      };
    } catch (error) {
      return {
        success: false,
        errors: [`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  // Duplicate workspace
  static duplicateWorkspace(id: string, newName: string): Workspace | null {
    const sourceWorkspace = this.getWorkspace(id);
    if (!sourceWorkspace) return null;
    
    const duplicatedWorkspace: Workspace = {
      ...sourceWorkspace,
      id: this.generateId(),
      name: newName,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const workspaces = this.getWorkspaces();
    workspaces.push(duplicatedWorkspace);
    this.saveWorkspaces(workspaces);
    
    return duplicatedWorkspace;
  }

  // Private helper methods
  private static saveWorkspaces(workspaces: Workspace[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(workspaces));
    } catch (error) {
      console.error('Failed to save workspaces:', error);
    }
  }

  private static generateId(): string {
    return 'ws_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
  }

  // Utility methods
  static validateWorkspaceName(name: string, excludeId?: string): boolean {
    if (!name || name.trim().length === 0) return false;
    
    const workspaces = this.getWorkspaces();
    return !workspaces.some(ws => 
      ws.name.toLowerCase() === name.toLowerCase() && ws.id !== excludeId
    );
  }

  static getWorkspaceSelectOptions(): Array<{ value: string; label: string }> {
    const workspaces = this.getWorkspaces();
    return workspaces.map(ws => ({
      value: ws.id,
      label: ws.name
    }));
  }
}

export default WorkspaceManager;
