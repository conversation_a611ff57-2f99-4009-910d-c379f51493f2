import React, { useState } from 'react';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export const AIChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      // Call AI service to generate request
      const result = await window.electronAPI.ai.generateRequest(userMessage.content);
      
      let aiResponse = '';
      if (result.success && result.request) {
        aiResponse = `I've generated a request for you:\n\n` +
          `Method: ${result.request.method}\n` +
          `URL: ${result.request.url}\n` +
          `Name: ${result.request.name}`;
      } else {
        aiResponse = result.error || 'Sorry, I couldn\'t generate a request from that description.';
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error: any) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error while processing your request.',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="ai-chat">
      <div className="chat-header">
        <h3>AI Assistant</h3>
        <span className="status-indicator">●</span>
      </div>
      
      <div className="chat-messages">
        {messages.length === 0 ? (
          <div className="welcome-message">
            <p>👋 Hi! I'm your AI assistant.</p>
            <p>Describe what you want to test and I'll help you create the perfect API request.</p>
            <div className="example-prompts">
              <p><strong>Try saying:</strong></p>
              <ul>
                <li>"Get user profile from GitHub API"</li>
                <li>"Create a POST request to send user data"</li>
                <li>"Test the weather API for London"</li>
              </ul>
            </div>
          </div>
        ) : (
          messages.map(message => (
            <div key={message.id} className={`message ${message.type}`}>
              <div className="message-content">
                {message.content}
              </div>
              <div className="message-time">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
        
        {loading && (
          <div className="message ai">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="chat-input">
        <textarea
          className="input-field"
          placeholder="Describe the API request you want to create..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={loading}
          rows={3}
        />
        <button
          className="send-button"
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || loading}
        >
          Send
        </button>
      </div>
    </div>
  );
};
