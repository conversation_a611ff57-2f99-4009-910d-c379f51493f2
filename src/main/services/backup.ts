import * as fs from 'fs';
import * as path from 'path';
import * as cron from 'node-cron';

export interface BackupConfig {
  enabled: boolean;
  interval: string; // cron expression
  location: string;
  maxBackups: number;
}

export class BackupService {
  private config: BackupConfig;
  private task: cron.ScheduledTask | null = null;

  constructor() {
    this.config = {
      enabled: false,
      interval: '0 2 * * *', // Daily at 2 AM
      location: './backups',
      maxBackups: 7
    };
  }

  async configure(config: Partial<BackupConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    
    if (this.config.enabled) {
      await this.startScheduledBackups();
    } else {
      this.stopScheduledBackups();
    }
  }

  async startScheduledBackups(): Promise<void> {
    if (this.task) {
      this.task.stop();
    }

    this.task = cron.schedule(this.config.interval, async () => {
      await this.createBackup();
    });
  }

  stopScheduledBackups(): void {
    if (this.task) {
      this.task.stop();
      this.task = null;
    }
  }

  async createBackup(): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      // Ensure backup directory exists
      if (!fs.existsSync(this.config.location)) {
        fs.mkdirSync(this.config.location, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `apicool-backup-${timestamp}.json`;
      const backupPath = path.join(this.config.location, backupFileName);

      // TODO: Implement actual backup logic
      // For now, create a placeholder backup
      const backupData = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        data: {
          // TODO: Export all workspace data
        }
      };

      fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));

      // Clean up old backups
      await this.cleanupOldBackups();

      return {
        success: true,
        filePath: backupPath
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Backup failed'
      };
    }
  }

  async restoreBackup(filePath: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: 'Backup file not found'
        };
      }

      const backupData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      // TODO: Implement restore logic
      console.log('Restoring backup from:', backupData.timestamp);

      return { success: true };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Restore failed'
      };
    }
  }

  async listBackups(): Promise<Array<{ fileName: string; filePath: string; timestamp: Date; size: number }>> {
    try {
      if (!fs.existsSync(this.config.location)) {
        return [];
      }

      const files = fs.readdirSync(this.config.location);
      const backupFiles = files
        .filter(file => file.startsWith('apicool-backup-') && file.endsWith('.json'))
        .map(file => {
          const filePath = path.join(this.config.location, file);
          const stats = fs.statSync(filePath);
          return {
            fileName: file,
            filePath: filePath,
            timestamp: stats.mtime,
            size: stats.size
          };
        })
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      return backupFiles;

    } catch (error) {
      console.error('Error listing backups:', error);
      return [];
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await this.listBackups();
      
      if (backups.length > this.config.maxBackups) {
        const backupsToDelete = backups.slice(this.config.maxBackups);
        
        for (const backup of backupsToDelete) {
          fs.unlinkSync(backup.filePath);
        }
      }
    } catch (error) {
      console.error('Error cleaning up old backups:', error);
    }
  }

  getConfig(): BackupConfig {
    return { ...this.config };
  }
}
