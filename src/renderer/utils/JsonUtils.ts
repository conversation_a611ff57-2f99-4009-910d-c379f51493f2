/**
 * Utility class for loading editor data from the database
 */
export class EditorDataLoader {
  /**
   * Load collection data from database
   */
  static async loadCollectionData(collectionId: string): Promise<any> {
    try {
      if (window.electronAPI) {
        const entityId = parseInt(collectionId);
        const collectionDetails = await window.electronAPI.workspace.getCollectionDetails(entityId);

        return {
          id: collectionId,
          name: collectionDetails.name,
          description: collectionDetails.description || '',
          version: '1.0.0', // TODO: Add version field to database
          baseUrl: '', // TODO: Add baseUrl field to database
          variables: JsonUtils.tryParse(collectionDetails.variables, {}),
          auth: { type: 'none' }, // TODO: Add auth field to database
          preRequestScript: '', // TODO: Add preRequestScript field to database
          testScript: '', // TODO: Add testScript field to database
          documentation: '', // TODO: Add documentation field to database
          tags: [] // TODO: Add tags field to database
        };
      }
    } catch (error) {
      console.error('Failed to load collection data:', error);
    }

    // Return default data if loading fails
    return {
      id: collectionId,
      name: 'Untitled Collection',
      description: '',
      version: '1.0.0',
      baseUrl: '',
      variables: {},
      auth: { type: 'none' },
      preRequestScript: '',
      testScript: '',
      documentation: '',
      tags: []
    };
  }

  /**
   * Load folder data from database
   */
  static async loadFolderData(folderId: string): Promise<any> {
    try {
      if (window.electronAPI) {
        const entityId = parseInt(folderId);
        const folderDetails = await window.electronAPI.workspace.getFolderDetails(entityId);

        return {
          id: folderId,
          name: folderDetails.name,
          description: folderDetails.description || '',
          variables: {}, // TODO: Add variables field to database
          auth: { type: 'none' }, // TODO: Add auth field to database
          preRequestScript: '', // TODO: Add preRequestScript field to database
          testScript: '' // TODO: Add testScript field to database
        };
      }
    } catch (error) {
      console.error('Failed to load folder data:', error);
    }

    // Return default data if loading fails
    return {
      id: folderId,
      name: 'Untitled Folder',
      description: '',
      variables: {},
      auth: { type: 'none' },
      preRequestScript: '',
      testScript: ''
    };
  }

  /**
   * Load request data from database
   */
  static async loadRequestData(requestId: string): Promise<any> {
    try {
      if (window.electronAPI) {
        const entityId = parseInt(requestId);
        const requestDetails = await window.electronAPI.workspace.getRequestDetails(entityId);

        return {
          id: requestId,
          name: requestDetails.name,
          method: requestDetails.method,
          url: requestDetails.url,
          headers: requestDetails.headers || {},
          params: requestDetails.params || {},
          body: requestDetails.body || '',
          description: requestDetails.description || '',
          auth: { type: 'none' }, // TODO: Add auth field to database
          preScript: '', // TODO: Add preScript field to database
          testScript: '' // TODO: Add testScript field to database
        };
      }
    } catch (error) {
      console.error('Failed to load request data:', error);
    }

    // Return default data if loading fails
    return {
      id: requestId,
      name: 'Untitled Request',
      method: 'GET',
      url: '',
      headers: {},
      params: {},
      body: '',
      description: '',
      auth: { type: 'none' },
      preScript: '',
      testScript: ''
    };
  }

  /**
   * Load example data from database
   */
  static async loadExampleData(exampleId: string, requestId: string): Promise<any> {
    try {
      if (window.electronAPI) {
        const entityId = parseInt(exampleId);
        const exampleDetails = await window.electronAPI.workspace.getExampleDetails(entityId);

        const responseData = JsonUtils.tryParse(exampleDetails.response, {}) as any;

        return {
          id: exampleId,
          name: exampleDetails.name,
          description: '', // TODO: Add description field to database
          requestId: requestId,
          request: {
            method: exampleDetails.request?.method || 'GET',
            url: exampleDetails.request?.url || '',
            headers: {},
            params: {},
            body: ''
          },
          response: {
            status: responseData.status || 200,
            statusText: responseData.statusText || 'OK',
            headers: responseData.headers || {},
            body: responseData.body || '',
            responseTime: responseData.responseTime || 0
          },
          notes: '', // TODO: Add notes field to database
          tags: [] // TODO: Add tags field to database
        };
      }
    } catch (error) {
      console.error('Failed to load example data:', error);
    }

    // Return default data if loading fails
    return {
      id: exampleId,
      name: 'Untitled Example',
      description: '',
      requestId: requestId,
      request: {
        method: 'GET',
        url: '',
        headers: {},
        params: {},
        body: ''
      },
      response: {
        status: 200,
        statusText: 'OK',
        headers: {},
        body: '',
        responseTime: 0
      },
      notes: '',
      tags: []
    };
  }
}

/**
 * Utility class for safe JSON operations
 */
export class JsonUtils {
  /**
   * Safely parse JSON string, returning default value if parsing fails
   * @param jsonString - The JSON string to parse
   * @param defaultValue - The default value to return if parsing fails (defaults to empty object)
   * @returns Parsed object or default value
   */
  static tryParse<T = any>(jsonString: string | null | undefined, defaultValue: T = {} as T): T {
    if (!jsonString || typeof jsonString !== 'string') {
      return defaultValue;
    }

    try {
      const parsed = JSON.parse(jsonString);
      return parsed !== null && parsed !== undefined ? parsed : defaultValue;
    } catch (error) {
      console.warn('Failed to parse JSON:', error, 'Input:', jsonString);
      return defaultValue;
    }
  }

  /**
   * Safely stringify an object, returning empty string if stringification fails
   * @param obj - The object to stringify
   * @param defaultValue - The default value to return if stringification fails (defaults to empty string)
   * @returns JSON string or default value
   */
  static tryStringify(obj: any, defaultValue: string = ''): string {
    if (obj === null || obj === undefined) {
      return defaultValue;
    }

    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.warn('Failed to stringify object:', error, 'Input:', obj);
      return defaultValue;
    }
  }

  /**
   * Check if a string is valid JSON
   * @param jsonString - The string to validate
   * @returns true if valid JSON, false otherwise
   */
  static isValidJson(jsonString: string | null | undefined): boolean {
    if (!jsonString || typeof jsonString !== 'string') {
      return false;
    }

    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }
}
