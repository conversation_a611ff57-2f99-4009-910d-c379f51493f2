// Core Types
export interface User {
  id: number;
  email: string;
  name?: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

export interface Workspace {
  id: number;
  name: string;
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Item {
  id: number;
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  entityId: number;
  workspaceId: number;
  parentId: number | null;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  children?: Item[];
  
  // Entity data (only one will be populated based on type)
  collection?: Collection;
  folder?: Folder;
  request?: Request;
  example?: Example;
}

export interface Collection {
  id: number;
  name: string;
  description?: string;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Folder {
  id: number;
  name: string;
  description?: string;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Request {
  id: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
  examples?: Example[];
}

export interface Example {
  id: number;
  name: string;
  response: any;
  requestId: number;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

// Request/Response Types
export interface RequestData {
  id?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  workspaceId: number;
}

export interface RequestResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: any;
  responseTime: number;
  size: number;
}

export interface ExecuteRequestResult {
  success: boolean;
  response?: RequestResponse;
  error?: string;
}

// AI Types
export interface AIProvider {
  name: string;
  type: 'local' | 'remote';
  endpoint?: string;
  apiKey?: string;
}

export interface GenerateRequestResult {
  success: boolean;
  request?: {
    name: string;
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: any;
  };
  error?: string;
}

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  mode: ThemeMode;
  customColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    surface?: string;
    text?: string;
  };
}

// Internationalization Types
export type SupportedLanguage = 'en' | 'es' | 'fr' | 'de' | 'zh' | 'ja';

export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
}

export interface TranslationData {
  [key: string]: string | TranslationData;
}

// Backup Types
export interface BackupConfig {
  enabled: boolean;
  interval: string; // cron expression
  location: string;
  maxBackups: number;
}

export interface BackupInfo {
  fileName: string;
  filePath: string;
  timestamp: Date;
  size: number;
}

// Logging Types
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  metadata?: any;
}

// Collection Types
export interface PostmanCollection {
  info: {
    name: string;
    description?: string;
    schema: string;
  };
  item: PostmanItem[];
}

export interface PostmanItem {
  name: string;
  request?: {
    method: string;
    header: Array<{ key: string; value: string }>;
    url: string | { raw: string };
    body?: any;
  };
  item?: PostmanItem[];
}

// Environment Types
export interface Environment {
  id: number;
  name: string;
  variables: Record<string, string>;
  workspaceId: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Test Types
export interface TestScript {
  id: number;
  name: string;
  script: string;
  requestId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

// Collection Run Types
export interface CollectionRun {
  id: number;
  collectionId: number;
  results: TestResult[];
  startedAt: Date;
  completedAt?: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  workspaceId: number;
}

// Settings Types
export interface Settings {
  id: number;
  key: string;
  value: any;
  userId?: number;
  workspaceId?: number;
  createdAt: Date;
  updatedAt: Date;
}

// UI State Types
export interface UIState {
  sidebarVisible: boolean;
  activeWorkspaceId: number | null;
  selectedItemId: number | null;
  activeRequestId: number | null;
  theme: ThemeMode;
  language: SupportedLanguage;
}

// Form Types
export interface CreateWorkspaceForm {
  name: string;
  description?: string;
}

export interface CreateCollectionForm {
  name: string;
  description?: string;
  workspaceId: number;
}

export interface CreateFolderForm {
  name: string;
  description?: string;
  workspaceId: number;
  parentId?: number;
}

export interface CreateRequestForm {
  name: string;
  method: string;
  url: string;
  workspaceId: number;
  parentId?: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Event Types
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: Date;
}

// Drag and Drop Types
export interface DragItem {
  id: number;
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  name: string;
}

export interface DropResult {
  itemId: number;
  parentId: number | null;
  order: number;
}

// Search Types
export interface SearchResult {
  id: number;
  type: 'collection' | 'folder' | 'request' | 'example';
  name: string;
  description?: string;
  workspaceId: number;
  parentPath?: string;
}

export interface SearchOptions {
  query: string;
  workspaceId?: number;
  types?: Array<'collection' | 'folder' | 'request' | 'example'>;
  limit?: number;
}

// Performance Types
export interface PerformanceMetrics {
  responseTime: number;
  requestSize: number;
  responseSize: number;
  timestamp: Date;
}

export interface PerformanceTest {
  id: number;
  name: string;
  requestId: number;
  iterations: number;
  concurrency: number;
  results: PerformanceMetrics[];
  createdAt: Date;
}
