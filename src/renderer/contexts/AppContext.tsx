import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// Import the new TabItem interface
export interface TabItem {
  id: string;
  type: 'request' | 'collection' | 'folder' | 'example' | 'environment' | 'history';
  data: any; // The full item data fetched from database
  hasUnsavedChanges?: boolean;
}

// Import KeyValuePair type
import { KeyValuePair } from '../types/request';

// Legacy Request interface for backward compatibility
export interface Request {
  id: string; // Hierarchy item ID for tabs
  entityId?: number; // Database entity ID for operations
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string> | KeyValuePair[];
  params?: Record<string, string> | KeyValuePair[];
  body?: string;
  hasUnsavedChanges?: boolean;
}

export interface Environment {
  id: string;
  name: string;
  variables: Record<string, string>;
}

export interface Workspace {
  id: number;
  name: string;
  description?: string;
}

export interface AppState {
  // Tab Management
  openTabs: TabItem[];
  activeTab: string;

  // Workspace Management
  currentWorkspace: Workspace | null;
  workspaces: Workspace[];

  // Environment Management
  environments: Environment[];
  activeEnvironment: string;

  // UI State
  leftPanelWidth: number;
  workspaceSection: 'collections' | 'environments' | 'history';
  isChatOpen: boolean;
  theme: 'light' | 'dark' | 'auto';

  // Collection Tree State
  expandedCollections: string[];
  expandedFolders: string[];

  // Dialog States
  isImportDialogOpen: boolean;
  isExportDialogOpen: boolean;
  isWorkspaceManagementOpen: boolean;
  isCreateWorkspaceOpen: boolean;
  isSaveDialogOpen: boolean;
  isQuickSearchOpen: boolean;

  // Toast Messages
  toastMessage: string;

  // Request to Save
  requestToSave: Request | null;
  exportCollection: any;

  // Request Execution State
  isExecutingRequest: boolean;
  currentResponse: RequestResult | null;
}

// Action Types
export type AppAction =
  | { type: 'SET_OPEN_TABS'; payload: TabItem[] }
  | { type: 'ADD_TAB'; payload: TabItem }
  | { type: 'REMOVE_TAB'; payload: string }
  | { type: 'UPDATE_TAB'; payload: { id: string; updates: Partial<TabItem> } }
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_WORKSPACE'; payload: Workspace | null }
  | { type: 'SET_WORKSPACES'; payload: Workspace[] }
  | { type: 'SET_ENVIRONMENTS'; payload: Environment[] }
  | { type: 'SET_ACTIVE_ENVIRONMENT'; payload: string }
  | { type: 'SET_LEFT_PANEL_WIDTH'; payload: number }
  | { type: 'SET_WORKSPACE_SECTION'; payload: 'collections' | 'environments' | 'history' }
  | { type: 'SET_CHAT_OPEN'; payload: boolean }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' | 'auto' }
  | { type: 'TOGGLE_COLLECTION'; payload: string }
  | { type: 'TOGGLE_FOLDER'; payload: string }
  | { type: 'SET_EXPANDED_COLLECTIONS'; payload: string[] }
  | { type: 'SET_EXPANDED_FOLDERS'; payload: string[] }
  | { type: 'SET_IMPORT_DIALOG_OPEN'; payload: boolean }
  | { type: 'SET_EXPORT_DIALOG_OPEN'; payload: boolean }
  | { type: 'SET_WORKSPACE_MANAGEMENT_OPEN'; payload: boolean }
  | { type: 'SET_CREATE_WORKSPACE_OPEN'; payload: boolean }
  | { type: 'SET_SAVE_DIALOG_OPEN'; payload: boolean }
  | { type: 'SET_QUICK_SEARCH_OPEN'; payload: boolean }
  | { type: 'SET_TOAST_MESSAGE'; payload: string }
  | { type: 'SET_REQUEST_TO_SAVE'; payload: Request | null }
  | { type: 'SET_EXPORT_COLLECTION'; payload: any }
  | { type: 'SET_EXECUTING_REQUEST'; payload: boolean }
  | { type: 'SET_CURRENT_RESPONSE'; payload: RequestResult | null }
  | { type: 'LOAD_STATE'; payload: Partial<AppState> };

// Import service utilities
import { ItemServiceUtils, initializeServices } from '../services/ItemServiceRegistry';
import useRequestExecutor, { RequestResult, RequestExecutorData, Variable } from '../components/RequestExecutor';

// Initial State
const initialState: AppState = {
  openTabs: [],
  activeTab: '',
  currentWorkspace: null,
  workspaces: [],
  environments: [],
  activeEnvironment: '',
  leftPanelWidth: 360,
  workspaceSection: 'collections',
  isChatOpen: false,
  theme: 'auto',
  expandedCollections: [],
  expandedFolders: [],
  isImportDialogOpen: false,
  isExportDialogOpen: false,
  isWorkspaceManagementOpen: false,
  isCreateWorkspaceOpen: false,
  isSaveDialogOpen: false,
  isQuickSearchOpen: false,
  toastMessage: '',
  requestToSave: null,
  exportCollection: null,
  isExecutingRequest: false,
  currentResponse: null,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_OPEN_TABS':
      return { ...state, openTabs: action.payload };

    case 'ADD_TAB': {
      const existingIndex = state.openTabs.findIndex(tab => tab.id === action.payload.id);
      if (existingIndex >= 0) {
        // Update existing tab
        const updatedTabs = [...state.openTabs];
        updatedTabs[existingIndex] = { ...updatedTabs[existingIndex], ...action.payload };
        return { ...state, openTabs: updatedTabs, activeTab: action.payload.id };
      } else {
        // Add new tab
        return {
          ...state,
          openTabs: [...state.openTabs, action.payload],
          activeTab: action.payload.id
        };
      }
    }

    case 'REMOVE_TAB': {
      const newTabs = state.openTabs.filter(tab => tab.id !== action.payload);
      let newActiveTab = state.activeTab;

      // If removing active tab, switch to another tab
      if (state.activeTab === action.payload && newTabs.length > 0) {
        const currentIndex = state.openTabs.findIndex(tab => tab.id === action.payload);
        const nextIndex = Math.min(currentIndex, newTabs.length - 1);
        newActiveTab = newTabs[nextIndex]?.id || '';
      } else if (newTabs.length === 0) {
        newActiveTab = '';
      }

      return { ...state, openTabs: newTabs, activeTab: newActiveTab };
    }

    case 'UPDATE_TAB': {
      const updatedTabs = state.openTabs.map(tab =>
        tab.id === action.payload.id ? { ...tab, ...action.payload.updates } : tab
      );
      return { ...state, openTabs: updatedTabs };
    }

    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload };

    case 'SET_CURRENT_WORKSPACE':
      return { ...state, currentWorkspace: action.payload };

    case 'SET_WORKSPACES':
      return { ...state, workspaces: action.payload };

    case 'SET_ENVIRONMENTS':
      return { ...state, environments: action.payload };

    case 'SET_ACTIVE_ENVIRONMENT':
      return { ...state, activeEnvironment: action.payload };

    case 'SET_LEFT_PANEL_WIDTH':
      return { ...state, leftPanelWidth: action.payload };

    case 'SET_WORKSPACE_SECTION':
      return { ...state, workspaceSection: action.payload };

    case 'SET_CHAT_OPEN':
      return { ...state, isChatOpen: action.payload };

    case 'SET_THEME':
      return { ...state, theme: action.payload };

    case 'TOGGLE_COLLECTION': {
      const isExpanded = state.expandedCollections.includes(action.payload);
      const newExpanded = isExpanded
        ? state.expandedCollections.filter(id => id !== action.payload)
        : [...state.expandedCollections, action.payload];
      return { ...state, expandedCollections: newExpanded };
    }

    case 'TOGGLE_FOLDER': {
      const isExpanded = state.expandedFolders.includes(action.payload);
      const newExpanded = isExpanded
        ? state.expandedFolders.filter(id => id !== action.payload)
        : [...state.expandedFolders, action.payload];
      return { ...state, expandedFolders: newExpanded };
    }

    case 'SET_EXPANDED_COLLECTIONS':
      return { ...state, expandedCollections: action.payload };

    case 'SET_EXPANDED_FOLDERS':
      return { ...state, expandedFolders: action.payload };

    case 'SET_IMPORT_DIALOG_OPEN':
      return { ...state, isImportDialogOpen: action.payload };

    case 'SET_EXPORT_DIALOG_OPEN':
      return { ...state, isExportDialogOpen: action.payload };

    case 'SET_WORKSPACE_MANAGEMENT_OPEN':
      return { ...state, isWorkspaceManagementOpen: action.payload };

    case 'SET_CREATE_WORKSPACE_OPEN':
      return { ...state, isCreateWorkspaceOpen: action.payload };

    case 'SET_SAVE_DIALOG_OPEN':
      return { ...state, isSaveDialogOpen: action.payload };

    case 'SET_QUICK_SEARCH_OPEN':
      return { ...state, isQuickSearchOpen: action.payload };

    case 'SET_TOAST_MESSAGE':
      return { ...state, toastMessage: action.payload };

    case 'SET_REQUEST_TO_SAVE':
      return { ...state, requestToSave: action.payload };

    case 'SET_EXPORT_COLLECTION':
      return { ...state, exportCollection: action.payload };

    case 'SET_EXECUTING_REQUEST':
      return { ...state, isExecutingRequest: action.payload };

    case 'SET_CURRENT_RESPONSE':
      return { ...state, currentResponse: action.payload };

    case 'LOAD_STATE':
      return { ...state, ...action.payload };

    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;

  // Tab Management APIs
  addTab: (tab: Request) => void;
  addTabItem: (tabItem: TabItem) => void;
  removeTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<Request>) => void;
  updateTabItem: (tabId: string, updates: Partial<TabItem>) => void;
  setActiveTab: (tabId: string) => void;
  closeAllTabs: () => void;

  // Workspace Management APIs
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  setWorkspaces: (workspaces: Workspace[]) => void;

  // Environment Management APIs
  setEnvironments: (environments: Environment[]) => void;
  setActiveEnvironment: (environmentId: string) => void;

  // UI State APIs
  setLeftPanelWidth: (width: number) => void;
  setWorkspaceSection: (section: 'collections' | 'environments' | 'history') => void;
  setChatOpen: (isOpen: boolean) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;

  // Collection Tree APIs
  toggleCollection: (collectionId: string) => void;
  toggleFolder: (folderId: string) => void;
  setExpandedCollections: (collections: string[]) => void;
  setExpandedFolders: (folders: string[]) => void;

  // Dialog APIs
  setImportDialogOpen: (isOpen: boolean) => void;
  setExportDialogOpen: (isOpen: boolean) => void;
  setWorkspaceManagementOpen: (isOpen: boolean) => void;
  setCreateWorkspaceOpen: (isOpen: boolean) => void;
  setSaveDialogOpen: (isOpen: boolean) => void;
  setQuickSearchOpen: (isOpen: boolean) => void;

  // Toast APIs
  showToast: (message: string, duration?: number) => void;
  hideToast: () => void;

  // Request Save APIs
  setRequestToSave: (request: Request | null) => void;
  setExportCollection: (collection: any) => void;

  // Request Execution APIs
  sendRequest: (tabItem: TabItem) => Promise<void>;
  setExecutingRequest: (isExecuting: boolean) => void;
  setCurrentResponse: (response: RequestResult | null) => void;

  // Persistence APIs
  saveState: () => void;
  loadState: () => void;
}

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider Component
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize request executor
  const { executeRequest } = useRequestExecutor({
    onRequestResult: (result: RequestResult) => {
      dispatch({ type: 'SET_CURRENT_RESPONSE', payload: result });
      dispatch({ type: 'SET_EXECUTING_REQUEST', payload: false });
    },
    onError: (error: string) => {
      console.error('Request execution error:', error);
      dispatch({ type: 'SET_EXECUTING_REQUEST', payload: false });
      // TODO: Show error toast
    }
  });

  // Initialize services on mount
  useEffect(() => {
    initializeServices();
  }, []);

  // Persistence APIs
  const saveState = () => {
    try {
      const stateToSave = {
        openTabs: state.openTabs,
        activeTab: state.activeTab,
        leftPanelWidth: state.leftPanelWidth,
        workspaceSection: state.workspaceSection,
        isChatOpen: state.isChatOpen,
        theme: state.theme,
        expandedCollections: state.expandedCollections,
        expandedFolders: state.expandedFolders,
        activeEnvironment: state.activeEnvironment,
      };
      localStorage.setItem('apicool-app-state', JSON.stringify(stateToSave));
      console.log('App state saved to localStorage');
    } catch (error) {
      console.error('Failed to save app state:', error);
    }
  };

  const loadState = () => {
    try {
      const savedState = localStorage.getItem('apicool-app-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        console.log('Loading app state from localStorage:', parsedState);
        dispatch({ type: 'LOAD_STATE', payload: parsedState });
      } else {
        console.log('No saved app state found');
      }
    } catch (error) {
      console.error('Failed to load app state:', error);
    }
  };

  // Load state from localStorage on mount
  useEffect(() => {
    loadState();
  }, []);

  // Save state to localStorage when relevant state changes (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveState();
    }, 500); // Debounce saves by 500ms

    return () => clearTimeout(timeoutId);
  }, [
    state.openTabs,
    state.activeTab,
    state.leftPanelWidth,
    state.workspaceSection,
    state.isChatOpen,
    state.theme,
    state.expandedCollections,
    state.expandedFolders,
    state.activeEnvironment
  ]);

  // Tab Management APIs
  const addTab = (tab: Request) => {
    // Convert Request to TabItem using service utilities
    const tabItem: TabItem = {
      id: tab.id,
      type: 'request',
      data: {
        entityId: tab.entityId,
        name: tab.name,
        method: tab.method,
        url: tab.url,
        headers: tab.headers,
        params: tab.params,
        body: tab.body
      },
      hasUnsavedChanges: tab.hasUnsavedChanges
    };
    dispatch({ type: 'ADD_TAB', payload: tabItem });
  };

  // New function to add any type of tab item
  const addTabItem = (tabItem: TabItem) => {
    dispatch({ type: 'ADD_TAB', payload: tabItem });
  };

  const removeTab = (tabId: string) => {
    dispatch({ type: 'REMOVE_TAB', payload: tabId });
  };

  const updateTab = (tabId: string, updates: Partial<Request>) => {
    console.log(`[AppContext] updateTab called for tab: ${tabId}`, updates);

    // Convert Request updates to TabItem updates for requests
    const tabItem = state.openTabs.find(tab => tab.id === tabId);
    console.log(`[AppContext] Found tab item:`, tabItem);

    if (tabItem && tabItem.type === 'request') {
      const tabItemUpdates: Partial<TabItem> = {
        data: { ...tabItem.data, ...updates },
        hasUnsavedChanges: updates.hasUnsavedChanges
      };
      console.log(`[AppContext] Dispatching tab updates:`, tabItemUpdates);
      dispatch({ type: 'UPDATE_TAB', payload: { id: tabId, updates: tabItemUpdates } });
    } else {
      console.log(`[AppContext] Tab not found or not a request type for tab: ${tabId}`);
    }
  };

  const updateTabItem = (tabId: string, updates: Partial<TabItem>) => {
    dispatch({ type: 'UPDATE_TAB', payload: { id: tabId, updates } });
  };

  const setActiveTab = (tabId: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tabId });
  };

  const closeAllTabs = () => {
    dispatch({ type: 'SET_OPEN_TABS', payload: [] });
    dispatch({ type: 'SET_ACTIVE_TAB', payload: '' });
  };

  // Workspace Management APIs
  const setCurrentWorkspace = (workspace: Workspace | null) => {
    dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: workspace });
  };

  const setWorkspaces = (workspaces: Workspace[]) => {
    dispatch({ type: 'SET_WORKSPACES', payload: workspaces });
  };

  // Environment Management APIs
  const setEnvironments = (environments: Environment[]) => {
    dispatch({ type: 'SET_ENVIRONMENTS', payload: environments });
  };

  const setActiveEnvironment = (environmentId: string) => {
    dispatch({ type: 'SET_ACTIVE_ENVIRONMENT', payload: environmentId });
  };

  // UI State APIs
  const setLeftPanelWidth = (width: number) => {
    dispatch({ type: 'SET_LEFT_PANEL_WIDTH', payload: width });
  };

  const setWorkspaceSection = (section: 'collections' | 'environments' | 'history') => {
    dispatch({ type: 'SET_WORKSPACE_SECTION', payload: section });
  };

  const setChatOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_CHAT_OPEN', payload: isOpen });
  };

  const setTheme = (theme: 'light' | 'dark' | 'auto') => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };

  // Collection Tree APIs
  const toggleCollection = (collectionId: string) => {
    dispatch({ type: 'TOGGLE_COLLECTION', payload: collectionId });
  };

  const toggleFolder = (folderId: string) => {
    dispatch({ type: 'TOGGLE_FOLDER', payload: folderId });
  };

  const setExpandedCollections = (collections: string[]) => {
    dispatch({ type: 'SET_EXPANDED_COLLECTIONS', payload: collections });
  };

  const setExpandedFolders = (folders: string[]) => {
    dispatch({ type: 'SET_EXPANDED_FOLDERS', payload: folders });
  };

  // Dialog APIs
  const setImportDialogOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_IMPORT_DIALOG_OPEN', payload: isOpen });
  };

  const setExportDialogOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_EXPORT_DIALOG_OPEN', payload: isOpen });
  };

  const setWorkspaceManagementOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_WORKSPACE_MANAGEMENT_OPEN', payload: isOpen });
  };

  const setCreateWorkspaceOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_CREATE_WORKSPACE_OPEN', payload: isOpen });
  };

  const setSaveDialogOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_SAVE_DIALOG_OPEN', payload: isOpen });
  };

  const setQuickSearchOpen = (isOpen: boolean) => {
    dispatch({ type: 'SET_QUICK_SEARCH_OPEN', payload: isOpen });
  };

  // Toast APIs
  const showToast = (message: string, duration: number = 3000) => {
    dispatch({ type: 'SET_TOAST_MESSAGE', payload: message });
    if (duration > 0) {
      setTimeout(() => {
        dispatch({ type: 'SET_TOAST_MESSAGE', payload: '' });
      }, duration);
    }
  };

  const hideToast = () => {
    dispatch({ type: 'SET_TOAST_MESSAGE', payload: '' });
  };

  // Request Save APIs
  const setRequestToSave = (request: Request | null) => {
    dispatch({ type: 'SET_REQUEST_TO_SAVE', payload: request });
  };

  const setExportCollection = (collection: any) => {
    dispatch({ type: 'SET_EXPORT_COLLECTION', payload: collection });
  };

  // Request Execution APIs
  const sendRequest = async (tabItem: TabItem) => {
    if (tabItem.type !== 'request') {
      console.error('Cannot send request: tab item is not a request');
      return;
    }

    dispatch({ type: 'SET_EXECUTING_REQUEST', payload: true });
    dispatch({ type: 'SET_CURRENT_RESPONSE', payload: null });

    try {
      // Get environment variables (convert to Variable format)
      const activeEnv = state.environments?.find(env => env.id === state.activeEnvironment);
      const environmentVariables: Record<string, Variable> = {};

      // Convert environment variables to Variable format if needed
      if (activeEnv?.variables) {
        Object.entries(activeEnv.variables).forEach(([key, value]) => {
          environmentVariables[key] = {
            name: key,
            initialValue: String(value),
            currentValue: String(value),
            type: 'string'
          };
        });
      }

      // Convert TabItem to RequestExecutorData format
      const requestData: RequestExecutorData = {
        id: tabItem.id,
        name: tabItem.data.name,
        method: tabItem.data.method,
        url: tabItem.data.url,
        headers: tabItem.data.headers,
        body: tabItem.data.body ? JSON.parse(tabItem.data.body) : undefined
      };

      // Get workspace ID and environment name
      const workspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);
      const environmentName = activeEnv?.name;

      await executeRequest(requestData, environmentVariables, {}, workspaceId, environmentName);
    } catch (error) {
      console.error('Failed to send request:', error);
      dispatch({ type: 'SET_EXECUTING_REQUEST', payload: false });
    }
  };

  const setExecutingRequest = (isExecuting: boolean) => {
    dispatch({ type: 'SET_EXECUTING_REQUEST', payload: isExecuting });
  };

  const setCurrentResponse = (response: RequestResult | null) => {
    dispatch({ type: 'SET_CURRENT_RESPONSE', payload: response });
  };

  const contextValue: AppContextType = {
    state,
    dispatch,

    // Tab Management APIs
    addTab,
    addTabItem,
    removeTab,
    updateTab,
    updateTabItem,
    setActiveTab,
    closeAllTabs,

    // Workspace Management APIs
    setCurrentWorkspace,
    setWorkspaces,

    // Environment Management APIs
    setEnvironments,
    setActiveEnvironment,

    // UI State APIs
    setLeftPanelWidth,
    setWorkspaceSection,
    setChatOpen,
    setTheme,

    // Collection Tree APIs
    toggleCollection,
    toggleFolder,
    setExpandedCollections,
    setExpandedFolders,

    // Dialog APIs
    setImportDialogOpen,
    setExportDialogOpen,
    setWorkspaceManagementOpen,
    setCreateWorkspaceOpen,
    setSaveDialogOpen,
    setQuickSearchOpen,

    // Toast APIs
    showToast,
    hideToast,

    // Request Save APIs
    setRequestToSave,
    setExportCollection,

    // Request Execution APIs
    sendRequest,
    setExecutingRequest,
    setCurrentResponse,

    // Persistence APIs
    saveState,
    loadState,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Hook to use the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
