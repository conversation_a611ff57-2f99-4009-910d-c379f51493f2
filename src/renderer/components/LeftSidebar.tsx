import React, { useState } from 'react';

interface LeftSidebarProps {
  activeSection: 'collections' | 'environments' | 'history';
  onSectionChange: (section: 'collections' | 'environments' | 'history') => void;
}

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  activeSection,
  onSectionChange
}) => {
  const sections = [
    {
      id: 'collections' as const,
      icon: '📁',
      label: 'Collections',
      tooltip: 'Collections'
    },
    {
      id: 'environments' as const,
      icon: '🌍',
      label: 'Environments',
      tooltip: 'Environments'
    },
    {
      id: 'history' as const,
      icon: '🕒',
      label: 'History',
      tooltip: 'History'
    }
  ];

  return (
    <div className="left-sidebar">
      {sections.map(section => (
        <button
          key={section.id}
          className={`sidebar-section-btn ${activeSection === section.id ? 'active' : ''}`}
          onClick={() => onSectionChange(section.id)}
          title={section.tooltip}
        >
          <span className="sidebar-section-icon">{section.icon}</span>
          <span className="sidebar-section-label">{section.label}</span>
        </button>
      ))}
    </div>
  );
};

export default LeftSidebar;
