import React, { useState, useEffect } from 'react';
import { RequestData } from '../../../utils/types';
import { HTTP_METHODS } from '../../../utils/constants';

interface RequestEditorProps {
  request: RequestData | null;
  onExecute: (request: RequestData) => void;
  onSave: (request: RequestData) => void;
  loading: boolean;
}

export const RequestEditor: React.FC<RequestEditorProps> = ({
  request,
  onExecute,
  onSave,
  loading
}) => {
  const [formData, setFormData] = useState<RequestData>({
    name: 'New Request',
    method: 'GET',
    url: '',
    headers: {},
    body: null,
    workspaceId: 1 // TODO: Get from context
  });

  const [activeTab, setActiveTab] = useState<'headers' | 'body'>('headers');
  const [headersText, setHeadersText] = useState('');
  const [bodyText, setBodyText] = useState('');

  useEffect(() => {
    if (request) {
      setFormData(request);
      
      // Convert headers object to text
      const headersStr = Object.entries(request.headers || {})
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
      setHeadersText(headersStr);
      
      // Convert body to text
      if (request.body) {
        setBodyText(typeof request.body === 'string' ? request.body : JSON.stringify(request.body, null, 2));
      } else {
        setBodyText('');
      }
    }
  }, [request]);

  const handleFieldChange = (field: keyof RequestData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleHeadersChange = (value: string) => {
    setHeadersText(value);
    
    // Parse headers text into object
    const headers: Record<string, string> = {};
    value.split('\n').forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        const val = line.substring(colonIndex + 1).trim();
        if (key && val) {
          headers[key] = val;
        }
      }
    });
    
    handleFieldChange('headers', headers);
  };

  const handleBodyChange = (value: string) => {
    setBodyText(value);
    
    // Try to parse as JSON, otherwise keep as string
    let body: any = value;
    try {
      if (value.trim()) {
        body = JSON.parse(value);
      } else {
        body = null;
      }
    } catch {
      // Keep as string if not valid JSON
    }
    
    handleFieldChange('body', body);
  };

  const handleExecute = () => {
    if (formData.url.trim()) {
      onExecute(formData);
    }
  };

  const handleSave = () => {
    if (formData.name.trim() && formData.url.trim()) {
      onSave(formData);
    }
  };

  return (
    <div className="request-editor">
      <div className="request-header">
        <div className="request-line">
          <select
            className="method-select"
            value={formData.method}
            onChange={(e) => handleFieldChange('method', e.target.value)}
          >
            {HTTP_METHODS.map(method => (
              <option key={method} value={method}>{method}</option>
            ))}
          </select>
          
          <input
            type="text"
            className="url-input"
            placeholder="Enter request URL"
            value={formData.url}
            onChange={(e) => handleFieldChange('url', e.target.value)}
          />
          
          <button
            className="btn btn-primary"
            onClick={handleExecute}
            disabled={loading || !formData.url.trim()}
          >
            {loading ? 'Sending...' : 'Send'}
          </button>
        </div>
        
        <div className="request-name">
          <input
            type="text"
            className="name-input"
            placeholder="Request name"
            value={formData.name}
            onChange={(e) => handleFieldChange('name', e.target.value)}
          />
          
          <button
            className="btn btn-outline btn-sm"
            onClick={handleSave}
            disabled={!formData.name.trim() || !formData.url.trim()}
          >
            Save
          </button>
        </div>
      </div>
      
      <div className="request-tabs">
        <button
          className={`tab ${activeTab === 'headers' ? 'active' : ''}`}
          onClick={() => setActiveTab('headers')}
        >
          Headers
        </button>
        <button
          className={`tab ${activeTab === 'body' ? 'active' : ''}`}
          onClick={() => setActiveTab('body')}
        >
          Body
        </button>
      </div>
      
      <div className="request-content">
        {activeTab === 'headers' && (
          <div className="headers-editor">
            <textarea
              className="headers-textarea"
              placeholder="key: value&#10;content-type: application/json"
              value={headersText}
              onChange={(e) => handleHeadersChange(e.target.value)}
            />
          </div>
        )}
        
        {activeTab === 'body' && (
          <div className="body-editor">
            <textarea
              className="body-textarea"
              placeholder="Request body (JSON, text, etc.)"
              value={bodyText}
              onChange={(e) => handleBodyChange(e.target.value)}
            />
          </div>
        )}
      </div>
    </div>
  );
};
