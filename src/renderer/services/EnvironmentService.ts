import { BaseItemService } from './BaseItemService';
import { FullEnvironmentData } from '../types/environment';

export class EnvironmentService implements BaseItemService<FullEnvironmentData> {
  async create(data: Partial<FullEnvironmentData>, workspaceId: number): Promise<FullEnvironmentData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const environment = await window.electronAPI.environment.create({
        name: data.name || 'Untitled Environment',
        variables: Object.entries(data.variables || {}).map(([key, value]) => ({
          key,
          value,
          enabled: true
        })),
        workspaceId
      });

      return {
        id: environment.id,
        entityId: environment.id,
        name: environment.name,
        variables: environment.variables?.reduce((acc: Record<string, string>, variable: any) => {
          if (variable.enabled) {
            acc[variable.key] = variable.value;
          }
          return acc;
        }, {}) || {},
        description: data.description || '',
        createdAt: environment.createdAt,
        updatedAt: environment.updatedAt
      };
    } catch (error) {
      console.error('Failed to create environment:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<FullEnvironmentData>): Promise<FullEnvironmentData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const updated = await window.electronAPI.environment.update(entityId, {
        name: data.name,
        variables: Object.entries(data.variables || {}).map(([key, value]) => ({
          key,
          value,
          enabled: true
        }))
      });

      return {
        id: updated.id,
        entityId: updated.id,
        name: updated.name,
        variables: updated.variables?.reduce((acc: Record<string, string>, variable: any) => {
          if (variable.enabled) {
            acc[variable.key] = variable.value;
          }
          return acc;
        }, {}) || {},
        description: data.description || '',
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      };
    } catch (error) {
      console.error('Failed to update environment:', error);
      throw error;
    }
  }

  async get(id: string | number): Promise<FullEnvironmentData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      // Environment service doesn't have a direct get method,
      // we need to get all environments and find the one we want
      const workspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);
      const environments = await window.electronAPI.environment.getAll(workspaceId);

      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const environment = environments.find((env: any) => env.id === entityId);

      if (!environment) return null;

      return {
        id: environment.id,
        entityId: environment.id,
        name: environment.name,
        variables: environment.variables?.reduce((acc: Record<string, string>, variable: any) => {
          if (variable.enabled) {
            acc[variable.key] = variable.value;
          }
          return acc;
        }, {}) || {},
        description: '',
        createdAt: environment.createdAt,
        updatedAt: environment.updatedAt
      };
    } catch (error) {
      console.error('Failed to get environment:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.environment.delete(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete environment:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // An environment is new if it doesn't have an entityId or has a temporary ID pattern
    return !data.entityId || 
           (typeof data.id === 'string' && data.id.startsWith('environment-'));
  }

  getDisplayName(data: any): string {
    return data.name || 'Untitled Environment';
  }

  getDefaultData(): Partial<FullEnvironmentData> {
    return {
      name: 'Untitled Environment',
      variables: {} as Record<string, string>,
      description: ''
    };
  }

  /**
   * Environments don't need save dialog - they save directly to current workspace
   */
  needsSaveDialog(data: any): boolean {
    return false;
  }
}
