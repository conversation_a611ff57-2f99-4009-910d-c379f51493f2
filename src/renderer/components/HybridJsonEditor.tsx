import React, { useState, useEffect } from 'react';
import MonacoJsonEditor from './MonacoJsonEditor';
import SimpleJsonEditor from './SimpleJsonEditor';

interface HybridJsonEditorProps {
  value: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  height?: string | number;
  theme?: 'vs-dark' | 'light';
  language?: string;
  placeholder?: string;
}

const HybridJsonEditor: React.FC<HybridJsonEditorProps> = (props) => {
  const [useMonaco, setUseMonaco] = useState(true);
  const [monacoFailed, setMonacoFailed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if we're in an environment that supports Monaco
    if (typeof window === 'undefined' || !window.Worker) {
      console.warn('Environment does not support Monaco Editor, using simple editor');
      setUseMonaco(false);
      setMonacoFailed(true);
      setIsLoading(false);
      return;
    }

    // Check if Monaco is already loaded
    if (window.monacoLoaded && window.monaco) {
      setUseMonaco(true);
      setMonacoFailed(false);
      setIsLoading(false);
      return;
    }

    // Listen for Monaco loaded event
    const handleMonacoLoaded = () => {
      setUseMonaco(true);
      setMonacoFailed(false);
      setIsLoading(false);
    };

    window.addEventListener('monacoLoaded', handleMonacoLoaded);

    // Set a timeout to fallback if Monaco takes too long
    const timeout = setTimeout(() => {
      if (!window.monacoLoaded) {
        console.warn('Monaco Editor loading timeout, falling back to simple editor');
        setUseMonaco(false);
        setMonacoFailed(true);
        setIsLoading(false);
      }
    }, 15000);

    return () => {
      window.removeEventListener('monacoLoaded', handleMonacoLoaded);
      clearTimeout(timeout);
    };
  }, []);

  const handleMonacoError = () => {
    console.warn('Monaco Editor failed to load, switching to simple editor');
    setUseMonaco(false);
    setMonacoFailed(true);
    setIsLoading(false);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="hybrid-json-editor loading">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '200px',
          color: 'var(--color-text-secondary)'
        }}>
          Loading editor...
        </div>
      </div>
    );
  }

  // If Monaco failed or is not available, use SimpleJsonEditor
  if (!useMonaco || monacoFailed) {
    return (
      <div className="hybrid-json-editor">
        <SimpleJsonEditor {...props} />
      </div>
    );
  }

  // Try Monaco first
  return (
    <div className="hybrid-json-editor">
      <MonacoJsonEditor
        {...props}
        onError={handleMonacoError}
      />
    </div>
  );
};

export default HybridJsonEditor;
