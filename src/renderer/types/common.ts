/**
 * Common type definitions shared across the application
 */

export interface BaseItemData {
  id?: string | number;
  entityId?: number;
  name: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BaseEntity {
  id: number;
  name: string;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface HierarchyItem {
  id: number;
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  entityId: number;
  workspaceId: number;
  parentId: number | null;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  children?: HierarchyItem[];
}

export interface CreateItemData {
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  entityId: number;
  workspaceId: number;
  parentId?: number | null;
  order?: number;
}

export interface UpdateItemData {
  name?: string;
  description?: string;
  parentId?: number | null;
  order?: number;
}

export interface AuthConfig {
  type: 'none' | 'bearer' | 'basic' | 'api-key' | 'oauth2';
  token?: string;
  username?: string;
  password?: string;
  key?: string;
  value?: string;
  clientId?: string;
  clientSecret?: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface Variable {
  key: string;
  value: string;
  description?: string;
  enabled: boolean;
}

export interface ScriptConfig {
  preRequestScript?: string;
  testScript?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SearchParams {
  query: string;
  type?: 'collection' | 'folder' | 'request' | 'example';
  workspaceId?: number;
}

export interface SearchResult {
  id: number;
  type: 'collection' | 'folder' | 'request' | 'example';
  name: string;
  description?: string;
  workspaceId: number;
  parentPath?: string;
}

export interface DragItem {
  id: number;
  type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
  name: string;
}

export interface DropResult {
  itemId: number;
  parentId: number | null;
  order: number;
}

export type ItemType = 'collection' | 'folder' | 'request' | 'example' | 'environment' | 'history';

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

export type ThemeMode = 'light' | 'dark' | 'auto';

export type SupportedLanguage = 'en' | 'zh' | 'es' | 'fr' | 'de' | 'ja' | 'ko';
