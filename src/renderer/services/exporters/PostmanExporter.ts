import {
  ExportResult,
  ExportOptions,
  PostmanCollection,
  PostmanItem,
  PostmanRequest,
  PostmanResponse,
  PostmanAuth,
  PostmanHeader,
  PostmanVariable,
  PostmanUrl,
  PostmanBody
} from '../../types/import-export';
import { WorkspaceItem } from '../../types/workspace';

export class PostmanExporter {
  static async exportCollection(
    collection: WorkspaceItem,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      if (collection.type !== 'collection') {
        return {
          success: false,
          filename: '',
          error: 'Only collections can be exported'
        };
      }

      const postmanCollection = this.convertToPostmanCollection(collection, options);
      
      return {
        success: true,
        data: postmanCollection,
        filename: `${collection.name.replace(/[^a-zA-Z0-9]/g, '_')}.postman_collection.json`
      };
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private static convertToPostmanCollection(
    collection: WorkspaceItem,
    options: ExportOptions
  ): PostmanCollection {
    const postmanCollection: PostmanCollection = {
      info: {
        name: collection.name,
        description: collection.description || '',
        version: '1.0.0',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [],
      variable: options.includeVariables ? this.convertVariables(collection) : undefined,
      auth: options.includeAuth ? this.convertAuth(collection) : undefined
    };

    // Convert child items
    if (collection.children) {
      postmanCollection.item = this.convertItems(collection.children, options);
    }

    return postmanCollection;
  }

  private static convertItems(items: WorkspaceItem[], options: ExportOptions): PostmanItem[] {
    return items.map(item => this.convertItem(item, options));
  }

  private static convertItem(item: WorkspaceItem, options: ExportOptions): PostmanItem {
    const postmanItem: PostmanItem = {
      name: item.name,
      description: item.description
    };

    if (item.type === 'folder') {
      // Convert folder
      if (item.children) {
        postmanItem.item = this.convertItems(item.children, options);
      }
    } else if (item.type === 'request') {
      // Convert request
      postmanItem.request = this.convertRequest(item, options);
      
      if (options.includeExamples) {
        postmanItem.response = this.convertExamples(item);
      }
    }

    return postmanItem;
  }

  private static convertRequest(item: WorkspaceItem, options: ExportOptions): PostmanRequest {
    const request: PostmanRequest = {
      method: item.method || 'GET',
      header: this.convertHeaders(item.headers || {}),
      url: this.convertUrl(item.url || '', item.params || {}),
      description: item.description
    };

    // Add body if present
    if (item.body) {
      request.body = this.convertBody(item.body);
    }

    // Add auth if included
    if (options.includeAuth) {
      request.auth = this.convertRequestAuth(item);
    }

    return request;
  }

  private static convertHeaders(headers: Record<string, string>): PostmanHeader[] {
    return Object.entries(headers).map(([key, value]) => ({
      key,
      value,
      disabled: false
    }));
  }

  private static convertUrl(url: string, params: Record<string, string>): PostmanUrl {
    const urlObj: PostmanUrl = {
      raw: url
    };

    // Add query parameters
    if (Object.keys(params).length > 0) {
      urlObj.query = Object.entries(params).map(([key, value]) => ({
        key,
        value,
        disabled: false
      }));
    }

    // Parse URL components
    try {
      const parsedUrl = new URL(url.replace(/\{\{[^}]+\}\}/g, 'placeholder'));
      urlObj.protocol = parsedUrl.protocol.replace(':', '');
      urlObj.host = parsedUrl.hostname.split('.');
      urlObj.port = parsedUrl.port;
      urlObj.path = parsedUrl.pathname.split('/').filter(Boolean);
    } catch {
      // If URL parsing fails, keep it as raw
    }

    return urlObj;
  }

  private static convertBody(body: string): PostmanBody {
    try {
      // Try to parse as JSON
      JSON.parse(body);
      return {
        mode: 'raw',
        raw: body,
        options: {
          raw: {
            language: 'json'
          }
        }
      };
    } catch {
      // If not JSON, treat as raw text
      return {
        mode: 'raw',
        raw: body
      };
    }
  }

  private static convertAuth(collection: WorkspaceItem): PostmanAuth | undefined {
    // This would convert collection-level auth
    // For now, return undefined as we don't have auth data in WorkspaceItem
    return undefined;
  }

  private static convertRequestAuth(item: WorkspaceItem): PostmanAuth | undefined {
    // This would convert request-level auth
    // For now, return undefined as we don't have auth data in WorkspaceItem
    return undefined;
  }

  private static convertVariables(collection: WorkspaceItem): PostmanVariable[] {
    // This would convert collection variables
    // For now, return empty array as we don't have variables in WorkspaceItem
    return [];
  }

  private static convertExamples(item: WorkspaceItem): PostmanResponse[] {
    // This would convert request examples
    // For now, return empty array as we don't have examples in WorkspaceItem
    return [];
  }

  static async downloadExport(exportResult: ExportResult): Promise<void> {
    if (!exportResult.success || !exportResult.data) {
      throw new Error(exportResult.error || 'Export failed');
    }

    const jsonString = JSON.stringify(exportResult.data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = exportResult.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }
}

export default PostmanExporter;
