import React, { useState, useRef, useCallback } from 'react';
import { ImportResult, ExportOptions } from '../types/import-export';
import { WorkspaceItem } from '../types/workspace';
import ImportExportManager from '../services/ImportExportManager';

interface ImportExportDialogProps {
  isOpen: boolean;
  mode: 'import' | 'export';
  collection?: WorkspaceItem;
  onClose: () => void;
  onImportComplete: (results: ImportResult[]) => void;
  onExportComplete: (success: boolean) => void;
}

const ImportExportDialog: React.FC<ImportExportDialogProps> = ({
  isOpen,
  mode,
  collection,
  onClose,
  onImportComplete,
  onExportComplete
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResults, setImportResults] = useState<ImportResult[]>([]);
  const [githubUrl, setGithubUrl] = useState('');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'postman',
    includeExamples: true,
    includeTests: true,
    includeVariables: true,
    includeAuth: true
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    await processFiles(files);
  }, []);

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    await processFiles(files);
  }, []);

  const processFiles = async (files: File[]) => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    try {
      const results = await ImportExportManager.importMultipleFiles(files);
      setImportResults(results);
      onImportComplete(results);
    } catch (error) {
      console.error('Import failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGitHubImport = async () => {
    if (!githubUrl.trim()) return;
    
    setIsProcessing(true);
    try {
      const result = await ImportExportManager.importFromGitHub(githubUrl);
      setImportResults([result]);
      onImportComplete([result]);
    } catch (error) {
      console.error('GitHub import failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExport = async () => {
    if (!collection) return;
    
    setIsProcessing(true);
    try {
      const result = await ImportExportManager.exportCollection(
        collection,
        exportOptions.format,
        exportOptions
      );
      
      if (result.success) {
        await ImportExportManager.downloadExport(result);
        onExportComplete(true);
        onClose();
      } else {
        console.error('Export failed:', result.error);
        onExportComplete(false);
      }
    } catch (error) {
      console.error('Export failed:', error);
      onExportComplete(false);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="import-export-overlay">
      <div className="import-export-dialog">
        <div className="dialog-header">
          <h2>{mode === 'import' ? '📥 Import Collections' : '📤 Export Collection'}</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="dialog-content">
          {mode === 'import' ? (
            <>
              {/* File Drop Zone */}
              <div
                className={`drop-zone ${isDragging ? 'dragging' : ''}`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <div className="drop-zone-content">
                  <div className="drop-icon">📁</div>
                  <h3>Drop collection files here</h3>
                  <p>or click to browse files</p>
                  <div className="supported-formats">
                    <span className="format-badge">Postman</span>
                    <span className="format-badge">OpenAPI</span>
                    <span className="format-badge">Swagger</span>
                    <span className="format-badge">Insomnia</span>
                    <span className="format-badge">HAR</span>
                  </div>
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".json,.yaml,.yml,.har"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />

              {/* GitHub Import */}
              <div className="github-import">
                <h3>Import from GitHub</h3>
                <div className="github-input-group">
                  <input
                    type="url"
                    value={githubUrl}
                    onChange={(e) => setGithubUrl(e.target.value)}
                    placeholder="https://github.com/owner/repo"
                    className="github-input"
                  />
                  <button
                    className="btn btn-primary"
                    onClick={handleGitHubImport}
                    disabled={!githubUrl.trim() || isProcessing}
                  >
                    Import
                  </button>
                </div>
              </div>

              {/* Import Results */}
              {importResults.length > 0 && (
                <div className="import-results">
                  <h3>Import Results</h3>
                  {importResults.map((result, index) => (
                    <div key={index} className={`result-item ${result.success ? 'success' : 'error'}`}>
                      <div className="result-status">
                        {result.success ? '✅' : '❌'}
                      </div>
                      <div className="result-details">
                        {result.success ? (
                          <div>
                            <strong>{result.collections.length} collection(s) imported</strong>
                            {result.collections.map(col => (
                              <div key={col.id} className="collection-name">{col.name}</div>
                            ))}
                          </div>
                        ) : (
                          <div>
                            <strong>Import failed</strong>
                            {result.errors.map((error, i) => (
                              <div key={i} className="error-message">{error}</div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          ) : (
            <>
              {/* Export Options */}
              <div className="export-options">
                <div className="option-group">
                  <label>Export Format</label>
                  <select
                    value={exportOptions.format}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      format: e.target.value as any
                    }))}
                    className="format-select"
                  >
                    <option value="postman">Postman Collection</option>
                    <option value="openapi">OpenAPI 3.0</option>
                    <option value="insomnia">Insomnia</option>
                    <option value="har">HAR Archive</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>Include Options</label>
                  <div className="checkbox-group">
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeExamples}
                        onChange={(e) => setExportOptions(prev => ({
                          ...prev,
                          includeExamples: e.target.checked
                        }))}
                      />
                      Examples
                    </label>
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeTests}
                        onChange={(e) => setExportOptions(prev => ({
                          ...prev,
                          includeTests: e.target.checked
                        }))}
                      />
                      Tests
                    </label>
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeVariables}
                        onChange={(e) => setExportOptions(prev => ({
                          ...prev,
                          includeVariables: e.target.checked
                        }))}
                      />
                      Variables
                    </label>
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeAuth}
                        onChange={(e) => setExportOptions(prev => ({
                          ...prev,
                          includeAuth: e.target.checked
                        }))}
                      />
                      Authentication
                    </label>
                  </div>
                </div>

                <div className="collection-info">
                  <h3>Collection: {collection?.name}</h3>
                  <p>{collection?.description || 'No description'}</p>
                </div>
              </div>
            </>
          )}
        </div>

        <div className="dialog-footer">
          {mode === 'export' && (
            <button
              className="btn btn-primary"
              onClick={handleExport}
              disabled={isProcessing}
            >
              {isProcessing ? 'Exporting...' : 'Export Collection'}
            </button>
          )}
          <button className="btn btn-secondary" onClick={onClose}>
            {mode === 'import' ? 'Close' : 'Cancel'}
          </button>
        </div>

        {isProcessing && (
          <div className="processing-overlay">
            <div className="spinner"></div>
            <p>{mode === 'import' ? 'Processing imports...' : 'Exporting collection...'}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportExportDialog;
