import React, { useState, useEffect } from 'react';
import { EditorDataLoader } from '../../utils/JsonUtils';
import { FullCollectionData } from '../../types/collection';

interface CollectionEditorProps {
  collectionId: string;
  collectionName: string;
  onSave: (data: FullCollectionData) => void;
  onClose: () => void;
  onChange?: () => void; // Callback when data changes (to mark tab as dirty)
  readOnly?: boolean;
  initialData?: FullCollectionData; // Pre-loaded data to avoid redundant API calls
}

const CollectionEditor: React.FC<CollectionEditorProps> = ({
  collectionId,
  collectionName,
  onSave,
  onClose,
  onChange,
  readOnly = false,
  initialData
}) => {
  const [collectionData, setCollectionData] = useState<FullCollectionData>({
    id: collectionId,
    name: collectionName,
    description: '',
    version: '1.0.0',
    baseUrl: '',
    variables: {},
    auth: { type: 'none' },
    preRequestScript: '',
    testScript: '',
    documentation: '',
    tags: []
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'auth' | 'variables' | 'scripts' | 'docs'>('overview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Helper function to log and set hasUnsavedChanges
  const setHasUnsavedChangesWithLog = (value: boolean, reason: string) => {
    console.log(`[CollectionEditor] Setting hasUnsavedChanges to ${value}. Reason: ${reason}`);
    console.log(`[CollectionEditor] Collection ID: ${collectionId}, Collection Name: ${collectionName}`);
    console.trace(); // This will show the call stack
    setHasUnsavedChanges(value);

    // Also notify parent component if onChange is provided
    if (value && onChange) {
      console.log(`[CollectionEditor] Calling onChange callback due to: ${reason}`);
      onChange();
    }
  };
  const [newTag, setNewTag] = useState('');

  // Log component mount and props
  useEffect(() => {
    console.log(`[CollectionEditor] Component mounted with props:`, {
      collectionId,
      collectionName,
      readOnly,
      hasInitialData: !!initialData
    });
  }, []);

  // Log when collectionData changes
  useEffect(() => {
    console.log(`[CollectionEditor] collectionData changed:`, collectionData);
  }, [collectionData]);

  // Log when hasUnsavedChanges changes
  useEffect(() => {
    console.log(`[CollectionEditor] hasUnsavedChanges changed to: ${hasUnsavedChanges}`);
  }, [hasUnsavedChanges]);

  useEffect(() => {
    // Load collection data from database or use initial data
    const loadData = async () => {
      try {
        // Use initial data if provided, otherwise load from API
        if (initialData) {
          console.log('[CollectionEditor] Using pre-loaded initial data:', initialData);
          // Ensure the data has the expected structure
          const normalizedData = {
            ...initialData,
            variables: initialData.variables || {},
            tags: initialData.tags || [],
            auth: initialData.auth || { type: 'none' },
            preRequestScript: initialData.preRequestScript || '',
            testScript: initialData.testScript || '',
            documentation: initialData.documentation || ''
          };
          setCollectionData(normalizedData);
          setHasUnsavedChangesWithLog(false, 'Using pre-loaded initial data');
        } else {
          console.log('[CollectionEditor] Loading data from API for collection:', collectionId);
          const data = await EditorDataLoader.loadCollectionData(collectionId);
          setCollectionData(data);
          setHasUnsavedChangesWithLog(false, 'Data loaded from API'); // Mark as saved since we just loaded from database
        }
      } catch (error) {
        console.error('Failed to load collection data:', error);
        // Set default data if loading fails
        setCollectionData({
          id: collectionId,
          name: collectionName,
          description: '',
          version: '1.0.0',
          baseUrl: '',
          variables: {},
          auth: { type: 'none' },
          preRequestScript: '',
          testScript: '',
          documentation: '',
          tags: []
        });
        setHasUnsavedChangesWithLog(false, 'Set default data after loading failure');
      }
    };

    loadData();
  }, [collectionId, collectionName, initialData]);

  // Listen for keyboard shortcut save event
  useEffect(() => {
    const handleSaveShortcut = () => {
      if (hasUnsavedChanges) {
        handleSave();
      }
    };

    window.addEventListener('editor-save-shortcut', handleSaveShortcut);
    return () => window.removeEventListener('editor-save-shortcut', handleSaveShortcut);
  }, [hasUnsavedChanges, collectionData]);

  const handleInputChange = (field: keyof FullCollectionData, value: any) => {
    console.log(`[CollectionEditor] handleInputChange called - field: ${String(field)}, value:`, value);
    setCollectionData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChangesWithLog(true, `Input changed for field: ${String(field)}`);
  };

  const handleVariableChange = (key: string, value: string) => {
    console.log(`[CollectionEditor] handleVariableChange called - key: ${key}, value: ${value}`);
    setCollectionData(prev => ({
      ...prev,
      variables: { ...prev.variables || {}, [key]: value }
    }));
    setHasUnsavedChangesWithLog(true, `Variable changed: ${key} = ${value}`);
  };

  const addVariable = () => {
    const key = `variable_${Object.keys(collectionData.variables || {}).length + 1}`;
    handleVariableChange(key, '');
  };

  const removeVariable = (key: string) => {
    console.log(`[CollectionEditor] removeVariable called - key: ${key}`);
    setCollectionData(prev => {
      const newVariables = { ...prev.variables || {} };
      delete newVariables[key];
      return { ...prev, variables: newVariables };
    });
    setHasUnsavedChangesWithLog(true, `Variable removed: ${key}`);
  };

  const addTag = () => {
    console.log(`[CollectionEditor] addTag called - newTag: ${newTag}`);
    if (newTag.trim() && !collectionData.tags?.includes(newTag.trim())) {
      setCollectionData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
      setHasUnsavedChangesWithLog(true, `Tag added: ${newTag.trim()}`);
    }
  };

  const removeTag = (tag: string) => {
    console.log(`[CollectionEditor] removeTag called - tag: ${tag}`);
    setCollectionData(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag)
    }));
    setHasUnsavedChangesWithLog(true, `Tag removed: ${tag}`);
  };

  const handleSave = () => {
    console.log(`[CollectionEditor] handleSave called for collection: ${collectionId}`);
    onSave(collectionData);
    setHasUnsavedChangesWithLog(false, 'Collection saved successfully');
  };

  return (
    <div className="collection-editor">
      <div className="editor-header">
        <div className="editor-title">
          <span className="editor-icon">📚</span>
          <h2>{collectionData.name}</h2>
          <span className="version-badge">v{collectionData.version}</span>
          {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
        </div>
        <div className="editor-actions">
          {!readOnly && (
            <button 
              className="btn btn-primary" 
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              Save Collection
            </button>
          )}
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>

      <div className="editor-tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab ${activeTab === 'auth' ? 'active' : ''}`}
          onClick={() => setActiveTab('auth')}
        >
          Authorization
        </button>
        <button
          className={`tab ${activeTab === 'variables' ? 'active' : ''}`}
          onClick={() => setActiveTab('variables')}
        >
          Variables ({Object.keys(collectionData.variables || {}).length})
        </button>
        <button
          className={`tab ${activeTab === 'scripts' ? 'active' : ''}`}
          onClick={() => setActiveTab('scripts')}
        >
          Scripts
        </button>
        <button
          className={`tab ${activeTab === 'docs' ? 'active' : ''}`}
          onClick={() => setActiveTab('docs')}
        >
          Documentation
        </button>
      </div>

      <div className="editor-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="collection-name">Collection Name</label>
                <input
                  id="collection-name"
                  type="text"
                  value={collectionData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  readOnly={readOnly}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label htmlFor="collection-version">Version</label>
                <input
                  id="collection-version"
                  type="text"
                  value={collectionData.version}
                  onChange={(e) => handleInputChange('version', e.target.value)}
                  readOnly={readOnly}
                  className="form-input"
                  placeholder="1.0.0"
                />
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="collection-description">Description</label>
              <textarea
                id="collection-description"
                value={collectionData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                readOnly={readOnly}
                className="form-textarea"
                rows={3}
                placeholder="Describe what this collection contains..."
              />
            </div>

            <div className="form-group">
              <label htmlFor="base-url">Base URL</label>
              <input
                id="base-url"
                type="url"
                value={collectionData.baseUrl}
                onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                readOnly={readOnly}
                className="form-input"
                placeholder="https://api.example.com"
              />
            </div>

            <div className="form-group">
              <label>Tags</label>
              <div className="tags-container">
                <div className="tags-list">
                  {collectionData.tags?.map(tag => (
                    <span key={tag} className="tag">
                      {tag}
                      {!readOnly && (
                        <button
                          className="tag-remove"
                          onClick={() => removeTag(tag)}
                        >
                          ✕
                        </button>
                      )}
                    </span>
                  ))}
                </div>
                {!readOnly && (
                  <div className="tag-input-container">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      className="form-input tag-input"
                      placeholder="Add tag..."
                    />
                    <button className="btn btn-sm btn-primary" onClick={addTag}>
                      Add
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'auth' && (
          <div className="auth-tab">
            <div className="form-group">
              <label htmlFor="auth-type">Authorization Type</label>
              <select
                id="auth-type"
                value={collectionData.auth?.type || 'none'}
                onChange={(e) => handleInputChange('auth', { 
                  ...collectionData.auth, 
                  type: e.target.value as any 
                })}
                disabled={readOnly}
                className="form-select"
              >
                <option value="none">No Auth</option>
                <option value="bearer">Bearer Token</option>
                <option value="basic">Basic Auth</option>
                <option value="api-key">API Key</option>
              </select>
            </div>

            {collectionData.auth?.type === 'bearer' && (
              <div className="form-group">
                <label htmlFor="bearer-token">Token</label>
                <input
                  id="bearer-token"
                  type="text"
                  value={collectionData.auth.token || ''}
                  onChange={(e) => handleInputChange('auth', {
                    ...collectionData.auth,
                    token: e.target.value
                  })}
                  readOnly={readOnly}
                  className="form-input"
                  placeholder="Enter bearer token or variable like {{token}}"
                />
              </div>
            )}

            {collectionData.auth?.type === 'basic' && (
              <>
                <div className="form-group">
                  <label htmlFor="basic-username">Username</label>
                  <input
                    id="basic-username"
                    type="text"
                    value={collectionData.auth.username || ''}
                    onChange={(e) => handleInputChange('auth', {
                      ...collectionData.auth,
                      username: e.target.value
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="Enter username or variable like {{username}}"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="basic-password">Password</label>
                  <input
                    id="basic-password"
                    type="password"
                    value={collectionData.auth.password || ''}
                    onChange={(e) => handleInputChange('auth', {
                      ...collectionData.auth,
                      password: e.target.value
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="Enter password or variable like {{password}}"
                  />
                </div>
              </>
            )}

            {collectionData.auth?.type === 'api-key' && (
              <>
                <div className="form-group">
                  <label htmlFor="api-key-name">Key Name</label>
                  <input
                    id="api-key-name"
                    type="text"
                    value={collectionData.auth.key || ''}
                    onChange={(e) => handleInputChange('auth', {
                      ...collectionData.auth,
                      key: e.target.value
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="e.g., X-API-Key, Authorization"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="api-key-value">Key Value</label>
                  <input
                    id="api-key-value"
                    type="text"
                    value={collectionData.auth.value || ''}
                    onChange={(e) => handleInputChange('auth', {
                      ...collectionData.auth,
                      value: e.target.value
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="Enter API key value or variable like {{apiKey}}"
                  />
                </div>
              </>
            )}
          </div>
        )}

        {activeTab === 'variables' && (
          <div className="variables-tab">
            <div className="variables-header">
              <div className="variables-title">
                <h3>Collection Variables</h3>
                <p className="variables-description">
                  Variables can be used in requests using the syntax <code>{'{{variableName}}'}</code>
                </p>
              </div>
              {!readOnly && (
                <button className="btn btn-sm btn-primary" onClick={addVariable}>
                  + Add Variable
                </button>
              )}
            </div>
            <div className="variables-table-container">
              <table className="variables-table">
                <thead>
                  <tr>
                    <th style={{ width: '35%' }}>Variable Name</th>
                    <th style={{ width: '55%' }}>Value</th>
                    <th style={{ width: '10%' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(collectionData.variables || {}).map(([key, value]) => (
                    <tr key={key}>
                      <td>
                        <input
                          type="text"
                          value={key}
                          onChange={(e) => {
                            const newKey = e.target.value.trim();
                            // Validate variable name (alphanumeric and underscore only)
                            if (newKey && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(newKey)) {
                              return; // Don't allow invalid variable names
                            }
                            const newVariables = { ...collectionData.variables || {} };
                            delete newVariables[key];
                            if (newKey) {
                              newVariables[newKey] = value;
                            }
                            handleInputChange('variables', newVariables);
                          }}
                          readOnly={readOnly}
                          className="form-input variable-key"
                          placeholder="variable_name"
                          title="Variable names must start with a letter or underscore, followed by letters, numbers, or underscores"
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => handleVariableChange(key, e.target.value)}
                          readOnly={readOnly}
                          className="form-input variable-value"
                          placeholder="Variable value"
                          title={`Use this variable in requests as {{${key}}}`}
                        />
                      </td>
                      <td>
                        {!readOnly && (
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => removeVariable(key)}
                            title="Remove variable"
                          >
                            ✕
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                  {Object.keys(collectionData.variables || {}).length === 0 && (
                    <tr>
                      <td colSpan={3} className="no-variables">
                        No variables defined. Click "Add Variable" to create one.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'scripts' && (
          <div className="scripts-tab">
            <div className="script-section">
              <h3>Pre-request Script</h3>
              <p className="script-description">
                This script will run before every request in this collection
              </p>
              <textarea
                value={collectionData.preRequestScript || ''}
                onChange={(e) => handleInputChange('preRequestScript', e.target.value)}
                readOnly={readOnly}
                className="form-textarea script-editor"
                rows={10}
                placeholder="// Pre-request script for the entire collection"
              />
            </div>
            <div className="script-section">
              <h3>Test Script</h3>
              <p className="script-description">
                This script will run after every request in this collection
              </p>
              <textarea
                value={collectionData.testScript || ''}
                onChange={(e) => handleInputChange('testScript', e.target.value)}
                readOnly={readOnly}
                className="form-textarea script-editor"
                rows={10}
                placeholder="// Test script for the entire collection"
              />
            </div>
          </div>
        )}

        {activeTab === 'docs' && (
          <div className="docs-tab">
            <div className="form-group">
              <label htmlFor="documentation">Documentation (Markdown)</label>
              <textarea
                id="documentation"
                value={collectionData.documentation || ''}
                onChange={(e) => handleInputChange('documentation', e.target.value)}
                readOnly={readOnly}
                className="form-textarea docs-editor"
                rows={20}
                placeholder="# Collection Documentation

Write your collection documentation here using Markdown..."
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollectionEditor;
