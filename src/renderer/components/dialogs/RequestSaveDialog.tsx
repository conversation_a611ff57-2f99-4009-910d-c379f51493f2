import React, { useState, useEffect } from 'react';
import { WorkspaceItem } from '../../types/workspace';

interface RequestSaveDialogProps {
  isOpen: boolean;
  requestName: string;
  onClose: () => void;
  onSave: (parentId: string | null, name: string) => void;
}

interface HierarchyItem {
  id: string;
  name: string;
  type: 'collection' | 'folder';
  parentId: string | null;
  children: HierarchyItem[];
  expanded: boolean;
}

const RequestSaveDialog: React.FC<RequestSaveDialogProps> = ({
  isOpen,
  requestName,
  onClose,
  onSave
}) => {
  const [hierarchy, setHierarchy] = useState<HierarchyItem[]>([]);
  const [selectedParentId, setSelectedParentId] = useState<string | null>(null);
  const [editedName, setEditedName] = useState(requestName);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setEditedName(requestName);
      loadHierarchy();
    }
  }, [isOpen, requestName]);

  const loadHierarchy = async () => {
    setLoading(true);
    try {
      if (window.electronAPI) {
        // Get current workspace ID (TODO: get from context/state)
        const workspaceId = 1;
        const items = await window.electronAPI.workspace.getItemsLight(workspaceId);
        


        // Convert the already-built hierarchy to our format
        const convertToHierarchyItem = (item: any): HierarchyItem => {
          return {
            id: item.id.toString(),
            name: item.entity?.name || 'Unnamed',
            type: item.type.toLowerCase() as 'collection' | 'folder',
            parentId: item.parentId?.toString() || null,
            children: (item.children || [])
              .filter((child: any) => child.type === 'COLLECTION' || child.type === 'FOLDER')
              .map(convertToHierarchyItem),
            expanded: true // Auto-expand all items
          };
        };

        // Filter and convert root items (collections and folders without parents)
        const rootItems = items
          .filter((item: any) => item.type === 'COLLECTION' || item.type === 'FOLDER')
          .map(convertToHierarchyItem);



        setHierarchy(rootItems);

        // Auto-select first collection if available
        if (rootItems.length > 0) {
          setSelectedParentId(rootItems[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load hierarchy:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = (itemId: string) => {
    const updateExpanded = (items: HierarchyItem[]): HierarchyItem[] => {
      return items.map(item => {
        if (item.id === itemId) {
          return { ...item, expanded: !item.expanded };
        }
        if (item.children.length > 0) {
          return { ...item, children: updateExpanded(item.children) };
        }
        return item;
      });
    };
    setHierarchy(updateExpanded(hierarchy));
  };

  const handleSave = () => {
    if (!editedName.trim()) {
      return;
    }
    onSave(selectedParentId, editedName.trim());
  };

  const renderHierarchyItem = (item: HierarchyItem, level: number = 0): React.ReactNode => {
    const hasChildren = item.children.length > 0;
    const isSelected = selectedParentId === item.id;

    return (
      <div key={item.id}>
        <div
          className={`hierarchy-item ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${level * 20 + 12}px` }}
          onClick={() => setSelectedParentId(item.id)}
        >
          {hasChildren && (
            <button
              className="hierarchy-toggle"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(item.id);
              }}
            >
              {item.expanded ? '▼' : '▶'}
            </button>
          )}
          {!hasChildren && <span className="hierarchy-spacer"></span>}
          
          <span className="hierarchy-icon">
            {item.type === 'collection' ? '📚' : '📁'}
          </span>
          <span className="hierarchy-name">{item.name}</span>
        </div>
        
        {item.expanded && item.children.map(child => 
          renderHierarchyItem(child, level + 1)
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="dialog-overlay">
      <div className="dialog request-save-dialog">
        <div className="dialog-header">
          <h2>Save Request</h2>
          <button className="dialog-close" onClick={onClose}>×</button>
        </div>
        
        <div className="dialog-content">
          <div className="form-group">
            <label htmlFor="request-name">Request Name:</label>
            <input
              id="request-name"
              type="text"
              value={editedName}
              onChange={(e) => setEditedName(e.target.value)}
              placeholder="Enter request name"
              autoFocus
            />
          </div>
          
          <div className="form-group">
            <label>Save Location:</label>
            <div className="hierarchy-container">
              {loading ? (
                <div className="loading-state">Loading collections...</div>
              ) : hierarchy.length > 0 ? (
                <div className="hierarchy-list">
                  {hierarchy.map(item => renderHierarchyItem(item))}
                </div>
              ) : (
                <div className="empty-state">
                  No collections found. Create a collection first.
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="dialog-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button 
            className="btn btn-primary" 
            onClick={handleSave}
            disabled={!editedName.trim() || !selectedParentId}
          >
            Save Request
          </button>
        </div>
      </div>
    </div>
  );
};

export default RequestSaveDialog;
