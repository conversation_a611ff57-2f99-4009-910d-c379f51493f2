import React, { useState } from 'react';
import { RequestResponse } from '../../../utils/types';

interface ResponseViewerProps {
  response: RequestResponse | null;
  loading: boolean;
}

export const ResponseViewer: React.FC<ResponseViewerProps> = ({
  response,
  loading
}) => {
  const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');

  const formatResponseData = (data: any): string => {
    if (typeof data === 'string') {
      try {
        // Try to parse and format as JSON
        const parsed = JSON.parse(data);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return data;
      }
    } else if (typeof data === 'object') {
      return JSON.stringify(data, null, 2);
    }
    return String(data);
  };

  const formatHeaders = (headers: Record<string, string>): string => {
    return Object.entries(headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  };

  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return 'var(--color-success)';
    if (status >= 300 && status < 400) return 'var(--color-warning)';
    if (status >= 400) return 'var(--color-error)';
    return 'var(--color-text-secondary)';
  };

  const formatSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  if (loading) {
    return (
      <div className="response-viewer">
        <div className="response-header">
          <h3>Response</h3>
        </div>
        <div className="loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">Sending request...</div>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="response-viewer">
        <div className="response-header">
          <h3>Response</h3>
        </div>
        <div className="empty-state">
          <p>No response yet</p>
          <p className="text-secondary">Send a request to see the response here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="response-viewer">
      <div className="response-header">
        <h3>Response</h3>
        <div className="response-meta">
          <span 
            className="status-badge"
            style={{ color: getStatusColor(response.status) }}
          >
            {response.status} {response.statusText}
          </span>
          <span className="time-badge">
            {response.responseTime}ms
          </span>
          <span className="size-badge">
            {formatSize(response.size)}
          </span>
        </div>
      </div>
      
      <div className="response-tabs">
        <button
          className={`tab ${activeTab === 'body' ? 'active' : ''}`}
          onClick={() => setActiveTab('body')}
        >
          Body
        </button>
        <button
          className={`tab ${activeTab === 'headers' ? 'active' : ''}`}
          onClick={() => setActiveTab('headers')}
        >
          Headers ({Object.keys(response.headers).length})
        </button>
      </div>
      
      <div className="response-content">
        {activeTab === 'body' && (
          <div className="response-body">
            <pre className="response-text">
              {formatResponseData(response.data)}
            </pre>
          </div>
        )}
        
        {activeTab === 'headers' && (
          <div className="response-headers">
            <pre className="response-text">
              {formatHeaders(response.headers)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};
