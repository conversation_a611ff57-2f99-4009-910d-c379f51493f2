import React, { useState, useEffect } from 'react';
import ItemTabs, { TabItem } from './ItemTabs';
import RequestEditor from './RequestEditor';
import ResponseViewer from './ResponseViewer';
import ResizableSplitter from './ResizableSplitter';
import { RequestResult } from './RequestExecutor';
import FolderEditor from './editors/FolderEditor';
import CollectionEditor from './editors/CollectionEditor';
import ExampleEditor from './editors/ExampleEditor';
import EnvironmentEditor from './EnvironmentEditor';
import HistoryEntryViewer from './HistoryEntryViewer';
import { TabManagementService } from '../services/TabManagementService';
import { useAppContext } from '../contexts/AppContext';
import { KeyValuePair } from '../types/request';

// Utility function to convert KeyValuePair arrays to Record format for backward compatibility
const convertToRecord = (data: Record<string, string> | KeyValuePair[]): Record<string, string> => {
  if (Array.isArray(data)) {
    const record: Record<string, string> = {};
    data.forEach(item => {
      if (item.key && item.enabled !== false) {
        record[item.key] = item.value;
      }
    });
    return record;
  }
  return data;
};

interface Request {
  id: string;
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  params?: Record<string, string>;
  body?: string;
  hasUnsavedChanges?: boolean;
}

interface Environment {
  id: string;
  name: string;
  variables: Record<string, string>;
}

interface MainPanelProps {
  openTabs: TabItem[];
  activeTab: string;
  activeRequest?: TabItem;
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onForceCloseAll?: () => void;
  onNewTab: () => void;
  onUpdateRequest: (tabId: string, updates: Partial<Request>) => void;
  onUpdateTabItem?: (tabId: string, updates: Partial<TabItem>) => void;
  onItemSaved?: (type: 'collection' | 'folder' | 'request', item: any) => void;
  environments?: Environment[];
  activeEnvironment?: string;
  onEnvironmentChange?: (environmentId: string) => void;
}

const MainPanel: React.FC<MainPanelProps> = ({
  openTabs,
  activeTab,
  activeRequest,
  onTabChange,
  onTabClose,
  onForceCloseAll,
  onNewTab,
  onUpdateRequest,
  onUpdateTabItem,
  onItemSaved,
  environments,
  activeEnvironment,
  onEnvironmentChange
}) => {
  const [requestPanelHeight, setRequestPanelHeight] = useState<number>(400);

  // // Keyboard shortcut for save (Ctrl/Cmd+S)
  // useEffect(() => {
  //   const handleKeyDown = (event: KeyboardEvent) => {
  //     if ((event.ctrlKey || event.metaKey) && event.key === 's') {
  //       event.preventDefault();

  //       if (activeRequest) {
  //         if (isEditorTab(activeRequest)) {
  //           // For editor tabs, we need to trigger the save from the editor component
  //           // We'll dispatch a custom event that the editor components can listen to
  //           window.dispatchEvent(new CustomEvent('editor-save-shortcut'));
  //         } else {
  //           // For regular request tabs, use the unified save logic
  //           handleEditorSave(activeRequest.data);
  //         }
  //       }
  //     }
  //   };

  //   window.addEventListener('keydown', handleKeyDown);
  //   return () => window.removeEventListener('keydown', handleKeyDown);
  // }, [activeRequest]);
  // Use AppContext for request execution
  const { sendRequest, state: { isExecutingRequest, currentResponse } } = useAppContext();

  const handleSendRequest = async () => {
    if (!activeRequest) return;
    await sendRequest(activeRequest);
  };

  // Helper function to check if a tab is an editor tab
  const isEditorTab = (tabItem: TabItem): boolean => {
    return ['collection', 'folder', 'example', 'environment', 'history'].includes(tabItem.type);
  };

  // Helper function to get editor type from TabItem
  const getEditorType = (tabItem: TabItem): 'folder' | 'collection' | 'example' | 'environment' | 'history' | null => {
    if (!isEditorTab(tabItem)) return null;
    return tabItem.type as 'folder' | 'collection' | 'example' | 'environment' | 'history';
  };

  // Helper function to get editor ID from TabItem
  const getEditorId = (tabItem: TabItem): string => {
    // For new TabItem structure, the ID is stored in the data
    return tabItem.data.entityId?.toString() || tabItem.id;
  };

  // Helper function to get display name from TabItem
  const getDisplayName = (tabItem: TabItem): string => {
    return tabItem.data.name || 'Untitled';
  };

  // Unified editor save handler using TabManagementService
  const handleEditorSave = async (data: any, type?: 'collection' | 'folder' | 'request' | 'example') => {
    console.log('[MainPanel] handleEditorSave called with data:', data);
    console.log('[MainPanel] Type:', type);
    console.log('[MainPanel] Data headers:', data.headers);
    console.log('[MainPanel] Data params:', data.params);

    if (!activeRequest) return;

    try {
      const workspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);

      // Determine the type from activeRequest if not provided
      const saveType = type || activeRequest.type;

      // Create a TabItem with the updated data
      const tabItemToSave: TabItem = {
        ...activeRequest,
        data: data
      };

      // Use TabManagementService to save
      const result = await TabManagementService.saveTabItem(tabItemToSave, workspaceId);

      if (result.success) {
        console.log(`${saveType} saved successfully:`, result.data);

        // Notify parent about the new item if it was created
        if (data.id === 'new' && result.data && onItemSaved) {
          onItemSaved(saveType as any, result.data);
        }

        // Update tab to reflect saved state
        if (onUpdateTabItem) {
          onUpdateTabItem(activeRequest.id, {
            hasUnsavedChanges: false,
            data: result.data || data // Update with saved data or fallback to input data
          });
        }
      } else if (result.needsDialog) {
        // Handle case where save dialog is needed (for new requests)
        console.log(`Save dialog needed for ${saveType}`);
        // TODO: Trigger save dialog through parent component
      } else {
        throw new Error(result.error || `Failed to save ${saveType}`);
      }
    } catch (error) {
      console.error(`Failed to save ${activeRequest.type}:`, error);
      // TODO: Show error notification
    }
  };


  // Handle saving response as example (special case)
  const handleSaveAsExample = async (exampleData: any) => {
    try {
      const workspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);

      // Create example in database directly (this is a special case, not a tab edit)
      const example = await window.electronAPI.workspace.createExample({
        name: exampleData.name,
        requestId: parseInt(exampleData.requestId),
        response: JSON.stringify(exampleData.response),
        workspaceId: workspaceId
      });

      console.log('Example saved successfully:', example);
      // TODO: Show success notification
      // TODO: Refresh the collections panel to show the new example
    } catch (error) {
      console.error('Failed to save example:', error);
      // TODO: Show error notification
    }
  };

  const handleEditorClose = () => {
    if (activeRequest) {
      onTabClose(activeRequest.id);
    }
  };

  if (openTabs.length === 0) {
    return (
      <div className="main-panel">
        <div className="empty-state">
          <h2>Welcome to ApiCool</h2>
          <p>Select a request from the sidebar or create a new one to get started.</p>
          <button className="btn btn-primary" onClick={onNewTab}>
            + New Request
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="main-panel">
      <ItemTabs
        tabs={openTabs}
        activeTab={activeTab}
        onTabChange={onTabChange}
        onTabClose={onTabClose}
        onForceCloseAll={onForceCloseAll}
        onNewTab={onNewTab}
        onSaveItem={(tabId) => {
          const tab = openTabs.find(t => t.id === tabId);
          if (tab) {
            handleEditorSave(tab.data);
          }
        }}
        environments={environments}
        activeEnvironment={activeEnvironment}
        onEnvironmentChange={onEnvironmentChange}
      />
      
      {activeRequest ? (
        isEditorTab(activeRequest) ? (
          // Render editor components
          <div className="editor-content">
            {getEditorType(activeRequest) === 'folder' && (
              <FolderEditor
                folderId={getEditorId(activeRequest)}
                folderName={getDisplayName(activeRequest)}
                onSave={(data) => handleEditorSave(data, 'folder')}
                onClose={handleEditorClose}
                onChange={() => onUpdateRequest(activeRequest.id, { hasUnsavedChanges: true })}
                initialData={activeRequest.data} // Pass pre-loaded data
              />
            )}
            {getEditorType(activeRequest) === 'collection' && (
              <CollectionEditor
                collectionId={getEditorId(activeRequest)}
                collectionName={getDisplayName(activeRequest)}
                onSave={(data) => handleEditorSave(data, 'collection')}
                onClose={handleEditorClose}
                onChange={() => onUpdateRequest(activeRequest.id, { hasUnsavedChanges: true })}
                initialData={activeRequest.data} // Pass pre-loaded data
              />
            )}

            {getEditorType(activeRequest) === 'example' && (
              <ExampleEditor
                exampleId={getEditorId(activeRequest)}
                exampleName={getDisplayName(activeRequest)}
                requestId={getEditorId(activeRequest)}
                onSave={(data) => handleEditorSave(data, 'example')}
                onClose={handleEditorClose}
                onChange={() => onUpdateRequest(activeRequest.id, { hasUnsavedChanges: true })}
              />
            )}
            {getEditorType(activeRequest) === 'environment' && (
              <EnvironmentEditor
                workspaceId={parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10)}
                environmentId={getEditorId(activeRequest) === 'new' ? undefined : parseInt(getEditorId(activeRequest))}
                isNewEnvironment={getEditorId(activeRequest) === 'new'}
              />
            )}
            {getEditorType(activeRequest) === 'history' && (
              <HistoryEntryViewer
                workspaceId={parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10)}
                historyId={parseInt(getEditorId(activeRequest))}
                onOpenRequest={(historyEntry) => {
                  // Convert history entry to request format and open it
                  const request = {
                    id: `history-${historyEntry.id}`,
                    name: historyEntry.name,
                    method: historyEntry.method,
                    url: historyEntry.url,
                    headers: historyEntry.headers || {},
                    params: {},
                    body: typeof historyEntry.body === 'string' ? historyEntry.body : JSON.stringify(historyEntry.body, null, 2)
                  };
                  // We need to pass this up to the App component to open a new tab
                  // For now, we'll use a custom event
                  window.dispatchEvent(new CustomEvent('open-request-from-history', { detail: request }));
                }}
              />
            )}
          </div>
        ) : (
          // Render normal request editor
          <div className="panel-content resizable-layout vertical">
            <div
              className="request-section resizable-panel"
              style={{ height: requestPanelHeight }}
            >
              <RequestEditor
                requestId={getEditorId(activeRequest)}
                requestName={getDisplayName(activeRequest)}
                // onSave={(data) => handleEditorSave(data, 'request')}
                onClose={handleEditorClose}
                onSendRequest={handleSendRequest}
                onChange={(updatedData) => {
                  console.log('[MainPanel] RequestEditor onChange called with updatedData:', updatedData);
                  if (updatedData) {
                    // Update the tab with the new request data
                    onUpdateRequest(activeRequest.id, {
                      hasUnsavedChanges: true,
                      name: updatedData.name,
                      method: updatedData.method,
                      url: updatedData.url,
                      headers: convertToRecord(updatedData.headers),
                      params: convertToRecord(updatedData.params || {}),
                      body: updatedData.body
                    });
                  } else {
                    // Just mark as having unsaved changes
                    onUpdateRequest(activeRequest.id, { hasUnsavedChanges: true });
                  }
                }}
                initialData={activeRequest.data} // Pass pre-loaded data
              />
            </div>

            <ResizableSplitter
              direction="vertical"
              onResize={setRequestPanelHeight}
            />

            <div className="response-section resizable-panel" style={{ flex: 1 }}>
              <ResponseViewer
                response={currentResponse}
                loading={isExecutingRequest}
                requestId={activeRequest?.id}
                requestName={getDisplayName(activeRequest)}
                onSaveAsExample={handleSaveAsExample}
              />
            </div>
          </div>
        )
      ) : (
        <div className="empty-state">
          <h2>Welcome to ApiCool</h2>
          <p>Select a request from the sidebar or create a new one to get started.</p>
        </div>
      )}
    </div>
  );
};

export default MainPanel;
