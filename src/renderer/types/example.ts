/**
 * Example-related type definitions
 */

export interface Example {
  id: number;
  name: string;
  response: any;
  requestId: number;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ExampleData {
  id?: string | number;
  entityId?: number;
  name: string;
  description?: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: string;
  response?: ExampleResponse;
  requestId?: string | number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ExampleResponse {
  status: number;
  headers: Record<string, string>;
  body: string;
  responseTime?: number;
  size?: number;
}

export interface ExampleRequest {
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: string;
}

export interface ExampleMetadata {
  description?: string;
  tags?: string[];
  category?: string;
}

export interface FullExampleData extends ExampleData {
  request?: ExampleRequest;
  metadata?: ExampleMetadata;
}
