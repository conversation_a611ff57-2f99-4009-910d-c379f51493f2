import { PrismaClient } from '@prisma/client';
import { ChatMessage, ChatSession } from './ai-service';

export interface CreateChatSessionData {
  title: string;
  workspaceId: number;
}

export interface CreateChatMessageData {
  sessionId: number;
  workspaceId: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: string[];
  metadata?: any;
  generatedRequest?: any;
}

export class ChatService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get all chat sessions for a workspace
   */
  async getChatSessions(workspaceId: number): Promise<ChatSession[]> {
    try {
      const sessions = await this.prisma.chatSession.findMany({
        where: { workspaceId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            take: 1 // Only get the first message for preview
          }
        },
        orderBy: { updatedAt: 'desc' }
      });

      return sessions.map(session => ({
        id: session.id,
        title: session.title,
        workspaceId: session.workspaceId,
        messages: session.messages.map(msg => ({
          id: msg.id,
          sessionId: msg.sessionId,
          workspaceId: msg.workspaceId,
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
          images: msg.images as string[] || [],
          metadata: msg.metadata,
          generatedRequest: msg.generatedRequest,
          createdAt: msg.createdAt,
          updatedAt: msg.updatedAt
        })),
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }));
    } catch (error) {
      console.error('Error fetching chat sessions:', error);
      throw new Error('Failed to fetch chat sessions');
    }
  }

  /**
   * Get a specific chat session with all messages
   */
  async getChatSession(sessionId: number): Promise<ChatSession | null> {
    try {
      const session = await this.prisma.chatSession.findUnique({
        where: { id: sessionId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' }
          }
        }
      });

      if (!session) return null;

      return {
        id: session.id,
        title: session.title,
        workspaceId: session.workspaceId,
        messages: session.messages.map(msg => ({
          id: msg.id,
          sessionId: msg.sessionId,
          workspaceId: msg.workspaceId,
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
          images: msg.images as string[] || [],
          metadata: msg.metadata,
          generatedRequest: msg.generatedRequest,
          createdAt: msg.createdAt,
          updatedAt: msg.updatedAt
        })),
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      };
    } catch (error) {
      console.error('Error fetching chat session:', error);
      throw new Error('Failed to fetch chat session');
    }
  }

  /**
   * Create a new chat session
   */
  async createChatSession(data: CreateChatSessionData): Promise<ChatSession> {
    try {
      const session = await this.prisma.chatSession.create({
        data: {
          title: data.title,
          workspaceId: data.workspaceId
        },
        include: {
          messages: true
        }
      });

      return {
        id: session.id,
        title: session.title,
        workspaceId: session.workspaceId,
        messages: [],
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      };
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw new Error('Failed to create chat session');
    }
  }

  /**
   * Update chat session title
   */
  async updateChatSession(sessionId: number, title: string): Promise<ChatSession> {
    try {
      const session = await this.prisma.chatSession.update({
        where: { id: sessionId },
        data: { title },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' }
          }
        }
      });

      return {
        id: session.id,
        title: session.title,
        workspaceId: session.workspaceId,
        messages: session.messages.map(msg => ({
          id: msg.id,
          sessionId: msg.sessionId,
          workspaceId: msg.workspaceId,
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
          images: msg.images as string[] || [],
          metadata: msg.metadata,
          generatedRequest: msg.generatedRequest,
          createdAt: msg.createdAt,
          updatedAt: msg.updatedAt
        })),
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      };
    } catch (error) {
      console.error('Error updating chat session:', error);
      throw new Error('Failed to update chat session');
    }
  }

  /**
   * Delete a chat session
   */
  async deleteChatSession(sessionId: number): Promise<void> {
    try {
      await this.prisma.chatSession.delete({
        where: { id: sessionId }
      });
    } catch (error) {
      console.error('Error deleting chat session:', error);
      throw new Error('Failed to delete chat session');
    }
  }

  /**
   * Add a message to a chat session
   */
  async addChatMessage(data: CreateChatMessageData): Promise<ChatMessage> {
    try {
      const message = await this.prisma.chatMessage.create({
        data: {
          sessionId: data.sessionId,
          workspaceId: data.workspaceId,
          role: data.role,
          content: data.content,
          images: data.images || [],
          metadata: data.metadata,
          generatedRequest: data.generatedRequest
        }
      });

      // Update session's updatedAt timestamp
      await this.prisma.chatSession.update({
        where: { id: data.sessionId },
        data: { updatedAt: new Date() }
      });

      return {
        id: message.id,
        sessionId: message.sessionId,
        workspaceId: message.workspaceId,
        role: message.role as 'user' | 'assistant' | 'system',
        content: message.content,
        images: message.images as string[] || [],
        metadata: message.metadata,
        generatedRequest: message.generatedRequest,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt
      };
    } catch (error) {
      console.error('Error adding chat message:', error);
      throw new Error('Failed to add chat message');
    }
  }

  /**
   * Update a chat message
   */
  async updateChatMessage(messageId: number, data: Partial<CreateChatMessageData>): Promise<ChatMessage> {
    try {
      const message = await this.prisma.chatMessage.update({
        where: { id: messageId },
        data: {
          ...(data.content !== undefined && { content: data.content }),
          ...(data.metadata !== undefined && { metadata: data.metadata }),
          ...(data.generatedRequest !== undefined && { generatedRequest: data.generatedRequest }),
          updatedAt: new Date()
        }
      });

      return {
        id: message.id,
        sessionId: message.sessionId,
        workspaceId: message.workspaceId,
        role: message.role as 'user' | 'assistant' | 'system',
        content: message.content,
        images: message.images as string[] || [],
        metadata: message.metadata,
        generatedRequest: message.generatedRequest,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt
      };
    } catch (error) {
      console.error('Error updating chat message:', error);
      throw new Error('Failed to update chat message');
    }
  }

  /**
   * Delete a chat message
   */
  async deleteChatMessage(messageId: number): Promise<void> {
    try {
      await this.prisma.chatMessage.delete({
        where: { id: messageId }
      });
    } catch (error) {
      console.error('Error deleting chat message:', error);
      throw new Error('Failed to delete chat message');
    }
  }

  /**
   * Get messages for a chat session
   */
  async getChatMessages(sessionId: number): Promise<ChatMessage[]> {
    try {
      const messages = await this.prisma.chatMessage.findMany({
        where: { sessionId },
        orderBy: { createdAt: 'asc' }
      });

      return messages.map(msg => ({
        id: msg.id,
        sessionId: msg.sessionId,
        workspaceId: msg.workspaceId,
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
        images: msg.images as string[] || [],
        metadata: msg.metadata,
        generatedRequest: msg.generatedRequest,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt
      }));
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      throw new Error('Failed to fetch chat messages');
    }
  }

  /**
   * Clear all messages in a chat session
   */
  async clearChatSession(sessionId: number): Promise<void> {
    try {
      await this.prisma.chatMessage.deleteMany({
        where: { sessionId }
      });
    } catch (error) {
      console.error('Error clearing chat session:', error);
      throw new Error('Failed to clear chat session');
    }
  }
}
