import { ImportResult, ExportResult, ExportOptions } from '../types/import-export';
import { WorkspaceItem } from '../types/workspace';
import PostmanImporter from './importers/PostmanImporter';
import OpenAPIImporter from './importers/OpenAPIImporter';
import PostmanExporter from './exporters/PostmanExporter';

export interface ImportFile {
  name: string;
  content: string;
  type: 'postman' | 'openapi' | 'swagger' | 'insomnia' | 'har' | 'unknown';
}

export class ImportExportManager {
  // Import methods
  static async importFromFile(file: File): Promise<ImportResult> {
    try {
      const content = await this.readFileContent(file);
      const fileType = this.detectFileType(file.name, content);
      
      return await this.importFromContent(content, fileType);
    } catch (error) {
      return {
        success: false,
        collections: [],
        errors: [`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  static async importFromContent(content: string, type: string): Promise<ImportResult> {
    switch (type) {
      case 'postman':
        return await PostmanImporter.importCollection(content);
      
      case 'openapi':
      case 'swagger':
        return await OpenAPIImporter.importSpec(content);
      
      case 'insomnia':
        return await this.importInsomnia(content);
      
      case 'har':
        return await this.importHAR(content);
      
      default:
        return {
          success: false,
          collections: [],
          errors: [`Unsupported file type: ${type}`],
          warnings: []
        };
    }
  }

  static async importMultipleFiles(files: File[]): Promise<ImportResult[]> {
    const results: ImportResult[] = [];
    
    for (const file of files) {
      const result = await this.importFromFile(file);
      results.push(result);
    }
    
    return results;
  }

  // Export methods
  static async exportCollection(
    collection: WorkspaceItem,
    format: 'postman' | 'openapi' | 'insomnia' | 'har',
    options: ExportOptions
  ): Promise<ExportResult> {
    switch (format) {
      case 'postman':
        return await PostmanExporter.exportCollection(collection, options);
      
      case 'openapi':
        return await this.exportOpenAPI(collection, options);
      
      case 'insomnia':
        return await this.exportInsomnia(collection, options);
      
      case 'har':
        return await this.exportHAR(collection, options);
      
      default:
        return {
          success: false,
          filename: '',
          error: `Unsupported export format: ${format}`
        };
    }
  }

  static async downloadExport(exportResult: ExportResult): Promise<void> {
    return await PostmanExporter.downloadExport(exportResult);
  }

  // Utility methods
  private static async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        resolve(content);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }

  private static detectFileType(filename: string, content: string): string {
    const extension = filename.toLowerCase().split('.').pop();
    
    // Check by file extension first
    switch (extension) {
      case 'json':
        return this.detectJSONType(content);
      case 'yaml':
      case 'yml':
        return 'openapi';
      case 'har':
        return 'har';
      default:
        return this.detectJSONType(content);
    }
  }

  private static detectJSONType(content: string): string {
    try {
      const data = JSON.parse(content);
      
      // Check for Postman collection
      if (data.info && data.item && data.info.schema) {
        return 'postman';
      }
      
      // Check for OpenAPI/Swagger
      if (data.openapi || data.swagger) {
        return 'openapi';
      }
      
      // Check for Insomnia
      if (data._type === 'export' && data.resources) {
        return 'insomnia';
      }
      
      // Check for HAR
      if (data.log && data.log.version && data.log.entries) {
        return 'har';
      }
      
      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  // Placeholder implementations for other formats
  private static async importInsomnia(content: string): Promise<ImportResult> {
    // TODO: Implement Insomnia import
    return {
      success: false,
      collections: [],
      errors: ['Insomnia import not yet implemented'],
      warnings: []
    };
  }

  private static async importHAR(content: string): Promise<ImportResult> {
    // TODO: Implement HAR import
    return {
      success: false,
      collections: [],
      errors: ['HAR import not yet implemented'],
      warnings: []
    };
  }

  private static async exportOpenAPI(
    collection: WorkspaceItem,
    options: ExportOptions
  ): Promise<ExportResult> {
    // TODO: Implement OpenAPI export
    return {
      success: false,
      filename: '',
      error: 'OpenAPI export not yet implemented'
    };
  }

  private static async exportInsomnia(
    collection: WorkspaceItem,
    options: ExportOptions
  ): Promise<ExportResult> {
    // TODO: Implement Insomnia export
    return {
      success: false,
      filename: '',
      error: 'Insomnia export not yet implemented'
    };
  }

  private static async exportHAR(
    collection: WorkspaceItem,
    options: ExportOptions
  ): Promise<ExportResult> {
    // TODO: Implement HAR export
    return {
      success: false,
      filename: '',
      error: 'HAR export not yet implemented'
    };
  }

  // GitHub import (for importing from GitHub repositories)
  static async importFromGitHub(
    repoUrl: string,
    path?: string
  ): Promise<ImportResult> {
    try {
      // Extract owner and repo from URL
      const match = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
      if (!match) {
        return {
          success: false,
          collections: [],
          errors: ['Invalid GitHub repository URL'],
          warnings: []
        };
      }

      const [, owner, repo] = match;
      const apiUrl = `https://api.github.com/repos/${owner}/${repo}/contents/${path || ''}`;
      
      const response = await fetch(apiUrl);
      if (!response.ok) {
        return {
          success: false,
          collections: [],
          errors: [`Failed to fetch from GitHub: ${response.statusText}`],
          warnings: []
        };
      }

      const data = await response.json();
      
      // Handle single file
      if (data.type === 'file') {
        const content = atob(data.content);
        const fileType = this.detectFileType(data.name, content);
        return await this.importFromContent(content, fileType);
      }
      
      // Handle directory - look for collection files
      if (Array.isArray(data)) {
        const collectionFiles = data.filter((file: any) => 
          file.type === 'file' && 
          (file.name.endsWith('.json') || file.name.endsWith('.yaml') || file.name.endsWith('.yml'))
        );
        
        const results: ImportResult[] = [];
        
        for (const file of collectionFiles) {
          const fileResponse = await fetch(file.download_url);
          const content = await fileResponse.text();
          const fileType = this.detectFileType(file.name, content);
          const result = await this.importFromContent(content, fileType);
          results.push(result);
        }
        
        // Merge results
        const mergedResult: ImportResult = {
          success: results.some(r => r.success),
          collections: results.flatMap(r => r.collections),
          errors: results.flatMap(r => r.errors),
          warnings: results.flatMap(r => r.warnings)
        };
        
        return mergedResult;
      }
      
      return {
        success: false,
        collections: [],
        errors: ['No collection files found in the repository'],
        warnings: []
      };
      
    } catch (error) {
      return {
        success: false,
        collections: [],
        errors: [`GitHub import failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }
}

export default ImportExportManager;
