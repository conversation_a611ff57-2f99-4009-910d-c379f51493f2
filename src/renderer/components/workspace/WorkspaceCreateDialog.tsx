import React, { useState } from 'react';
import { Workspace, WorkspaceCreateRequest } from '../../types/workspace-management';
import WorkspaceManager from '../../services/WorkspaceManager';

interface WorkspaceCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onWorkspaceCreated: (workspace: Workspace) => void;
}

const WorkspaceCreateDialog: React.FC<WorkspaceCreateDialogProps> = ({
  isOpen,
  onClose,
  onWorkspaceCreated
}) => {
  const [formData, setFormData] = useState<WorkspaceCreateRequest>({
    name: '',
    description: '',
    copyFrom: undefined
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isCreating, setIsCreating] = useState(false);

  const workspaces = WorkspaceManager.getWorkspaces();

  const handleInputChange = (field: keyof WorkspaceCreateRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Workspace name is required';
    } else if (!WorkspaceManager.validateWorkspaceName(formData.name)) {
      newErrors.name = 'A workspace with this name already exists';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      const newWorkspace = WorkspaceManager.createWorkspace(formData);
      onWorkspaceCreated(newWorkspace);
      handleClose();
    } catch (error) {
      console.error('Failed to create workspace:', error);
      setErrors({ general: 'Failed to create workspace. Please try again.' });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setFormData({ name: '', description: '', copyFrom: undefined });
    setErrors({});
    setIsCreating(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="workspace-dialog-overlay">
      <div className="workspace-dialog">
        <div className="dialog-header">
          <h2>Create New Workspace</h2>
          <button className="close-btn" onClick={handleClose}>✕</button>
        </div>

        <form onSubmit={handleSubmit} className="dialog-content">
          {errors.general && (
            <div className="error-message general-error">
              {errors.general}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="workspace-name">Workspace Name *</label>
            <input
              id="workspace-name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`form-input ${errors.name ? 'error' : ''}`}
              placeholder="Enter workspace name"
              disabled={isCreating}
            />
            {errors.name && (
              <div className="error-message">{errors.name}</div>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="workspace-description">Description</label>
            <textarea
              id="workspace-description"
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="form-textarea"
              placeholder="Optional description for this workspace"
              rows={3}
              disabled={isCreating}
            />
          </div>

          <div className="form-group">
            <label htmlFor="copy-from">Copy Settings From</label>
            <select
              id="copy-from"
              value={formData.copyFrom || ''}
              onChange={(e) => handleInputChange('copyFrom', e.target.value)}
              className="form-select"
              disabled={isCreating}
            >
              <option value="">Use default settings</option>
              {workspaces.map(workspace => (
                <option key={workspace.id} value={workspace.id}>
                  {workspace.name}
                </option>
              ))}
            </select>
            <div className="form-help">
              Copy settings like theme, timeout, and proxy configuration from an existing workspace
            </div>
          </div>

          <div className="dialog-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleClose}
              disabled={isCreating}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isCreating || !formData.name.trim()}
            >
              {isCreating ? 'Creating...' : 'Create Workspace'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WorkspaceCreateDialog;
