/**
 * Folder-related type definitions
 */

export interface Folder {
  id: number;
  name: string;
  description?: string;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface FolderData {
  id?: string | number;
  entityId?: number;
  name: string;
  description?: string;
  parentId?: string | number;
  variables?: Record<string, string> | FolderVariable[];
  auth?: FolderAuth;
  preRequestScript?: string;
  testScript?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FolderAuth {
  type: 'none' | 'bearer' | 'basic' | 'api-key';
  token?: string;
  username?: string;
  password?: string;
  key?: string;
  value?: string;
}

export interface FolderVariable {
  key: string;
  value: string;
  description?: string;
  enabled: boolean;
}

export interface FolderSettings {
  inheritAuth?: boolean;
  inheritVariables?: boolean;
}

export interface FullFolderData extends FolderData {
  settings?: FolderSettings;
}
