/**
 * UI State Manager - Handles persistence and restoration of UI state
 */

export interface UIState {
  // Tab management
  openTabs: Array<{
    id: string; // Hierarchy item ID for tab identification
    entityId?: number; // Database entity ID for operations
    name: string;
    method: string;
    url: string;
    headers?: Record<string, string>;
    params?: Record<string, string>;
    body?: string;
    hasUnsavedChanges?: boolean;
  }>;
  activeTab: string;
  
  // Panel states
  leftPanelWidth: number;
  workspaceSection: 'collections' | 'environments' | 'history';
  
  // Collection tree state
  expandedCollections: string[];
  expandedFolders: string[];
  
  // Environment state
  activeEnvironment: string;
  
  // Chat state
  isChatOpen: boolean;
  
  // Response viewer state
  responseViewerState: {
    activeTab: 'body' | 'headers' | 'cookies' | 'test-results';
    bodyViewMode: 'pretty' | 'raw' | 'tree';
    wrapLines: boolean;
    showLineNumbers: boolean;
  };
  
  // Request editor state
  requestEditorState: {
    activeTab: 'params' | 'headers' | 'body' | 'auth' | 'pre-script' | 'tests';
    bodyType: 'none' | 'raw' | 'form-data' | 'x-www-form-urlencoded';
    bodyLanguage: string;
  };
  
  // Window state
  windowState: {
    isMaximized: boolean;
    bounds?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
  
  // Settings
  theme: 'light' | 'dark' | 'auto';
  
  // Last saved timestamp
  lastSaved: number;
}

export class UIStateManager {
  private static readonly STORAGE_KEY = 'apicool-ui-state';
  private static readonly AUTO_SAVE_INTERVAL = 5000; // 5 seconds
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private currentState: Partial<UIState> = {};
  private listeners: Array<(state: Partial<UIState>) => void> = [];

  constructor() {
    this.startAutoSave();
    this.setupBeforeUnloadHandler();
  }

  /**
   * Get the default UI state
   */
  private getDefaultState(): UIState {
    return {
      openTabs: [],
      activeTab: '',
      leftPanelWidth: 360,
      workspaceSection: 'collections',
      expandedCollections: [],
      expandedFolders: [],
      activeEnvironment: '',
      isChatOpen: false,
      responseViewerState: {
        activeTab: 'body',
        bodyViewMode: 'pretty',
        wrapLines: true,
        showLineNumbers: true,
      },
      requestEditorState: {
        activeTab: 'params',
        bodyType: 'none',
        bodyLanguage: 'json',
      },
      windowState: {
        isMaximized: false,
      },
      theme: 'auto',
      lastSaved: Date.now(),
    };
  }

  /**
   * Load UI state from localStorage
   */
  loadState(): UIState {
    try {
      const savedState = localStorage.getItem(UIStateManager.STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState) as Partial<UIState>;
        const defaultState = this.getDefaultState();

        // Merge with defaults to ensure all properties exist
        const mergedState = {
          ...defaultState,
          ...parsedState,
          responseViewerState: {
            ...defaultState.responseViewerState,
            ...parsedState.responseViewerState,
          },
          requestEditorState: {
            ...defaultState.requestEditorState,
            ...parsedState.requestEditorState,
          },
          windowState: {
            ...defaultState.windowState,
            ...parsedState.windowState,
          },
        };

        this.currentState = mergedState;
        return mergedState;
      }
    } catch (error) {
      console.warn('Failed to load UI state from localStorage:', error);
    }

    const defaultState = this.getDefaultState();
    this.currentState = defaultState;
    return defaultState;
  }

  /**
   * Save UI state to localStorage
   */
  saveState(state?: Partial<UIState>): void {
    try {
      if (state) {
        this.currentState = { ...this.currentState, ...state };
      }

      const stateToSave = {
        ...this.currentState,
        lastSaved: Date.now(),
      };

      localStorage.setItem(UIStateManager.STORAGE_KEY, JSON.stringify(stateToSave));

      // Notify listeners
      this.listeners.forEach(listener => listener(stateToSave));
    } catch (error) {
      console.error('Failed to save UI state to localStorage:', error);
    }
  }

  /**
   * Update specific part of the state
   */
  updateState(updates: Partial<UIState>): void {
    this.currentState = { ...this.currentState, ...updates };
  }

  /**
   * Get current state
   */
  getCurrentState(): Partial<UIState> {
    return { ...this.currentState };
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: Partial<UIState>) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Start auto-save timer
   */
  private startAutoSave(): void {
    this.autoSaveTimer = setInterval(() => {
      this.saveState();
    }, UIStateManager.AUTO_SAVE_INTERVAL);
  }

  /**
   * Stop auto-save timer
   */
  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  /**
   * Setup beforeunload handler to save state before closing
   */
  private setupBeforeUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      this.saveState();
      this.stopAutoSave();
    });
  }

  /**
   * Manually trigger save (useful for critical state changes)
   */
  forceSave(): void {
    this.saveState();
  }

  /**
   * Clear all saved state
   */
  clearState(): void {
    try {
      localStorage.removeItem(UIStateManager.STORAGE_KEY);
      this.currentState = this.getDefaultState();
    } catch (error) {
      console.error('Failed to clear UI state:', error);
    }
  }

  /**
   * Export state for backup
   */
  exportState(): string {
    return JSON.stringify(this.currentState, null, 2);
  }

  /**
   * Import state from backup
   */
  importState(stateJson: string): boolean {
    try {
      const importedState = JSON.parse(stateJson) as Partial<UIState>;
      this.currentState = importedState;
      this.saveState();
      return true;
    } catch (error) {
      console.error('Failed to import UI state:', error);
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopAutoSave();
    this.listeners = [];
  }
}

// Create singleton instance
export const uiStateManager = new UIStateManager();
