import React, { useState, useEffect } from 'react';
import CollectionsPanel from './CollectionsPanel';
import EnvironmentsList from './EnvironmentsList';
import HistoryList from './HistoryList';
import WorkspaceSwitcher from './workspace/WorkspaceSwitcher';
import { Workspace } from '../types/workspace-management';

interface WorkspacePanelProps {
  onOpenRequest: (request: any) => void;
  onOpenEditor?: (type: 'folder' | 'collection' | 'example' | 'environment' | 'history' | 'request', entityId: string, name: string, hierarchyId?: string, entityData?: any) => void;
  activeSection?: 'collections' | 'environments' | 'history';
  onImportClick?: () => void;
  onCreateWorkspace?: () => void;
  onManageWorkspaces?: () => void;
}

const WorkspacePanel: React.FC<WorkspacePanelProps> = ({
  onOpenRequest,
  onOpenEditor,
  activeSection = 'collections',
  onImportClick,
  onCreateWorkspace,
  onManageWorkspaces
}) => {
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<number | null>(null);

  // Handlers for opening specific environment/history tabs
  const handleOpenEnvironmentEditor = (environmentId: string, environmentName: string) => {
    onOpenEditor('environment', environmentId, `Environment: ${environmentName}`);
  };

  const handleOpenHistoryEntry = (historyId: string, historyName: string) => {
    onOpenEditor('history', historyId, `History: ${historyName}`);
  };

  const handleOpenHistoryRequest = (historyEntry: any) => {
    // Convert history entry to request format and open it
    const request = {
      id: `history-${historyEntry.id}`,
      name: historyEntry.name,
      method: historyEntry.method,
      url: historyEntry.url,
      headers: historyEntry.headers || {},
      params: {},
      body: typeof historyEntry.body === 'string' ? historyEntry.body : JSON.stringify(historyEntry.body, null, 2)
    };
    onOpenRequest(request);
  };

  useEffect(() => {
    // Get current workspace ID from localStorage or context
    const workspaceId = localStorage.getItem('apicool-active-workspace');
    if (workspaceId) {
      setCurrentWorkspaceId(parseInt(workspaceId, 10));
    } else {
      // Default to workspace ID 1 if none found
      setCurrentWorkspaceId(1);
    }

    // Listen for workspace changes
    const handleWorkspaceChange = (event: CustomEvent) => {
      const { workspaceId: newWorkspaceId } = event.detail;
      if (newWorkspaceId) {
        setCurrentWorkspaceId(parseInt(newWorkspaceId, 10));
      }
    };

    window.addEventListener('workspace-switched', handleWorkspaceChange as EventListener);

    return () => {
      window.removeEventListener('workspace-switched', handleWorkspaceChange as EventListener);
    };
  }, []);


  const handleNewCollection = () => {
    // TODO: Implement new collection creation
    console.log('New collection');
  };

  const handleImport = () => {
    if (onImportClick) {
      onImportClick();
    }
  };

  const handleWorkspaceChange = (workspace: Workspace) => {
    console.log('Workspace changed to:', workspace.name);
    // TODO: Handle workspace change (reload collections, etc.)
  };

  const renderActivePanel = () => {
    if (!currentWorkspaceId) {
      return <div className="workspace-loading">Loading workspace...</div>;
    }

    switch (activeSection) {
      case 'collections':
        return <CollectionsPanel onOpenRequest={onOpenRequest} onOpenEditor={onOpenEditor} />;
      case 'environments':
        return (
          <EnvironmentsList
            workspaceId={currentWorkspaceId}
            onOpenEnvironmentEditor={handleOpenEnvironmentEditor}
          />
        );
      case 'history':
        return (
          <HistoryList
            workspaceId={currentWorkspaceId}
            onOpenHistoryEntry={handleOpenHistoryEntry}
            onOpenRequest={handleOpenHistoryRequest}
          />
        );
      default:
        return <CollectionsPanel onOpenRequest={onOpenRequest} onOpenEditor={onOpenEditor} />;
    }
  };

  const getSectionTitle = () => {
    switch (activeSection) {
      case 'collections':
        return 'Collections';
      case 'environments':
        return 'Environments';
      case 'history':
        return 'History';
      default:
        return 'Collections';
    }
  };

  return (
    <div className="workspace-panel">
      {activeSection === 'collections' && (
        <div className="workspace-header">
          <div className="workspace-actions">
            <WorkspaceSwitcher
              onWorkspaceChange={handleWorkspaceChange}
              onCreateWorkspace={onCreateWorkspace}
              onManageWorkspaces={onManageWorkspaces}
            />
            <div className="workspace-action-buttons">
              <button
                className="workspace-action-btn"
                onClick={handleNewCollection}
                title="New Collection"
              >
                New
              </button>
              <button
                className="workspace-action-btn"
                onClick={handleImport}
                title="Import"
              >
                Import
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="workspace-panel-content">
        {renderActivePanel()}
      </div>
    </div>
  );
};

export default WorkspacePanel;
