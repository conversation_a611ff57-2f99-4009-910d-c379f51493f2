import { ServiceRegistry } from './BaseItemService';
import { RequestService } from './RequestService';
import { CollectionService } from './CollectionService';
import { FolderService } from './FolderService';
import { EnvironmentService } from './EnvironmentService';
import { ExampleService } from './ExampleService';
import { HistoryService } from './HistoryService';

/**
 * Initialize all item services and register them
 */
export function initializeServices() {
  // Register all services
  ServiceRegistry.register('request', new RequestService());
  ServiceRegistry.register('collection', new CollectionService());
  ServiceRegistry.register('folder', new FolderService());
  ServiceRegistry.register('environment', new EnvironmentService());
  ServiceRegistry.register('example', new ExampleService());
  ServiceRegistry.register('history', new HistoryService());
}

/**
 * Utility functions for working with tab items and services
 */
export class ItemServiceUtils {
  /**
   * Save a tab item using the appropriate service
   */
  static async saveTabItem(tabItem: any, workspaceId: number): Promise<any> {
    console.log('[ItemServiceUtils] saveTabItem called with tabItem:', tabItem);
    console.log('[ItemServiceUtils] TabItem type:', tabItem.type);
    console.log('[ItemServiceUtils] TabItem data:', tabItem.data);
    console.log('[ItemServiceUtils] TabItem headers:', tabItem.data?.headers);
    console.log('[ItemServiceUtils] TabItem params:', tabItem.data?.params);

    const service = ServiceRegistry.getService(tabItem.type);

    if (service.isNew(tabItem.data)) {
      // Create new item
      console.log('[ItemServiceUtils] Creating new item');
      return await service.create(tabItem.data, workspaceId);
    } else {
      // Update existing item
      const entityId = tabItem.data.entityId || tabItem.data.id;
      console.log('[ItemServiceUtils] Updating existing item with entityId:', entityId);
      return await service.update(entityId, tabItem.data);
    }
  }

  /**
   * Delete a tab item using the appropriate service
   */
  static async deleteTabItem(tabItem: any): Promise<boolean> {
    const service = ServiceRegistry.getService(tabItem.type);
    const entityId = tabItem.data.entityId || tabItem.data.id;
    return await service.delete(entityId);
  }

  /**
   * Load a tab item using the appropriate service
   */
  static async loadTabItem(type: string, id: string | number): Promise<any> {
    const service = ServiceRegistry.getService(type);
    return await service.get(id);
  }

  /**
   * Check if a tab item is new (not saved to database)
   */
  static isNewTabItem(tabItem: any): boolean {
    const service = ServiceRegistry.getService(tabItem.type);
    return service.isNew(tabItem.data);
  }

  /**
   * Get display name for a tab item
   */
  static getTabDisplayName(tabItem: any): string {
    const service = ServiceRegistry.getService(tabItem.type);
    return service.getDisplayName(tabItem.data);
  }

  /**
   * Get default data for a new tab item of the specified type
   */
  static getDefaultTabData(type: string): any {
    const service = ServiceRegistry.getService(type);
    return service.getDefaultData();
  }

  /**
   * Check if a tab item needs a save dialog
   */
  static needsSaveDialog(tabItem: any): boolean {
    const service = ServiceRegistry.getService(tabItem.type);
    
    // Check if the service has a needsSaveDialog method
    if ('needsSaveDialog' in service && typeof service.needsSaveDialog === 'function') {
      return service.needsSaveDialog(tabItem.data);
    }
    
    // Default: only requests need save dialog for new items
    return tabItem.type === 'request' && service.isNew(tabItem.data);
  }

  /**
   * Create a new tab item with default data
   */
  static createNewTabItem(type: string, overrides: any = {}): any {
    const service = ServiceRegistry.getService(type);
    const defaultData = service.getDefaultData();
    
    return {
      id: `${type}-${Date.now()}`, // Temporary ID
      type: type,
      data: {
        ...defaultData,
        ...overrides
      },
      hasUnsavedChanges: true
    };
  }

  /**
   * Get the icon for a tab item type
   */
  static getTabIcon(tabItem: any): string {
    switch (tabItem.type) {
      case 'request':
        return '🌐';
      case 'collection':
        return '📚';
      case 'folder':
        return '📁';
      case 'environment':
        return '🌍';
      case 'example':
        return '📄';
      case 'history':
        return '🕒';
      default:
        return '📄';
    }
  }

  /**
   * Get the method badge for request-type items
   */
  static getMethodBadge(tabItem: any): string | null {
    if (tabItem.type === 'request' || tabItem.type === 'example' || tabItem.type === 'history') {
      return tabItem.data.method || null;
    }
    return null;
  }

  /**
   * Get method color for HTTP methods
   */
  static getMethodColor(method: string): string {
    const colors: Record<string, string> = {
      'GET': '#10b981',
      'POST': '#3b82f6', 
      'PUT': '#f59e0b',
      'PATCH': '#8b5cf6',
      'DELETE': '#ef4444',
      'HEAD': '#6b7280',
      'OPTIONS': '#6b7280'
    };
    return colors[method?.toUpperCase()] || '#6b7280';
  }
}

// Export all services for direct access if needed
export {
  RequestService,
  CollectionService,
  FolderService,
  EnvironmentService,
  ExampleService,
  HistoryService,
  ServiceRegistry
};
