import { PrismaClient } from '@prisma/client';

export interface Workspace {
  id: number;
  name: string;
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWorkspaceData {
  name: string;
  description?: string;
}

export interface UpdateWorkspaceData {
  name?: string;
  description?: string;
}

export class WorkspaceService {
  public prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async getAllWorkspaces(): Promise<Workspace[]> {
    try {
      return await this.prisma.workspace.findMany({
        orderBy: {
          updatedAt: 'desc'
        }
      });
    } catch (error) {
      console.error('Error fetching workspaces:', error);
      throw new Error('Failed to fetch workspaces');
    }
  }

  async getWorkspaceById(id: number): Promise<Workspace | null> {
    try {
      return await this.prisma.workspace.findUnique({
        where: { id }
      });
    } catch (error) {
      console.error('Error fetching workspace:', error);
      throw new Error('Failed to fetch workspace');
    }
  }

  async createWorkspace(data: CreateWorkspaceData): Promise<Workspace> {
    try {
      return await this.prisma.workspace.create({
        data: {
          name: data.name,
          description: data.description
        }
      });
    } catch (error) {
      console.error('Error creating workspace:', error);
      throw new Error('Failed to create workspace');
    }
  }

  async updateWorkspace(id: number, data: UpdateWorkspaceData): Promise<Workspace> {
    try {
      return await this.prisma.workspace.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error updating workspace:', error);
      throw new Error('Failed to update workspace');
    }
  }

  async deleteWorkspace(id: number): Promise<void> {
    try {
      // Delete all related items first
      await this.prisma.item.deleteMany({
        where: { workspaceId: id }
      });

      // Delete all related entities
      await this.prisma.collection.deleteMany({
        where: { workspaceId: id }
      });

      await this.prisma.folder.deleteMany({
        where: { workspaceId: id }
      });

      await this.prisma.request.deleteMany({
        where: { workspaceId: id }
      });

      await this.prisma.example.deleteMany({
        where: { workspaceId: id }
      });

      // Finally delete the workspace
      await this.prisma.workspace.delete({
        where: { id }
      });
    } catch (error) {
      console.error('Error deleting workspace:', error);
      throw new Error('Failed to delete workspace');
    }
  }

  async getWorkspaceStats(id: number): Promise<{
    collectionsCount: number;
    foldersCount: number;
    requestsCount: number;
    examplesCount: number;
  }> {
    try {
      const [collectionsCount, foldersCount, requestsCount, examplesCount] = await Promise.all([
        this.prisma.collection.count({ where: { workspaceId: id } }),
        this.prisma.folder.count({ where: { workspaceId: id } }),
        this.prisma.request.count({ where: { workspaceId: id } }),
        this.prisma.example.count({ where: { workspaceId: id } })
      ]);

      return {
        collectionsCount,
        foldersCount,
        requestsCount,
        examplesCount
      };
    } catch (error) {
      console.error('Error fetching workspace stats:', error);
      throw new Error('Failed to fetch workspace statistics');
    }
  }

  /**
   * Get workspace items with minimal data for left panel performance
   * Only includes basic info needed for tree display
   */
  async getWorkspaceItemsLight(workspaceId: number): Promise<any[]> {
    try {
      if (!workspaceId || typeof workspaceId !== 'number') {
        throw new Error(`Invalid workspaceId: ${workspaceId}. Expected a number.`);
      }

      const items = await this.prisma.item.findMany({
        where: { workspaceId },
        select: {
          id: true,
          type: true,
          entityId: true,
          parentId: true,
          order: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: [
          { parentId: 'asc' },
          { order: 'asc' }
        ]
      });

      // Fetch minimal entity data for each item
      const itemsWithBasicData = await Promise.all(
        items.map(async (item) => {
          let entityData = null;

          switch (item.type) {
            case 'COLLECTION':
              entityData = await this.prisma.collection.findUnique({
                where: { id: item.entityId },
                select: { id: true, name: true, description: true }
              });
              break;
            case 'FOLDER':
              entityData = await this.prisma.folder.findUnique({
                where: { id: item.entityId },
                select: { id: true, name: true, description: true }
              });
              break;
            case 'REQUEST':
              entityData = await this.prisma.request.findUnique({
                where: { id: item.entityId },
                select: { id: true, name: true, method: true, url: true }
              });
              break;
            case 'EXAMPLE':
              entityData = await this.prisma.example.findUnique({
                where: { id: item.entityId },
                select: { id: true, name: true }
              });
              break;
          }

          return {
            ...item,
            entity: entityData
          };
        })
      );

      return this.buildHierarchy(itemsWithBasicData);
    } catch (error) {
      console.error('Error fetching workspace items (light):', error);
      throw new Error('Failed to fetch workspace items');
    }
  }

  /**
   * Get full details for a specific item when opening in tab
   */
  async getItemDetails(itemId: number): Promise<any> {
    try {
      const item = await this.prisma.item.findUnique({
        where: { id: itemId }
      });

      if (!item) {
        throw new Error('Item not found');
      }

      let entityData = null;

      switch (item.type) {
        case 'COLLECTION':
          entityData = await this.prisma.collection.findUnique({
            where: { id: item.entityId },
            include: {
              runs: {
                orderBy: { startedAt: 'desc' },
                take: 10 // Last 10 runs
              }
            }
          });
          break;
        case 'FOLDER':
          entityData = await this.prisma.folder.findUnique({
            where: { id: item.entityId }
          });
          break;
        case 'REQUEST':
          entityData = await this.prisma.request.findUnique({
            where: { id: item.entityId },
            include: {
              examples: {
                orderBy: { createdAt: 'desc' }
              }
            }
          });
          break;
        case 'EXAMPLE':
          entityData = await this.prisma.example.findUnique({
            where: { id: item.entityId },
            include: {
              request: {
                select: { id: true, name: true, method: true, url: true }
              }
            }
          });
          break;
      }

      return {
        ...item,
        entity: entityData
      };
    } catch (error) {
      console.error('Error fetching item details:', error);
      throw new Error('Failed to fetch item details');
    }
  }

  /**
   * Build hierarchy from flat items array
   */
  private buildHierarchy(items: any[]): any[] {
    const itemMap = new Map();
    const rootItems: any[] = [];

    // Create a map of all items
    items.forEach(item => {
      itemMap.set(item.id, { ...item, children: [] });
    });

    // Build the hierarchy
    items.forEach(item => {
      const itemWithChildren = itemMap.get(item.id);

      if (item.parentId === null) {
        rootItems.push(itemWithChildren);
      } else {
        const parent = itemMap.get(item.parentId);
        if (parent) {
          parent.children.push(itemWithChildren);
        }
      }
    });

    return rootItems;
  }

  /**
   * Get full collection details by entity ID
   */
  async getCollectionDetails(collectionId: number): Promise<any> {
    try {
      return await this.prisma.collection.findUnique({
        where: { id: collectionId },
        include: {
          runs: {
            orderBy: { startedAt: 'desc' },
            take: 10
          }
        }
      });
    } catch (error) {
      console.error('Error fetching collection details:', error);
      throw new Error('Failed to fetch collection details');
    }
  }

  /**
   * Get full folder details by entity ID
   */
  async getFolderDetails(folderId: number): Promise<any> {
    try {
      return await this.prisma.folder.findUnique({
        where: { id: folderId }
      });
    } catch (error) {
      console.error('Error fetching folder details:', error);
      throw new Error('Failed to fetch folder details');
    }
  }

  /**
   * Get full request details by entity ID
   */
  async getRequestDetails(requestId: number): Promise<any> {
    try {
      return await this.prisma.request.findUnique({
        where: { id: requestId },
        include: {
          examples: {
            orderBy: { createdAt: 'desc' }
          }
        }
      });
    } catch (error) {
      console.error('Error fetching request details:', error);
      throw new Error('Failed to fetch request details');
    }
  }

  /**
   * Get full example details by entity ID
   */
  async getExampleDetails(exampleId: number): Promise<any> {
    try {
      return await this.prisma.example.findUnique({
        where: { id: exampleId },
        include: {
          request: {
            select: { id: true, name: true, method: true, url: true }
          }
        }
      });
    } catch (error) {
      console.error('Error fetching example details:', error);
      throw new Error('Failed to fetch example details');
    }
  }

  /**
   * Create a new example from a request response
   */
  async createExample(data: {
    name: string;
    requestId: number;
    response: string;
    workspaceId: number;
  }): Promise<any> {
    try {
      // Create the example entity
      const example = await this.prisma.example.create({
        data: {
          name: data.name,
          response: data.response,
          requestId: data.requestId,
          workspaceId: data.workspaceId
        }
      });

      // Find the request item to make the example a child of it
      const requestItem = await this.prisma.item.findFirst({
        where: {
          type: 'REQUEST',
          entityId: data.requestId,
          workspaceId: data.workspaceId
        }
      });

      if (requestItem) {
        // Create the example item as a child of the request
        await this.prisma.item.create({
          data: {
            type: 'EXAMPLE',
            entityId: example.id,
            workspaceId: data.workspaceId,
            parentId: requestItem.id,
            order: 1 // TODO: Calculate proper order
          }
        });
      }

      return example;
    } catch (error) {
      console.error('Error creating example:', error);
      throw new Error('Failed to create example');
    }
  }

  /**
   * Create a new collection
   */
  async createCollection(data: {
    name: string;
    description?: string;
    workspaceId: number;
  }): Promise<any> {
    try {
      // Create the collection entity
      const collection = await this.prisma.collection.create({
        data: {
          name: data.name,
          description: data.description || '',
          variables: {},
          workspaceId: data.workspaceId
        }
      });

      // Calculate the order for the new item
      const maxOrder = await this.prisma.item.findFirst({
        where: {
          parentId: null,
          workspaceId: data.workspaceId
        },
        orderBy: { order: 'desc' },
        select: { order: true }
      });

      const nextOrder = (maxOrder?.order || 0) + 1;

      // Create the hierarchy item
      await this.prisma.item.create({
        data: {
          type: 'COLLECTION',
          entityId: collection.id,
          workspaceId: data.workspaceId,
          parentId: null,
          order: nextOrder
        }
      });

      return collection;
    } catch (error) {
      console.error('Error creating collection:', error);
      throw new Error('Failed to create collection');
    }
  }

  /**
   * Update an existing collection
   */
  async updateCollection(collectionId: number, data: {
    name?: string;
    description?: string;
    baseUrl?: string;
    variables?: Record<string, string>;
    auth?: any;
    preRequestScript?: string;
    testScript?: string;
    documentation?: string;
    tags?: string[];
    version?: string;
  }): Promise<any> {
    try {
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.baseUrl !== undefined) updateData.baseUrl = data.baseUrl;
      if (data.variables !== undefined) updateData.variables = data.variables;
      if (data.auth !== undefined) updateData.auth = data.auth;
      if (data.preRequestScript !== undefined) updateData.preRequestScript = data.preRequestScript;
      if (data.testScript !== undefined) updateData.testScript = data.testScript;
      if (data.documentation !== undefined) updateData.documentation = data.documentation;
      if (data.tags !== undefined) updateData.tags = data.tags;
      if (data.version !== undefined) updateData.version = data.version;

      console.log('[WorkspaceService] Updating collection with data:', updateData);

      const collection = await this.prisma.collection.update({
        where: { id: collectionId },
        data: updateData
      });

      return collection;
    } catch (error) {
      console.error('Error updating collection:', error);
      throw new Error('Failed to update collection');
    }
  }

  /**
   * Create a new folder
   */
  async createFolder(data: {
    name: string;
    description?: string;
    workspaceId: number;
  }): Promise<any> {
    try {
      // Create the folder entity
      const folder = await this.prisma.folder.create({
        data: {
          name: data.name,
          description: data.description || '',
          workspaceId: data.workspaceId
        }
      });

      // Calculate the order for the new item
      const maxOrder = await this.prisma.item.findFirst({
        where: {
          parentId: null,
          workspaceId: data.workspaceId
        },
        orderBy: { order: 'desc' },
        select: { order: true }
      });

      const nextOrder = (maxOrder?.order || 0) + 1;

      // Create the hierarchy item
      await this.prisma.item.create({
        data: {
          type: 'FOLDER',
          entityId: folder.id,
          workspaceId: data.workspaceId,
          parentId: null,
          order: nextOrder
        }
      });

      return folder;
    } catch (error) {
      console.error('Error creating folder:', error);
      throw new Error('Failed to create folder');
    }
  }

  /**
   * Update an existing folder
   */
  async updateFolder(folderId: number, data: {
    name?: string;
    description?: string;
    variables?: Record<string, string>;
    auth?: any;
    preRequestScript?: string;
    testScript?: string;
  }): Promise<any> {
    try {
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.variables !== undefined) updateData.variables = data.variables;
      if (data.auth !== undefined) updateData.auth = data.auth;
      if (data.preRequestScript !== undefined) updateData.preRequestScript = data.preRequestScript;
      if (data.testScript !== undefined) updateData.testScript = data.testScript;

      console.log('[WorkspaceService] Updating folder with data:', updateData);

      const folder = await this.prisma.folder.update({
        where: { id: folderId },
        data: updateData
      });

      return folder;
    } catch (error) {
      console.error('Error updating folder:', error);
      throw new Error('Failed to update folder');
    }
  }

  /**
   * Create a new request and save it to the database
   */
  async createRequest(data: {
    name: string;
    method: string;
    url: string;
    headers: Record<string, string>;
    params?: Record<string, string>;
    body?: string;
    description?: string;
    auth?: any;
    preScript?: string;
    testScript?: string;
    parentId: number;
    workspaceId: number;
  }): Promise<any> {
    try {
      // Create the request entity
      const request = await this.prisma.request.create({
        data: {
          name: data.name,
          method: data.method,
          url: data.url,
          headers: data.headers,
          params: data.params,
          body: data.body || undefined,
          description: data.description,
          auth: data.auth,
          preScript: data.preScript,
          testScript: data.testScript,
          workspaceId: data.workspaceId
        }
      });

      // Calculate the order for the new item
      const maxOrder = await this.prisma.item.findFirst({
        where: {
          parentId: data.parentId,
          workspaceId: data.workspaceId
        },
        orderBy: { order: 'desc' },
        select: { order: true }
      });

      const newOrder = (maxOrder?.order || 0) + 1;

      // Create the item in the hierarchy
      const item = await this.prisma.item.create({
        data: {
          type: 'REQUEST',
          entityId: request.id,
          workspaceId: data.workspaceId,
          parentId: data.parentId,
          order: newOrder
        }
      });

      return {
        request,
        item
      };
    } catch (error) {
      console.error('Error creating request:', error);
      throw new Error('Failed to create request');
    }
  }

  /**
   * Update an existing request
   */
  async updateRequest(requestId: number, data: {
    name?: string;
    method?: string;
    url?: string;
    headers?: Record<string, string>;
    params?: Record<string, string>;
    body?: string;
    description?: string;
    auth?: any;
    preScript?: string;
    testScript?: string;
  }): Promise<any> {
    try {
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.method !== undefined) updateData.method = data.method;
      if (data.url !== undefined) updateData.url = data.url;
      if (data.headers !== undefined) updateData.headers = data.headers;
      if (data.params !== undefined) updateData.params = data.params;
      if (data.body !== undefined) updateData.body = data.body;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.auth !== undefined) updateData.auth = data.auth;
      if (data.preScript !== undefined) updateData.preScript = data.preScript;
      if (data.testScript !== undefined) updateData.testScript = data.testScript;

      console.log('[WorkspaceService] Updating request with data:', updateData);

      const request = await this.prisma.request.update({
        where: { id: requestId },
        data: updateData
      });

      return request;
    } catch (error) {
      console.error('Error updating request:', error);
      throw new Error('Failed to update request');
    }
  }

  /**
   * Add an item to the hierarchy
   */
  async addItemToHierarchy(data: {
    type: 'COLLECTION' | 'FOLDER' | 'REQUEST' | 'EXAMPLE';
    entityId: number;
    workspaceId: number;
    parentId?: number;
    order?: number;
  }): Promise<any> {
    try {
      // Calculate the order if not provided
      let itemOrder = data.order;
      if (itemOrder === undefined) {
        const maxOrder = await this.prisma.item.findFirst({
          where: {
            parentId: data.parentId || null,
            workspaceId: data.workspaceId
          },
          orderBy: { order: 'desc' },
          select: { order: true }
        });
        itemOrder = (maxOrder?.order || 0) + 1;
      }

      // Create the hierarchy item
      const item = await this.prisma.item.create({
        data: {
          type: data.type,
          entityId: data.entityId,
          workspaceId: data.workspaceId,
          parentId: data.parentId || null,
          order: itemOrder
        }
      });

      return item;
    } catch (error) {
      console.error('Error adding item to hierarchy:', error);
      throw new Error('Failed to add item to hierarchy');
    }
  }

  /**
   * Delete an item and its entity from the database
   */
  async deleteItem(itemId: number): Promise<void> {
    try {
      await this.prisma.$transaction(async (tx: any) => {
        // Get the item to determine its type and entity ID
        const item = await tx.item.findUnique({
          where: { id: itemId }
        });

        if (!item) {
          throw new Error('Item not found');
        }

        // Get all descendants recursively
        const getAllDescendants = async (parentId: number): Promise<any[]> => {
          const children = await tx.item.findMany({
            where: { parentId: parentId }
          });

          let descendants = [...children];
          for (const child of children) {
            const childDescendants = await getAllDescendants(child.id);
            descendants = descendants.concat(childDescendants);
          }

          return descendants;
        };

        const descendants = await getAllDescendants(itemId);
        const allItemsToDelete = [item, ...descendants];

        // Delete entities first (in reverse order to handle dependencies)
        for (const itemToDelete of allItemsToDelete.reverse()) {
          switch (itemToDelete.type) {
            case 'EXAMPLE':
              await tx.example.delete({
                where: { id: itemToDelete.entityId }
              });
              break;
            case 'REQUEST':
              // Delete examples first
              await tx.example.deleteMany({
                where: { requestId: itemToDelete.entityId }
              });
              // Delete request
              await tx.request.delete({
                where: { id: itemToDelete.entityId }
              });
              break;
            case 'FOLDER':
              await tx.folder.delete({
                where: { id: itemToDelete.entityId }
              });
              break;
            case 'COLLECTION':
              await tx.collection.delete({
                where: { id: itemToDelete.entityId }
              });
              break;
          }
        }

        // Delete all items (hierarchy entries)
        for (const itemToDelete of allItemsToDelete) {
          await tx.item.delete({
            where: { id: itemToDelete.id }
          });
        }
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      throw new Error('Failed to delete item');
    }
  }

  /**
   * Update item hierarchy (for drag & drop operations)
   */
  async updateItemHierarchy(itemId: number, newParentId: number | null, newOrder: number): Promise<void> {
    try {
      await this.prisma.item.update({
        where: { id: itemId },
        data: {
          parentId: newParentId,
          order: newOrder
        }
      });
    } catch (error) {
      console.error('Error updating item hierarchy:', error);
      throw new Error('Failed to update item hierarchy');
    }
  }

  /**
   * Reorder items within the same parent
   */
  async reorderItems(items: { id: number; order: number }[]): Promise<void> {
    try {
      await this.prisma.$transaction(async (tx: any) => {
        for (const item of items) {
          await tx.item.update({
            where: { id: item.id },
            data: { order: item.order }
          });
        }
      });
    } catch (error) {
      console.error('Error reordering items:', error);
      throw new Error('Failed to reorder items');
    }
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
