/* Main application styles */

.app-header {
  height: 60px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-shrink: 0;
}

.app-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.request-panel {
  height: 50%;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
}

.response-panel {
  height: 50%;
  display: flex;
  flex-direction: column;
}

.ai-panel {
  width: 350px;
  background: var(--color-surface);
  border-left: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* Loading states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--color-text-secondary);
}

/* Error states */
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  text-align: center;
}

.error h2 {
  color: var(--color-error);
  margin-bottom: 8px;
}

.error p {
  color: var(--color-text-secondary);
  margin-bottom: 16px;
}

.error button {
  background: var(--color-primary);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error button:hover {
  background: var(--color-primary);
  opacity: 0.9;
}

/* Common button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  text-decoration: none;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  opacity: 0.9;
}

.btn-secondary {
  background: var(--color-secondary);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  opacity: 0.9;
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: white;
}

.btn-ghost {
  background: transparent;
  color: var(--color-text);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-surface);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* Form elements */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  cursor: pointer;
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-sm {
  font-size: 12px;
}

.text-base {
  font-size: 14px;
}

.text-lg {
  font-size: 16px;
}

.text-xl {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-error {
  color: var(--color-error);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.bg-surface {
  background-color: var(--color-surface);
}

.border {
  border: 1px solid var(--color-border);
}

.border-b {
  border-bottom: 1px solid var(--color-border);
}

.border-r {
  border-right: 1px solid var(--color-border);
}

.rounded {
  border-radius: 4px;
}

.shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* Workspace Switcher Styles */
.workspace-switcher {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.workspace-info {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0;
}

.active-workspace {
  display: flex;
  align-items: center;
  gap: 8px;
}

.workspace-label {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.workspace-select {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  min-width: 150px;
}

.workspace-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* TreeView Styles */
.tree-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
}

.tree-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.tree-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  margin: 0 8px;
}

.tree-item:hover {
  background: var(--color-surface);
}

.tree-item.selected {
  background: var(--color-primary);
  color: white;
}

.item-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.request-method {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.tree-item.selected .request-method {
  background: rgba(255, 255, 255, 0.3);
}

/* Variables Table Styles */
.variables-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.variables-title h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.variables-description {
  margin: 0;
  font-size: 13px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.variables-description code {
  background: var(--color-surface);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.variables-table-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.variables-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-background);
}

.variables-table thead {
  background: var(--color-surface);
}

.variables-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  color: var(--color-text);
  border-bottom: 2px solid var(--color-border);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.variables-table td {
  padding: 4px 8px;
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.variables-table tbody tr:last-child td {
  border-bottom: none;
}

.variables-table tbody tr:hover {
  background: rgba(0, 122, 204, 0.05);
}

.variables-table .form-input {
  border: 1px solid transparent;
  background: transparent;
  padding: 8px 12px;
  margin: 0;
  width: 100%;
  border-radius: 4px;
  transition: all 0.2s;
}

.variables-table .form-input:hover {
  background: var(--color-surface);
}

.variables-table .form-input:focus {
  background: var(--color-background);
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.variables-table .variable-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: var(--color-primary);
}

.variables-table .variable-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: var(--color-text);
}

.variables-table .btn {
  padding: 6px 10px;
  font-size: 12px;
  min-width: auto;
  border-radius: 4px;
}

.variables-table .no-variables {
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
  padding: 32px 16px;
  background: var(--color-surface);
}

.btn-danger {
  background: var(--color-error);
  color: white;
  border: 1px solid var(--color-error);
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  border-color: #dc2626;
}

/* Script Editor Styles */
.script-editor {
  width: 100%;
  min-height: 200px;
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  outline: none;
}

.script-editor:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.script-editor:disabled,
.script-editor[readonly] {
  background: var(--color-surface);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

/* Auth Editor Styles */
.auth-editor {
  padding: 16px;
}

.auth-type-selector {
  margin-bottom: 16px;
}

.auth-type-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text);
}

.auth-type-selector select {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.auth-fields {
  margin-top: 16px;
}

.auth-fields .form-group {
  margin-bottom: 16px;
}

.auth-fields label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--color-text);
}

.auth-fields input {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.auth-fields input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.auth-fields input:disabled {
  background: var(--color-surface);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

/* Form Data Table Styles */
.form-data-table {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.form-data-table .table-header {
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 8px;
  padding: 8px 12px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-weight: 500;
  color: var(--color-text);
  font-size: 14px;
}

.form-data-table .table-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid var(--color-border);
  align-items: center;
}

.form-data-table .table-row:last-child {
  border-bottom: none;
}

.form-data-table input,
.form-data-table select {
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 13px;
}

.form-data-table input:focus,
.form-data-table select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px rgba(0, 122, 204, 0.2);
}

.form-data-table .delete-btn {
  background: var(--color-error);
  color: white;
  border: none;
  border-radius: 3px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

.form-data-table .delete-btn:hover:not(:disabled) {
  background: #dc2626;
}

.form-data-table .delete-btn:disabled {
  background: var(--color-border);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

/* Body Type Selector */
.body-type-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--color-surface);
  border-radius: 4px;
}

.body-type-selector label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text);
}

.body-type-selector input[type="radio"] {
  margin: 0;
}

.body-form {
  margin-top: 16px;
}
