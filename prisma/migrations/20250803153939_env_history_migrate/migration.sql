/*
  Warnings:

  - Added the required column `name` to the `RequestHistory` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `RequestHistory` table without a default value. This is not possible if the table is not empty.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Environment" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "variables" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "workspaceId" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Environment_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_Environment" ("createdAt", "id", "isActive", "name", "updatedAt", "variables", "workspaceId") SELECT "createdAt", "id", "isActive", "name", "updatedAt", "variables", "workspaceId" FROM "Environment";
DROP TABLE "Environment";
ALTER TABLE "new_Environment" RENAME TO "Environment";
CREATE UNIQUE INDEX "Environment_workspaceId_name_key" ON "Environment"("workspaceId", "name");
CREATE TABLE "new_RequestHistory" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "requestId" INTEGER,
    "name" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "headers" JSONB NOT NULL,
    "body" JSONB,
    "response" JSONB,
    "statusCode" INTEGER,
    "responseTime" INTEGER,
    "error" TEXT,
    "environment" TEXT,
    "workspaceId" INTEGER NOT NULL,
    "executedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "RequestHistory_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_RequestHistory" ("body", "error", "executedAt", "headers", "id", "method", "requestId", "response", "responseTime", "statusCode", "url", "workspaceId") SELECT "body", "error", "executedAt", "headers", "id", "method", "requestId", "response", "responseTime", "statusCode", "url", "workspaceId" FROM "RequestHistory";
DROP TABLE "RequestHistory";
ALTER TABLE "new_RequestHistory" RENAME TO "RequestHistory";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
