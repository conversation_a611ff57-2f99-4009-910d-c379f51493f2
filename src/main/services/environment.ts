import { PrismaClient } from '@prisma/client';

export interface EnvironmentVariable {
  key: string;
  value: string;
  enabled: boolean;
  description?: string;
}

export interface Environment {
  id: number;
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  isActive: boolean;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateEnvironmentData {
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  workspaceId: number;
}

export interface UpdateEnvironmentData {
  name?: string;
  description?: string;
  variables?: EnvironmentVariable[];
  isActive?: boolean;
}

export class EnvironmentService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get all environments for a workspace
   */
  async getEnvironments(workspaceId: number): Promise<Environment[]> {
    try {
      const environments = await this.prisma.environment.findMany({
        where: { workspaceId },
        orderBy: [
          { isActive: 'desc' }, // Active environment first
          { name: 'asc' }
        ]
      });

      return environments.map(env => ({
        ...env,
        variables: (env.variables as unknown) as EnvironmentVariable[]
      }));
    } catch (error) {
      console.error('Error fetching environments:', error);
      throw new Error('Failed to fetch environments');
    }
  }

  /**
   * Get active environment for a workspace
   */
  async getActiveEnvironment(workspaceId: number): Promise<Environment | null> {
    try {
      const environment = await this.prisma.environment.findFirst({
        where: { 
          workspaceId,
          isActive: true 
        }
      });

      if (!environment) return null;

      return {
        ...environment,
        variables: (environment.variables as unknown) as EnvironmentVariable[]
      };
    } catch (error) {
      console.error('Error fetching active environment:', error);
      throw new Error('Failed to fetch active environment');
    }
  }

  /**
   * Create a new environment
   */
  async createEnvironment(data: CreateEnvironmentData): Promise<Environment> {
    try {
      const environment = await this.prisma.environment.create({
        data: {
          name: data.name,
          description: data.description,
          variables: data.variables as any,
          workspaceId: data.workspaceId,
          isActive: false
        }
      });

      return {
        ...environment,
        variables: (environment.variables as unknown) as EnvironmentVariable[]
      };
    } catch (error) {
      console.error('Error creating environment:', error);
      if (error.code === 'P2002') {
        throw new Error('Environment name already exists in this workspace');
      }
      throw new Error('Failed to create environment');
    }
  }

  /**
   * Update an environment
   */
  async updateEnvironment(id: number, data: UpdateEnvironmentData): Promise<Environment> {
    try {
      const environment = await this.prisma.environment.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.variables && { variables: data.variables as any }),
          ...(data.isActive !== undefined && { isActive: data.isActive })
        }
      });

      return {
        ...environment,
        variables: (environment.variables as unknown) as EnvironmentVariable[]
      };
    } catch (error) {
      console.error('Error updating environment:', error);
      if (error.code === 'P2002') {
        throw new Error('Environment name already exists in this workspace');
      }
      throw new Error('Failed to update environment');
    }
  }

  /**
   * Set active environment (deactivates others)
   */
  async setActiveEnvironment(id: number, workspaceId: number): Promise<Environment> {
    try {
      // Use transaction to ensure only one active environment
      const result = await this.prisma.$transaction(async (tx) => {
        // Deactivate all environments in workspace
        await tx.environment.updateMany({
          where: { workspaceId },
          data: { isActive: false }
        });

        // Activate the selected environment
        const environment = await tx.environment.update({
          where: { id },
          data: { isActive: true }
        });

        return environment;
      });

      return {
        ...result,
        variables: (result.variables as unknown) as EnvironmentVariable[]
      };
    } catch (error) {
      console.error('Error setting active environment:', error);
      throw new Error('Failed to set active environment');
    }
  }

  /**
   * Delete an environment
   */
  async deleteEnvironment(id: number): Promise<void> {
    try {
      await this.prisma.environment.delete({
        where: { id }
      });
    } catch (error) {
      console.error('Error deleting environment:', error);
      throw new Error('Failed to delete environment');
    }
  }

  /**
   * Duplicate an environment
   */
  async duplicateEnvironment(id: number, newName: string): Promise<Environment> {
    try {
      const original = await this.prisma.environment.findUnique({
        where: { id }
      });

      if (!original) {
        throw new Error('Environment not found');
      }

      const duplicate = await this.prisma.environment.create({
        data: {
          name: newName,
          description: original.description,
          variables: original.variables,
          workspaceId: original.workspaceId,
          isActive: false
        }
      });

      return {
        ...duplicate,
        variables: (duplicate.variables as unknown) as EnvironmentVariable[]
      };
    } catch (error) {
      console.error('Error duplicating environment:', error);
      if (error.code === 'P2002') {
        throw new Error('Environment name already exists in this workspace');
      }
      throw new Error('Failed to duplicate environment');
    }
  }

  /**
   * Get environment variables as key-value map
   */
  getVariablesMap(environment: Environment): Record<string, string> {
    const map: Record<string, string> = {};
    environment.variables
      .filter(variable => variable.enabled)
      .forEach(variable => {
        map[variable.key] = variable.value;
      });
    return map;
  }

  /**
   * Resolve variables in a string (replace {{variableName}} with values)
   */
  resolveVariables(text: string, environment: Environment | null): string {
    if (!environment || !text) return text;

    const variables = this.getVariablesMap(environment);
    
    return text.replace(/\{\{([^}]+)\}\}/g, (match, variableName) => {
      const trimmedName = variableName.trim();
      return variables[trimmedName] || match; // Keep original if variable not found
    });
  }
}
