export interface ParsedCurlRequest {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: string;
  name: string;
}

/**
 * Parse a curl command and extract request information
 */
export function parseCurlCommand(curlCommand: string): ParsedCurlRequest | null {
  try {
    // Clean up the curl command
    const cleanCommand = curlCommand.trim();

    // Check if it starts with curl
    if (!cleanCommand.toLowerCase().startsWith('curl')) {
      return null;
    }

    // Initialize result
    const result: ParsedCurlRequest = {
      method: 'GET',
      url: '',
      headers: {},
      name: 'Imported Request'
    };

    // Split command into tokens, handling quoted strings
    const tokens = tokenizeCurlCommand(cleanCommand);

    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];

      switch (token) {
        case 'curl':
          // Skip the curl command itself
          break;

        case '-X':
        case '--request':
          // HTTP method
          if (i + 1 < tokens.length) {
            result.method = tokens[++i].toUpperCase();
          }
          break;

        case '-H':
        case '--header':
          // Headers
          if (i + 1 < tokens.length) {
            const headerString = tokens[++i];
            const colonIndex = headerString.indexOf(':');
            if (colonIndex > 0) {
              const key = headerString.substring(0, colonIndex).trim();
              const value = headerString.substring(colonIndex + 1).trim();
              result.headers[key] = value;
            }
          }
          break;

        case '-d':
        case '--data':
        case '--data-raw':
          // Request body
          if (i + 1 < tokens.length) {
            result.body = tokens[++i];
            // If method wasn't explicitly set and we have data, assume POST
            if (result.method === 'GET') {
              result.method = 'POST';
            }
          }
          break;

        case '--data-binary':
          // Binary data
          if (i + 1 < tokens.length) {
            result.body = tokens[++i];
            if (result.method === 'GET') {
              result.method = 'POST';
            }
          }
          break;

        case '--compressed':
          // Add Accept-Encoding header for compression
          result.headers['Accept-Encoding'] = 'gzip, deflate, br';
          break;

        case '-u':
        case '--user':
          // Basic auth
          if (i + 1 < tokens.length) {
            const credentials = tokens[++i];
            const encoded = btoa(credentials);
            result.headers['Authorization'] = `Basic ${encoded}`;
          }
          break;

        case '-A':
        case '--user-agent':
          // User agent
          if (i + 1 < tokens.length) {
            result.headers['User-Agent'] = tokens[++i];
          }
          break;

        case '-e':
        case '--referer':
          // Referer
          if (i + 1 < tokens.length) {
            result.headers['Referer'] = tokens[++i];
          }
          break;

        case '-b':
        case '--cookie':
          // Cookies
          if (i + 1 < tokens.length) {
            result.headers['Cookie'] = tokens[++i];
          }
          break;

        default:
          // If it doesn't start with -, it might be the URL
          if (!token.startsWith('-') && !result.url && token !== 'curl') {
            result.url = token;
          }
          break;
      }
    }

    // Generate a name from the URL
    if (result.url) {
      try {
        const urlObj = new URL(result.url);
        const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
        if (pathParts.length > 0) {
          result.name = `${result.method} ${pathParts[pathParts.length - 1]}`;
        } else {
          result.name = `${result.method} ${urlObj.hostname}`;
        }
      } catch {
        result.name = `${result.method} Request`;
      }
    }

    // Validate that we have at least a URL
    if (!result.url) {
      return null;
    }

    return result;
  } catch (error) {
    console.error('Error parsing curl command:', error);
    return null;
  }
}

/**
 * Tokenize curl command, properly handling quoted strings
 */
function tokenizeCurlCommand(command: string): string[] {
  const tokens: string[] = [];
  let current = '';
  let inQuotes = false;
  let quoteChar = '';
  let escaped = false;

  for (let i = 0; i < command.length; i++) {
    const char = command[i];

    if (escaped) {
      current += char;
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (!inQuotes && (char === '"' || char === "'")) {
      inQuotes = true;
      quoteChar = char;
      continue;
    }

    if (inQuotes && char === quoteChar) {
      inQuotes = false;
      quoteChar = '';
      continue;
    }

    if (!inQuotes && /\s/.test(char)) {
      if (current.length > 0) {
        tokens.push(current);
        current = '';
      }
      continue;
    }

    current += char;
  }

  if (current.length > 0) {
    tokens.push(current);
  }

  return tokens;
}

/**
 * Check if clipboard content looks like a curl command
 */
export function isCurlCommand(text: string): boolean {
  const trimmed = text.trim();
  return trimmed.toLowerCase().startsWith('curl ') || trimmed.toLowerCase() === 'curl';
}