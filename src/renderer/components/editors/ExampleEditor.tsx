import React, { useState, useEffect } from 'react';
import HybridJsonEditor from '../HybridJsonEditor';
import { EditorDataLoader } from '../../utils/JsonUtils';

interface ExampleEditorProps {
  exampleId: string;
  exampleName: string;
  requestId: string;
  onSave: (data: ExampleData) => void;
  onClose: () => void;
  onChange?: () => void; // Callback when data changes (to mark tab as dirty)
  readOnly?: boolean;
  initialData?: ExampleData; // Pre-loaded data to avoid redundant API calls
}

interface ExampleData {
  id: string;
  name: string;
  description: string;
  requestId: string;
  request: {
    method: string;
    url: string;
    headers: Record<string, string>;
    params: Record<string, string>;
    body: string;
  };
  response: {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    body: string;
    responseTime: number;
  };
  notes: string;
  tags: string[];
}

const ExampleEditor: React.FC<ExampleEditorProps> = ({
  exampleId,
  exampleName,
  requestId,
  onSave,
  onClose,
  onChange,
  readOnly = false,
  initialData
}) => {
  const [exampleData, setExampleData] = useState<ExampleData>({
    id: exampleId,
    name: exampleName,
    description: '',
    requestId: requestId,
    request: {
      method: 'GET',
      url: '',
      headers: {},
      params: {},
      body: ''
    },
    response: {
      status: 200,
      statusText: 'OK',
      headers: {},
      body: '',
      responseTime: 0
    },
    notes: '',
    tags: []
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'request' | 'response' | 'notes'>('overview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    // Load example data from database or use initial data
    const loadData = async () => {
      try {
        // Use initial data if provided, otherwise load from API
        if (initialData) {
          console.log('[ExampleEditor] Using pre-loaded initial data:', initialData);
          setExampleData(initialData);
          setHasUnsavedChanges(false);
        } else {
          console.log('[ExampleEditor] Loading data from API for example:', exampleId);
          const data = await EditorDataLoader.loadExampleData(exampleId, requestId);
          setExampleData(data);
          setHasUnsavedChanges(false); // Mark as saved since we just loaded from database
        }
      } catch (error) {
        console.error('Failed to load example data:', error);
        // Set default data if loading fails
        setExampleData({
          id: exampleId,
          name: exampleName,
          description: '',
          requestId: requestId,
          request: {
            method: 'GET',
            url: '',
            headers: {},
            params: {},
            body: ''
          },
          response: {
            status: 200,
            statusText: 'OK',
            headers: {},
            body: '',
            responseTime: 0
          },
          notes: '',
          tags: []
        });
        setHasUnsavedChanges(false);
      }
    };

    loadData();
  }, [exampleId, exampleName, requestId, initialData]);

  // Listen for keyboard shortcut save event
  useEffect(() => {
    const handleSaveShortcut = () => {
      if (hasUnsavedChanges) {
        handleSave();
      }
    };

    window.addEventListener('editor-save-shortcut', handleSaveShortcut);
    return () => window.removeEventListener('editor-save-shortcut', handleSaveShortcut);
  }, [hasUnsavedChanges, exampleData]);

  const handleInputChange = (field: keyof ExampleData, value: any) => {
    setExampleData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleRequestChange = (field: keyof ExampleData['request'], value: any) => {
    setExampleData(prev => ({
      ...prev,
      request: { ...prev.request, [field]: value }
    }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleResponseChange = (field: keyof ExampleData['response'], value: any) => {
    setExampleData(prev => ({
      ...prev,
      response: { ...prev.response, [field]: value }
    }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleHeaderChange = (type: 'request' | 'response', key: string, value: string) => {
    setExampleData(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        headers: { ...prev[type].headers, [key]: value }
      }
    }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const addHeader = (type: 'request' | 'response') => {
    const key = `Header-${Object.keys(exampleData[type].headers).length + 1}`;
    handleHeaderChange(type, key, '');
  };

  const removeHeader = (type: 'request' | 'response', key: string) => {
    setExampleData(prev => {
      const newHeaders = { ...prev[type].headers };
      delete newHeaders[key];
      return {
        ...prev,
        [type]: { ...prev[type], headers: newHeaders }
      };
    });
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const addTag = () => {
    if (newTag.trim() && !exampleData.tags.includes(newTag.trim())) {
      setExampleData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
      setHasUnsavedChanges(true);
      if (onChange) {
        onChange();
      }
    }
  };

  const removeTag = (tag: string) => {
    setExampleData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleSave = () => {
    onSave(exampleData);
    setHasUnsavedChanges(false);
  };

  return (
    <div className="example-editor">
      <div className="editor-header">
        <div className="editor-title">
          <span className="editor-icon">📄</span>
          <h2>{exampleData.name}</h2>
          <span className="status-badge status-200">{exampleData.response.status}</span>
          {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
        </div>
        <div className="editor-actions">
          {!readOnly && (
            <button 
              className="btn btn-primary" 
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              Save Example
            </button>
          )}
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>

      <div className="editor-tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab ${activeTab === 'request' ? 'active' : ''}`}
          onClick={() => setActiveTab('request')}
        >
          Request
        </button>
        <button
          className={`tab ${activeTab === 'response' ? 'active' : ''}`}
          onClick={() => setActiveTab('response')}
        >
          Response
        </button>
        <button
          className={`tab ${activeTab === 'notes' ? 'active' : ''}`}
          onClick={() => setActiveTab('notes')}
        >
          Notes
        </button>
      </div>

      <div className="editor-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="form-group">
              <label htmlFor="example-name">Example Name</label>
              <input
                id="example-name"
                type="text"
                value={exampleData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                readOnly={readOnly}
                className="form-input"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="example-description">Description</label>
              <textarea
                id="example-description"
                value={exampleData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                readOnly={readOnly}
                className="form-textarea"
                rows={3}
                placeholder="Describe what this example demonstrates..."
              />
            </div>

            <div className="form-group">
              <label>Tags</label>
              <div className="tags-container">
                <div className="tags-list">
                  {exampleData.tags.map(tag => (
                    <span key={tag} className="tag">
                      {tag}
                      {!readOnly && (
                        <button
                          className="tag-remove"
                          onClick={() => removeTag(tag)}
                        >
                          ✕
                        </button>
                      )}
                    </span>
                  ))}
                </div>
                {!readOnly && (
                  <div className="tag-input-container">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      className="form-input tag-input"
                      placeholder="Add tag..."
                    />
                    <button className="btn btn-sm btn-primary" onClick={addTag}>
                      Add
                    </button>
                  </div>
                )}
              </div>
            </div>

            <div className="example-summary">
              <div className="summary-item">
                <label>Method</label>
                <span className={`method-badge method-${exampleData.request.method.toLowerCase()}`}>
                  {exampleData.request.method}
                </span>
              </div>
              <div className="summary-item">
                <label>URL</label>
                <span className="url-text">{exampleData.request.url}</span>
              </div>
              <div className="summary-item">
                <label>Status</label>
                <span className={`status-badge status-${Math.floor(exampleData.response.status / 100)}xx`}>
                  {exampleData.response.status} {exampleData.response.statusText}
                </span>
              </div>
              <div className="summary-item">
                <label>Response Time</label>
                <span className="time-text">{exampleData.response.responseTime}ms</span>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'request' && (
          <div className="request-tab">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="request-method">Method</label>
                <select
                  id="request-method"
                  value={exampleData.request.method}
                  onChange={(e) => handleRequestChange('method', e.target.value)}
                  disabled={readOnly}
                  className="form-select"
                >
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="PATCH">PATCH</option>
                  <option value="DELETE">DELETE</option>
                </select>
              </div>
              <div className="form-group flex-1">
                <label htmlFor="request-url">URL</label>
                <input
                  id="request-url"
                  type="url"
                  value={exampleData.request.url}
                  onChange={(e) => handleRequestChange('url', e.target.value)}
                  readOnly={readOnly}
                  className="form-input"
                />
              </div>
            </div>

            <div className="headers-section">
              <div className="section-header">
                <h3>Headers</h3>
                {!readOnly && (
                  <button className="btn btn-sm btn-primary" onClick={() => addHeader('request')}>
                    Add Header
                  </button>
                )}
              </div>
              <div className="headers-list">
                {Object.entries(exampleData.request.headers).map(([key, value]) => (
                  <div key={key} className="header-item">
                    <input
                      type="text"
                      value={key}
                      onChange={(e) => {
                        const newKey = e.target.value;
                        const newHeaders = { ...exampleData.request.headers };
                        delete newHeaders[key];
                        newHeaders[newKey] = value;
                        handleRequestChange('headers', newHeaders);
                      }}
                      readOnly={readOnly}
                      className="form-input header-key"
                      placeholder="Header name"
                    />
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => handleHeaderChange('request', key, e.target.value)}
                      readOnly={readOnly}
                      className="form-input header-value"
                      placeholder="Header value"
                    />
                    {!readOnly && (
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => removeHeader('request', key)}
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="body-section">
              <h3>Request Body</h3>
              <HybridJsonEditor
                value={exampleData.request.body}
                onChange={(value) => handleRequestChange('body', value)}
                readOnly={readOnly}
                height="300px"
                language="json"
              />
            </div>
          </div>
        )}

        {activeTab === 'response' && (
          <div className="response-tab">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="response-status">Status Code</label>
                <input
                  id="response-status"
                  type="number"
                  value={exampleData.response.status}
                  onChange={(e) => handleResponseChange('status', parseInt(e.target.value))}
                  readOnly={readOnly}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label htmlFor="response-status-text">Status Text</label>
                <input
                  id="response-status-text"
                  type="text"
                  value={exampleData.response.statusText}
                  onChange={(e) => handleResponseChange('statusText', e.target.value)}
                  readOnly={readOnly}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label htmlFor="response-time">Response Time (ms)</label>
                <input
                  id="response-time"
                  type="number"
                  value={exampleData.response.responseTime}
                  onChange={(e) => handleResponseChange('responseTime', parseInt(e.target.value))}
                  readOnly={readOnly}
                  className="form-input"
                />
              </div>
            </div>

            <div className="headers-section">
              <div className="section-header">
                <h3>Response Headers</h3>
                {!readOnly && (
                  <button className="btn btn-sm btn-primary" onClick={() => addHeader('response')}>
                    Add Header
                  </button>
                )}
              </div>
              <div className="headers-list">
                {Object.entries(exampleData.response.headers).map(([key, value]) => (
                  <div key={key} className="header-item">
                    <input
                      type="text"
                      value={key}
                      onChange={(e) => {
                        const newKey = e.target.value;
                        const newHeaders = { ...exampleData.response.headers };
                        delete newHeaders[key];
                        newHeaders[newKey] = value;
                        handleResponseChange('headers', newHeaders);
                      }}
                      readOnly={readOnly}
                      className="form-input header-key"
                      placeholder="Header name"
                    />
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => handleHeaderChange('response', key, e.target.value)}
                      readOnly={readOnly}
                      className="form-input header-value"
                      placeholder="Header value"
                    />
                    {!readOnly && (
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => removeHeader('response', key)}
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="body-section">
              <h3>Response Body</h3>
              <HybridJsonEditor
                value={exampleData.response.body}
                onChange={(value) => handleResponseChange('body', value)}
                readOnly={readOnly}
                height="400px"
                language="json"
              />
            </div>
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="notes-tab">
            <div className="form-group">
              <label htmlFor="example-notes">Notes (Markdown)</label>
              <textarea
                id="example-notes"
                value={exampleData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                readOnly={readOnly}
                className="form-textarea notes-editor"
                rows={20}
                placeholder="# Example Notes

Add notes about this example using Markdown..."
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExampleEditor;
