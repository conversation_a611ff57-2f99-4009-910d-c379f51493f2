import { BaseItemService } from './BaseItemService';
import { FullFolderData } from '../types/folder';

export class FolderService implements BaseItemService<FullFolderData> {
  async create(data: Partial<FullFolderData>, workspaceId: number): Promise<FullFolderData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const folder = await window.electronAPI.workspace.createFolder({
        name: data.name || 'Untitled Folder',
        description: data.description || '',
        parentId: data.parentId ? Number(data.parentId) : undefined,
        workspaceId
      });

      return {
        id: folder.hierarchyId || folder.id,
        entityId: folder.id,
        name: folder.name,
        description: folder.description,
        parentId: folder.parentId,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt
      };
    } catch (error) {
      console.error('Failed to create folder:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<FullFolderData>): Promise<FullFolderData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const updated = await window.electronAPI.workspace.updateFolder(entityId, {
        name: data.name,
        description: data.description,
        variables: data.variables,
        auth: data.auth,
        preRequestScript: data.preRequestScript,
        testScript: data.testScript
      });

      return {
        id: updated.hierarchyId || updated.id,
        entityId: updated.id,
        name: updated.name,
        description: updated.description,
        parentId: updated.parentId,
        variables: updated.variables || data.variables,
        auth: updated.auth || data.auth,
        preRequestScript: updated.preRequestScript || data.preRequestScript,
        testScript: updated.testScript || data.testScript,
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      };
    } catch (error) {
      console.error('Failed to update folder:', error);
      throw error;
    }
  }

  async get(id: string | number): Promise<FullFolderData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const folder = await window.electronAPI.workspace.getFolderDetails(entityId);

      if (!folder) return null;

      return {
        id: folder.hierarchyId || folder.id,
        entityId: folder.id,
        name: folder.name,
        description: folder.description,
        parentId: folder.parentId,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt
      };
    } catch (error) {
      console.error('Failed to get folder:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.workspace.deleteItem(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete folder:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // A folder is new if it doesn't have an entityId or has a temporary ID pattern
    return !data.id || 
           (typeof data.id === 'string' && data.id.startsWith('folder-'));
  }

  getDisplayName(data: any): string {
    return data.name || 'Untitled Folder';
  }

  getDefaultData(): Partial<FullFolderData> {
    return {
      name: 'Untitled Folder',
      description: ''
    };
  }

  /**
   * Folders don't need save dialog - they save directly to current workspace
   */
  needsSaveDialog(data: any): boolean {
    return false;
  }
}
