import React, { useState, useEffect } from 'react';

const KeyboardShortcuts: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMac, setIsMac] = useState(false);

  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0);

    const handleKeyDown = (event: KeyboardEvent) => {
      // Show shortcuts on Ctrl/Cmd + ?
      if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault();
        setIsVisible(true);
      }

      // Hide on Escape
      if (event.key === 'Escape') {
        setIsVisible(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const cmdKey = isMac ? '⌘' : 'Ctrl';

  if (!isVisible) return null;

  return (
    <div className="keyboard-shortcuts visible">
      <h4>Keyboard Shortcuts</h4>
      <ul>
        <li>
          <span>Send Request</span>
          <span className="shortcut">{cmdKey} + Enter</span>
        </li>
        <li>
          <span>Save Request</span>
          <span className="shortcut">{cmdKey} + S</span>
        </li>
        <li>
          <span>New Request</span>
          <span className="shortcut">{cmdKey} + N</span>
        </li>
        <li>
          <span>Close Tab</span>
          <span className="shortcut">{cmdKey} + W</span>
        </li>
        <li>
          <span>Duplicate Tab</span>
          <span className="shortcut">{cmdKey} + Shift + D</span>
        </li>
        <li>
          <span>Tab Search</span>
          <span className="shortcut">{cmdKey} + Shift + A</span>
        </li>
        <li>
          <span>AI Chat</span>
          <span className="shortcut">{cmdKey} + Shift + C</span>
        </li>
        <li>
          <span>Settings</span>
          <span className="shortcut">{cmdKey} + ,</span>
        </li>
        <li>
          <span>Show Shortcuts</span>
          <span className="shortcut">{cmdKey} + /</span>
        </li>
      </ul>
      <div style={{ marginTop: '12px', fontSize: '11px', opacity: 0.7 }}>
        Press Escape to close
      </div>
    </div>
  );
};

export default KeyboardShortcuts;
