<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Response Body Toolbar Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #212529;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            padding: 20px;
            background: #007acc;
            color: white;
        }
        
        .demo-content {
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        /* Response Body Toolbar Styles */
        .response-body-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            gap: 16px;
            min-height: 44px;
            flex-wrap: wrap;
        }
        
        .toolbar-left,
        .toolbar-center,
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .toolbar-right {
            gap: 12px;
        }
        
        .content-type-badge {
            background: #007acc;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .content-size {
            font-size: 12px;
            color: #6c757d;
        }
        
        .view-mode-buttons {
            display: flex;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .view-mode-btn {
            padding: 4px 12px;
            border: none;
            background: transparent;
            color: #212529;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .view-mode-btn:hover {
            background: #f8f9fa;
        }
        
        .view-mode-btn.active {
            background: #007acc;
            color: white;
        }
        
        .toolbar-search {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .search-input {
            border: none;
            background: transparent;
            padding: 6px 8px;
            font-size: 12px;
            color: #212529;
            outline: none;
            width: 120px;
        }
        
        .search-input::placeholder {
            color: #6c757d;
        }
        
        .search-btn {
            border: none;
            background: transparent;
            padding: 6px 8px;
            cursor: pointer;
            color: #6c757d;
            font-size: 12px;
            transition: color 0.2s;
        }
        
        .search-btn:hover {
            color: #007acc;
        }
        
        .toolbar-divider {
            width: 1px;
            height: 20px;
            background: #dee2e6;
            margin: 0 4px;
        }
        
        .toolbar-options,
        .toolbar-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .option-btn,
        .toolbar-btn {
            padding: 6px 8px;
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .option-btn:hover,
        .toolbar-btn:hover {
            background: #f8f9fa;
            border-color: #007acc;
            color: #212529;
        }
        
        .option-btn.active {
            background: #007acc;
            border-color: #007acc;
            color: white;
        }
        
        .response-body-content {
            flex: 1;
            overflow: auto;
            padding: 16px;
            background: white;
        }
        
        .demo-json {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre;
            margin: 0;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>Enhanced Response Body Toolbar</h1>
            <p>This demo showcases the enhanced response body toolbar with search, view options, and actions.</p>
        </div>
        
        <div class="demo-content">
            <!-- Enhanced Response Body Toolbar -->
            <div class="response-body-toolbar">
                <div class="toolbar-left">
                    <span class="content-type-badge">JSON</span>
                    <span class="content-size">2.4 KB</span>
                </div>

                <div class="toolbar-center">
                    <div class="view-mode-buttons">
                        <button class="view-mode-btn active" title="Pretty Print">
                            Pretty
                        </button>
                        <button class="view-mode-btn" title="Raw">
                            Raw
                        </button>
                        <button class="view-mode-btn" title="Tree View">
                            Tree
                        </button>
                    </div>
                </div>

                <div class="toolbar-right">
                    <div class="toolbar-search">
                        <input type="text" placeholder="Search..." class="search-input">
                        <button class="search-btn" title="Search">🔍</button>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-options">
                        <button class="option-btn active" title="Wrap Lines">↩️</button>
                        <button class="option-btn" title="Show Line Numbers">#</button>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-actions">
                        <button class="toolbar-btn" title="Copy Response">📋</button>
                        <button class="toolbar-btn" title="Download Response">💾</button>
                        <button class="toolbar-btn" title="Print">🖨️</button>
                    </div>
                </div>
            </div>

            <!-- Response Body Content -->
            <div class="response-body-content">
                <pre class="demo-json">{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "zipCode": "10001"
      },
      "preferences": {
        "theme": "dark",
        "notifications": true,
        "language": "en"
      }
    },
    {
      "id": 2,
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "address": {
        "street": "456 Oak Ave",
        "city": "Los Angeles",
        "zipCode": "90210"
      },
      "preferences": {
        "theme": "light",
        "notifications": false,
        "language": "es"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 2,
    "hasNext": false,
    "hasPrev": false
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0",
    "requestId": "req_123456789"
  }
}</pre>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity to the demo
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-mode-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
            });
        });

        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                alert(`${btn.title} clicked!`);
            });
        });

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        const searchBtn = document.querySelector('.search-btn');
        
        searchBtn.addEventListener('click', () => {
            const term = searchInput.value;
            if (term) {
                alert(`Searching for: "${term}"`);
            }
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    </script>
</body>
</html>
