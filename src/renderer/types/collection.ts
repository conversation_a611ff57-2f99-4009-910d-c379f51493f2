/**
 * Collection-related type definitions
 */

export interface Collection {
  id: number;
  name: string;
  description?: string;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CollectionData {
  id?: string | number;
  entityId?: number;
  name: string;
  description?: string;
  baseUrl?: string;
  variables?: Record<string, string> | CollectionVariable[];
  auth?: CollectionAuth;
  preRequestScript?: string;
  testScript?: string;
  documentation?: string;
  tags?: string[];
  version?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CollectionAuth {
  type: 'none' | 'bearer' | 'basic' | 'api-key';
  token?: string;
  username?: string;
  password?: string;
  key?: string;
  value?: string;
}

export interface CollectionVariable {
  key: string;
  value: string;
  description?: string;
  enabled: boolean;
}

export interface CollectionSettings {
  baseUrl?: string;
  timeout?: number;
  followRedirects?: boolean;
  validateSSL?: boolean;
}

export interface CollectionMetadata {
  version: string;
  schema: string;
  createdBy?: string;
  updatedBy?: string;
  tags: string[];
  documentation?: string;
}

export interface FullCollectionData extends CollectionData {
  settings?: CollectionSettings;
  metadata?: CollectionMetadata;
}
