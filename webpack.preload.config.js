const path = require('path');

module.exports = {
  mode: 'development',
  entry: './src/renderer/preload.ts',
  target: 'electron-preload',
  devtool: 'source-map',
  resolve: {
    extensions: ['.ts', '.js'],
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  output: {
    filename: 'preload.js',
    path: path.resolve(__dirname, 'dist/main'),
    clean: false,
  },
  externals: {
    electron: 'commonjs electron',
  },
};
