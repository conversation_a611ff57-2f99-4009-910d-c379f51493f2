import React, { useState, useRef, useEffect } from 'react';

interface Environment {
  id: string;
  name: string;
  variables: Record<string, string>;
}

interface EnvironmentSelectorProps {
  environments: Environment[];
  activeEnvironment?: string;
  onEnvironmentChange: (environmentId: string) => void;
}

const EnvironmentSelector: React.FC<EnvironmentSelectorProps> = ({
  environments,
  activeEnvironment,
  onEnvironmentChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const activeEnv = environments.find(env => env.id === activeEnvironment);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleEnvironmentSelect = (environmentId: string) => {
    onEnvironmentChange(environmentId);
    setIsOpen(false);
  };

  return (
    <div className="environment-selector" ref={dropdownRef}>
      <button
        className="environment-selector-btn"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="environment-icon">🌍</span>
        <span className="environment-name">
          {activeEnv ? activeEnv.name : 'No Environment'}
        </span>
        <span className="environment-arrow">▼</span>
      </button>

      {isOpen && (
        <div className="environment-dropdown">
          <div className="environment-dropdown-header">
            <span>Select Environment</span>
          </div>
          
          <div className="environment-list">
            <div
              className={`environment-option ${!activeEnvironment ? 'active' : ''}`}
              onClick={() => handleEnvironmentSelect('')}
            >
              <span className="environment-option-icon">🚫</span>
              <span className="environment-option-name">No Environment</span>
            </div>
            
            {environments.map(env => (
              <div
                key={env.id}
                className={`environment-option ${activeEnvironment === env.id ? 'active' : ''}`}
                onClick={() => handleEnvironmentSelect(env.id)}
              >
                <span className="environment-option-icon">🌍</span>
                <span className="environment-option-name">{env.name}</span>
                <div className="environment-option-variables">
                  {Object.keys(env.variables).length} variables
                </div>
              </div>
            ))}
          </div>
          
          <div className="environment-dropdown-footer">
            <button className="environment-manage-btn">
              Manage Environments
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnvironmentSelector;
