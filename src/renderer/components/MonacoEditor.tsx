import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';

interface MonacoEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  height?: string;
  readOnly?: boolean;
  placeholder?: string;
  theme?: 'vs-dark' | 'light';
  minimap?: boolean;
  wordWrap?: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  fontSize?: number;
  lineNumbers?: 'on' | 'off' | 'relative' | 'interval';
  folding?: boolean;
  scrollBeyondLastLine?: boolean;
  automaticLayout?: boolean;
  showToolbar?: boolean;
  label?: string;
}

const MonacoEditor: React.FC<MonacoEditorProps> = ({
  value,
  onChange,
  language = 'json',
  height = '100%',
  readOnly = false,
  placeholder = '',
  theme = 'vs-dark',
  minimap = false,
  wordWrap = 'on',
  fontSize = 14,
  lineNumbers = 'on',
  folding = true,
  scrollBeyondLastLine = false,
  automaticLayout = true,
  showToolbar = true,
  label = ''
}) => {
  // Debug logging to track value type issues
  if (typeof value !== 'string') {
    console.warn('[MonacoEditor] Received non-string value:', { value, type: typeof value });
  }
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure JSON language features
    if (language === 'json') {
      monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: true,
        allowComments: false,
        schemas: [],
        enableSchemaRequest: false
      });
    }

    // Add placeholder support
    if (placeholder && !value) {
      editor.setValue(placeholder);
      editor.setSelection(new monaco.Selection(1, 1, 1, 1));
    }

    // Auto-format JSON on mount if valid
    if (language === 'json' && value) {
      try {
        const parsed = JSON.parse(value);
        const formatted = JSON.stringify(parsed, null, 2);
        if (formatted !== value) {
          editor.setValue(formatted);
        }
      } catch (e) {
        // Invalid JSON, keep as is
      }
    }
  };

  const handleEditorChange = (newValue: string | undefined) => {
    if (newValue !== undefined) {
      onChange(newValue);
    }
  };

  const formatDocument = () => {
    if (editorRef.current && language === 'json') {
      try {
        const currentValue = editorRef.current.getValue();
        const parsed = JSON.parse(currentValue);
        const formatted = JSON.stringify(parsed, null, 2);
        editorRef.current.setValue(formatted);
      } catch (e) {
        // Invalid JSON, show error or keep as is
        console.warn('Invalid JSON, cannot format');
      }
    }
  };

  // Expose format function
  useEffect(() => {
    if (editorRef.current) {
      (editorRef.current as any).formatDocument = formatDocument;
    }
  }, [language]);

  const editorOptions = {
    readOnly,
    minimap: { enabled: minimap },
    wordWrap,
    fontSize,
    lineNumbers,
    folding,
    scrollBeyondLastLine,
    automaticLayout,
    contextmenu: true,
    selectOnLineNumbers: true,
    roundedSelection: false,
    cursorStyle: 'line' as const,
    renderLineHighlight: 'none' as const, // Remove line highlighting
    renderLineHighlightOnlyWhenFocus: false, // Disable focus-based highlighting
    hideCursorInOverviewRuler: true, // Hide cursor in overview ruler
    overviewRulerBorder: false, // Remove overview ruler border
    scrollbar: {
      vertical: 'auto' as const,
      horizontal: 'auto' as const,
      useShadows: false,
      verticalHasArrows: false,
      horizontalHasArrows: false,
      verticalScrollbarSize: 10,
      horizontalScrollbarSize: 10
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showStructs: true,
      showInterfaces: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: true,
      showValues: true,
      showConstants: true,
      showEnums: true,
      showEnumMembers: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true
    }
  };

  const getContentSize = () => {
    // Ensure value is a string before calling split
    const stringValue = typeof value === 'string' ? value : (value ? String(value) : '');
    const lines = stringValue.split('\n').length;
    const chars = stringValue.length;
    return `${lines} lines, ${chars} chars`;
  };

  return (
    <div className="monaco-editor-wrapper">
      {showToolbar && (
        <div className="monaco-editor-toolbar">
          <div className="monaco-editor-toolbar-left">
            {label && <span>{label}</span>}
            <span className="monaco-editor-language-badge">{language}</span>
          </div>
          <div className="monaco-editor-toolbar-right">
            <span className="monaco-editor-size-info">{getContentSize()}</span>
            {language === 'json' && !readOnly && (
              <button
                className="monaco-editor-format-btn"
                onClick={formatDocument}
                title="Format JSON (Ctrl+Shift+F)"
              >
                Format
              </button>
            )}
          </div>
        </div>
      )}
      <div className="monaco-editor-container">
        <Editor
          height="100%"
          language={language}
          value={typeof value === 'string' ? value : (value ? String(value) : '')}
          theme={theme}
          options={editorOptions}
          onMount={handleEditorDidMount}
          onChange={handleEditorChange}
        />
      </div>
    </div>
  );
};

export default MonacoEditor;
