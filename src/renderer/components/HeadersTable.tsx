import React, { useState, useEffect } from 'react';
import { KeyValuePair } from '../types/request';

interface HeadersTableProps {
  headers: Record<string, string> | KeyValuePair[];
  onChange: (headers: KeyValuePair[]) => void;
}

const HeadersTable: React.FC<HeadersTableProps> = ({ headers, onChange }) => {
  const [rows, setRows] = useState<KeyValuePair[]>([]);
  const [lastExternalHeaders, setLastExternalHeaders] = useState<Record<string, string> | KeyValuePair[]>([]);

  useEffect(() => {
    // Check if headers changed from external source (different from what we last sent)
    const headersChanged = JSON.stringify(headers) !== JSON.stringify(lastExternalHeaders);

    if (headersChanged) {
      let headerRows: KeyValuePair[];

      if (Array.isArray(headers)) {
        // Already in KeyValuePair format
        headerRows = [...headers];
      } else {
        // Convert from Record<string, string> format
        headerRows = Object.entries(headers).map(([key, value]) => ({
          key,
          value,
          description: '',
          enabled: true
        }));
      }

      // Always have at least one empty row (enabled by default)
      if (headerRows.length === 0) {
        headerRows.push({ key: '', value: '', description: '', enabled: true });
      }

      setRows(headerRows);
      setLastExternalHeaders(headers);
    }
  }, [headers, lastExternalHeaders]);

  const updateRow = (index: number, field: keyof KeyValuePair, value: string | boolean) => {
    const newRows = [...rows];
    newRows[index] = { ...newRows[index], [field]: value };

    // If this is the last row and user is typing, add a new empty row (enabled by default)
    if (index === newRows.length - 1 && field === 'key' && value && typeof value === 'string') {
      newRows.push({ key: '', value: '', description: '', enabled: true });
    }

    setRows(newRows);

    // Filter out empty rows and pass the full KeyValuePair array
    const validHeaders = newRows.filter(row => row.key.trim() !== '');

    // Track what we're sending to prevent circular updates
    setLastExternalHeaders(validHeaders);
    console.log('[HeadersTable] onChange called with validHeaders:', validHeaders);
    onChange(validHeaders);
  };

  const addRow = () => {
    setRows([...rows, { key: '', value: '', description: '', enabled: true }]);
  };

  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index);
    setRows(newRows);

    // Filter out empty rows and pass the full KeyValuePair array
    const validHeaders = newRows.filter(row => row.key.trim() !== '');
    onChange(validHeaders);
  };

  return (
    <div className="headers-table">
      <div className="table-wrapper">
        <table className="table">
          <thead>
            <tr>
              <th className="checkbox-col"></th>
              <th className="key-col">Key</th>
              <th className="value-col">Value</th>
              <th className="description-col">Description</th>
              <th className="actions-col"></th>
            </tr>
          </thead>
          <tbody>
            {rows.map((row, index) => (
              <tr key={index} className={!row.key && !row.value ? 'empty-row' : ''}>
                <td className="checkbox-col">
                  <input
                    type="checkbox"
                    checked={row.enabled}
                    onChange={(e) => updateRow(index, 'enabled', e.target.checked)}
                  />
                </td>
                <td className="key-col">
                  <input
                    type="text"
                    placeholder="Key"
                    value={row.key}
                    onChange={(e) => updateRow(index, 'key', e.target.value)}
                  />
                </td>
                <td className="value-col">
                  <input
                    type="text"
                    placeholder="Value"
                    value={row.value}
                    onChange={(e) => updateRow(index, 'value', e.target.value)}
                  />
                </td>
                <td className="description-col">
                  <input
                    type="text"
                    placeholder="Description"
                    value={row.description}
                    onChange={(e) => updateRow(index, 'description', e.target.value)}
                    onFocus={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                  />
                </td>
                <td className="actions-col">
                  <button
                    className="delete-btn"
                    onClick={() => deleteRow(index)}
                    disabled={rows.length === 1}
                    title="Delete header"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="3,6 5,6 21,6"/>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                    </svg>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <button className="add-row-btn" onClick={addRow}>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        Add Header
      </button>
    </div>
  );
};

export default HeadersTable;
