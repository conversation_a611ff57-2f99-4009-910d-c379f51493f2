export interface User {
  id: number;
  email: string;
  name?: string;
  role: 'admin' | 'user';
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name?: string;
}

export class AuthService {
  private currentUser: User | null = null;

  constructor() {
    // For now, we'll implement a simple auth system
    // TODO: Implement proper authentication with JWT/OAuth2
  }

  async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Mock authentication - replace with real implementation
      if (credentials.email === '<EMAIL>' && credentials.password === 'admin') {
        this.currentUser = {
          id: 1,
          email: credentials.email,
          name: 'Admin User',
          role: 'admin'
        };

        return {
          success: true,
          user: this.currentUser
        };
      }

      return {
        success: false,
        error: 'Invalid credentials'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  async register(data: RegisterData): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Mock registration - replace with real implementation
      const newUser: User = {
        id: Date.now(), // Mock ID
        email: data.email,
        name: data.name,
        role: 'user'
      };

      this.currentUser = newUser;

      return {
        success: true,
        user: newUser
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Registration failed'
      };
    }
  }

  async logout(): Promise<void> {
    this.currentUser = null;
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  hasRole(role: string): boolean {
    return this.currentUser?.role === role;
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      // TODO: Implement password change
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to change password'
      };
    }
  }
}
