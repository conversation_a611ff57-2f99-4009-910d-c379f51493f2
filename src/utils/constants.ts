// IPC Channel Constants
export const IPC_CHANNELS = {
  // Workspace operations
  WORKSPACE_GET_ALL: 'workspace:getAll',
  WORKSPACE_CREATE: 'workspace:create',
  WORKSPACE_UPDATE: 'workspace:update',
  WORKSPACE_DELETE: 'workspace:delete',
  WORKSPACE_GET_STATS: 'workspace:getStats',

  // Hierarchy operations
  HIERARCHY_GET_ITEMS: 'hierarchy:getItems',
  HIERARCHY_UPDATE_ITEM: 'hierarchy:updateItem',
  HIERARCHY_CREATE_ITEM: 'hierarchy:createItem',
  HIERARCHY_DELETE_ITEM: 'hierarchy:deleteItem',

  // Request operations
  REQUEST_EXECUTE: 'request:execute',
  REQUEST_SAVE: 'request:save',
  REQUEST_GET: 'request:get',
  REQUEST_DELETE: 'request:delete',
  REQUEST_DUPLICATE: 'request:duplicate',

  // Collection operations
  COLLECTION_IMPORT: 'collection:import',
  COLLECTION_EXPORT: 'collection:export',
  COLLECTION_RUN: 'collection:run',
  COLLECTION_CREATE: 'collection:create',
  COLLECTION_DELETE: 'collection:delete',

  // AI operations
  AI_GENERATE_REQUEST: 'ai:generateRequest',
  AI_SET_PROVIDER: 'ai:setProvider',
  AI_GET_PROVIDERS: 'ai:getProviders',
  AI_TEST_PROVIDER: 'ai:testProvider',

  // Authentication operations
  AUTH_LOGIN: 'auth:login',
  AUTH_LOGOUT: 'auth:logout',
  AUTH_REGISTER: 'auth:register',
  AUTH_GET_CURRENT_USER: 'auth:getCurrentUser',
  AUTH_CHANGE_PASSWORD: 'auth:changePassword',

  // Theme operations
  THEME_GET: 'theme:get',
  THEME_SET: 'theme:set',
  THEME_GET_VARIABLES: 'theme:getVariables',

  // Internationalization operations
  I18N_GET_LANGUAGE: 'i18n:getLanguage',
  I18N_SET_LANGUAGE: 'i18n:setLanguage',
  I18N_GET_TRANSLATIONS: 'i18n:getTranslations',
  I18N_GET_SUPPORTED_LANGUAGES: 'i18n:getSupportedLanguages',

  // Backup operations
  BACKUP_CREATE: 'backup:create',
  BACKUP_RESTORE: 'backup:restore',
  BACKUP_LIST: 'backup:list',
  BACKUP_CONFIGURE: 'backup:configure',

  // Logging operations
  LOG_GET_LOGS: 'log:getLogs',
  LOG_SET_LEVEL: 'log:setLevel',
  LOG_CLEAR: 'log:clear',

  // File operations
  FILE_OPEN_DIALOG: 'file:openDialog',
  FILE_SAVE_DIALOG: 'file:saveDialog',
  FILE_READ: 'file:read',
  FILE_WRITE: 'file:write',

  // Window operations
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_CLOSE: 'window:close',
  WINDOW_TOGGLE_DEVTOOLS: 'window:toggleDevTools'
} as const;

// HTTP Methods
export const HTTP_METHODS = [
  'GET',
  'POST',
  'PUT',
  'PATCH',
  'DELETE',
  'HEAD',
  'OPTIONS',
  'CONNECT',
  'TRACE'
] as const;

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  XML: 'application/xml',
  FORM_DATA: 'multipart/form-data',
  FORM_URLENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
  HTML: 'text/html',
  JAVASCRIPT: 'application/javascript',
  CSS: 'text/css',
  BINARY: 'application/octet-stream'
} as const;

// Item Types
export const ITEM_TYPES = {
  COLLECTION: 'COLLECTION',
  FOLDER: 'FOLDER',
  REQUEST: 'REQUEST',
  EXAMPLE: 'EXAMPLE'
} as const;

// Theme Modes
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const;

// Log Levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user'
} as const;

// Request Status
export const REQUEST_STATUS = {
  PENDING: 'pending',
  SUCCESS: 'success',
  ERROR: 'error',
  TIMEOUT: 'timeout'
} as const;

// Collection Run Status
export const COLLECTION_RUN_STATUS = {
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

// AI Provider Types
export const AI_PROVIDER_TYPES = {
  LOCAL: 'local',
  REMOTE: 'remote'
} as const;

// File Extensions
export const FILE_EXTENSIONS = {
  JSON: '.json',
  XML: '.xml',
  TXT: '.txt',
  CSV: '.csv',
  YAML: '.yaml',
  YML: '.yml'
} as const;

// Default Values
export const DEFAULTS = {
  REQUEST_TIMEOUT: 30000, // 30 seconds
  MAX_RESPONSE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_BACKUP_FILES: 7,
  DEFAULT_WORKSPACE_NAME: 'My Workspace',
  DEFAULT_COLLECTION_NAME: 'New Collection',
  DEFAULT_FOLDER_NAME: 'New Folder',
  DEFAULT_REQUEST_NAME: 'New Request',
  DEFAULT_EXAMPLE_NAME: 'Example Response'
} as const;

// Validation Rules
export const VALIDATION = {
  WORKSPACE_NAME_MIN_LENGTH: 1,
  WORKSPACE_NAME_MAX_LENGTH: 100,
  REQUEST_NAME_MIN_LENGTH: 1,
  REQUEST_NAME_MAX_LENGTH: 200,
  URL_MAX_LENGTH: 2048,
  HEADER_KEY_MAX_LENGTH: 100,
  HEADER_VALUE_MAX_LENGTH: 1000
} as const;

// Keyboard Shortcuts
export const SHORTCUTS = {
  NEW_REQUEST: 'CmdOrCtrl+N',
  SAVE_REQUEST: 'CmdOrCtrl+S',
  SEND_REQUEST: 'CmdOrCtrl+Enter',
  DUPLICATE_REQUEST: 'CmdOrCtrl+D',
  DELETE_ITEM: 'Delete',
  TOGGLE_SIDEBAR: 'CmdOrCtrl+B',
  TOGGLE_DEVTOOLS: 'F12',
  ZOOM_IN: 'CmdOrCtrl+Plus',
  ZOOM_OUT: 'CmdOrCtrl+-',
  ZOOM_RESET: 'CmdOrCtrl+0',
  FIND: 'CmdOrCtrl+F',
  FIND_NEXT: 'F3',
  FIND_PREVIOUS: 'Shift+F3'
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error occurred',
  TIMEOUT_ERROR: 'Request timeout',
  INVALID_URL: 'Invalid URL format',
  INVALID_JSON: 'Invalid JSON format',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  NOT_FOUND: 'Resource not found',
  SERVER_ERROR: 'Internal server error',
  UNKNOWN_ERROR: 'An unknown error occurred'
} as const;
