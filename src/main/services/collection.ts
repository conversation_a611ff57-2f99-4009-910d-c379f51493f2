import * as fs from 'fs';
import * as path from 'path';
import { PrismaClient } from '@prisma/client';
import type { Variable } from './variable-resolution';

export interface PostmanCollection {
  info: {
    name: string;
    description?: string;
    schema: string;
  };
  item: PostmanItem[];
}

export interface PostmanItem {
  name: string;
  request?: {
    method: string;
    header: Array<{ key: string; value: string }>;
    url: string | { raw: string };
    body?: any;
  };
  item?: PostmanItem[];
}

export class CollectionService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async importPostmanCollection(filePath: string): Promise<{ success: boolean; collectionId?: number; error?: string }> {
    try {
      // Read and parse the Postman collection file
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const postmanCollection: PostmanCollection = JSON.parse(fileContent);

      // Validate the collection format
      if (!postmanCollection.info || !postmanCollection.item) {
        return {
          success: false,
          error: 'Invalid Postman collection format'
        };
      }

      // Create a new collection in the database
      const collection = await this.prisma.collection.create({
        data: {
          name: postmanCollection.info.name,
          description: postmanCollection.info.description,
          variables: {}, // Initialize with empty variables object
          workspaceId: 1 // TODO: Get current workspace ID
        }
      });

      // Create the collection item in hierarchy
      await this.prisma.item.create({
        data: {
          type: 'COLLECTION',
          entityId: collection.id,
          workspaceId: 1,
          parentId: null,
          order: await this.getNextOrderNumber(1, null)
        }
      });

      // Import all items recursively
      await this.importItems(postmanCollection.item, collection.id, 1, collection.id);

      return {
        success: true,
        collectionId: collection.id
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to import collection'
      };
    }
  }

  async exportCollection(collectionId: number, filePath: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get collection data
      const collection = await this.prisma.collection.findUnique({
        where: { id: collectionId }
      });

      if (!collection) {
        return {
          success: false,
          error: 'Collection not found'
        };
      }

      // Get all items in the collection
      const items = await this.prisma.item.findMany({
        where: {
          workspaceId: collection.workspaceId,
          // TODO: Add logic to get items that belong to this collection
        }
      });

      // Convert to Postman format
      const postmanCollection: PostmanCollection = {
        info: {
          name: collection.name,
          description: collection.description || '',
          schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        },
        item: await this.convertToPostmanItems(items)
      };

      // Write to file
      fs.writeFileSync(filePath, JSON.stringify(postmanCollection, null, 2));

      return { success: true };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to export collection'
      };
    }
  }

  private async importItems(
    items: PostmanItem[], 
    collectionId: number, 
    workspaceId: number, 
    parentId: number | null
  ): Promise<void> {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (item.request) {
        // This is a request
        const headers: Record<string, string> = {};
        if (item.request.header) {
          item.request.header.forEach(h => {
            headers[h.key] = h.value;
          });
        }

        const url = typeof item.request.url === 'string' 
          ? item.request.url 
          : item.request.url.raw;

        const request = await this.prisma.request.create({
          data: {
            name: item.name,
            method: item.request.method,
            url: url,
            headers: JSON.stringify(headers || {}),
            body: item.request.body ? JSON.stringify(item.request.body) : undefined,
            workspaceId: workspaceId
          }
        });

        await this.prisma.item.create({
          data: {
            type: 'REQUEST',
            entityId: request.id,
            workspaceId: workspaceId,
            parentId: parentId,
            order: i
          }
        });

      } else if (item.item) {
        // This is a folder
        const folder = await this.prisma.folder.create({
          data: {
            name: item.name,
            description: '',
            workspaceId: workspaceId
          }
        });

        const folderItem = await this.prisma.item.create({
          data: {
            type: 'FOLDER',
            entityId: folder.id,
            workspaceId: workspaceId,
            parentId: parentId,
            order: i
          }
        });

        // Recursively import sub-items
        await this.importItems(item.item, collectionId, workspaceId, folderItem.id);
      }
    }
  }

  private async convertToPostmanItems(items: any[]): Promise<PostmanItem[]> {
    // TODO: Implement conversion from database items to Postman format
    return [];
  }

  private async getNextOrderNumber(workspaceId: number, parentId: number | null): Promise<number> {
    const maxOrder = await this.prisma.item.aggregate({
      where: {
        workspaceId,
        parentId
      },
      _max: {
        order: true
      }
    });

    return (maxOrder._max.order || 0) + 1;
  }

  async runCollection(collectionId: number): Promise<{ success: boolean; results?: any[]; error?: string }> {
    try {
      // TODO: Implement collection runner
      return {
        success: true,
        results: []
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to run collection'
      };
    }
  }

  /**
   * Get collection variables
   */
  async getCollectionVariables(collectionId: number): Promise<Record<string, Variable>> {
    try {
      const collection = await this.prisma.collection.findUnique({
        where: { id: collectionId }
      });

      if (!collection) {
        throw new Error('Collection not found');
      }

      return (collection.variables as any) || {};
    } catch (error: any) {
      throw new Error(`Failed to get collection variables: ${error.message}`);
    }
  }

  /**
   * Update collection variables
   */
  async updateCollectionVariables(
    collectionId: number,
    variables: Record<string, Variable>
  ): Promise<void> {
    try {
      await this.prisma.collection.update({
        where: { id: collectionId },
        data: { variables: variables as any }
      });
    } catch (error: any) {
      throw new Error(`Failed to update collection variables: ${error.message}`);
    }
  }

  /**
   * Add or update a single collection variable
   */
  async setCollectionVariable(
    collectionId: number,
    name: string,
    variable: Variable
  ): Promise<void> {
    try {
      const currentVariables = await this.getCollectionVariables(collectionId);
      currentVariables[name] = variable;
      await this.updateCollectionVariables(collectionId, currentVariables);
    } catch (error: any) {
      throw new Error(`Failed to set collection variable: ${error.message}`);
    }
  }

  /**
   * Remove a collection variable
   */
  async removeCollectionVariable(collectionId: number, name: string): Promise<void> {
    try {
      const currentVariables = await this.getCollectionVariables(collectionId);
      delete currentVariables[name];
      await this.updateCollectionVariables(collectionId, currentVariables);
    } catch (error: any) {
      throw new Error(`Failed to remove collection variable: ${error.message}`);
    }
  }

  /**
   * Create a collection run record
   */
  async createCollectionRun(
    collectionId: number,
    workspaceId: number,
    variables: Record<string, Variable> = {}
  ): Promise<number> {
    try {
      const run = await this.prisma.collectionRun.create({
        data: {
          collectionId,
          workspaceId,
          variables: variables as any,
          results: JSON.stringify([]),
          status: 'running'
        }
      });
      return run.id;
    } catch (error: any) {
      throw new Error(`Failed to create collection run: ${error.message}`);
    }
  }

  /**
   * Update collection run
   */
  async updateCollectionRun(
    runId: number,
    data: {
      results?: any[];
      variables?: Record<string, Variable>;
      status?: string;
      completedAt?: Date;
    }
  ): Promise<void> {
    try {
      await this.prisma.collectionRun.update({
        where: { id: runId },
        data: {
          ...data,
          variables: data.variables ? (data.variables as any) : undefined,
          results: data.results ? (data.results as any) : undefined
        }
      });
    } catch (error: any) {
      throw new Error(`Failed to update collection run: ${error.message}`);
    }
  }

  /**
   * Get collection run
   */
  async getCollectionRun(runId: number): Promise<any> {
    try {
      return await this.prisma.collectionRun.findUnique({
        where: { id: runId },
        include: {
          collection: true
        }
      });
    } catch (error: any) {
      throw new Error(`Failed to get collection run: ${error.message}`);
    }
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
