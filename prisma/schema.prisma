// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        Int       @id @default(autoincrement())
  email     String    @unique
  password  String
  name      String?
  role      String    @default("user") // 'admin' or 'user'
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  workspaces Workspace[]
}

model Workspace {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  users       User[]
  items       Item[]    @relation("WorkspaceItems")
  collections Collection[]
  folders     Folder[]
  requests    Request[]
  examples    Example[]
  environments Environment[]
  requestHistories RequestHistory[]
  chatSessions ChatSession[]
  chatMessages ChatMessage[]
}

model Item {
  id           Int       @id @default(autoincrement())
  type         String    // 'COLLECTION', 'FOLDER', 'REQUEST', 'EXAMPLE'
  entityId     Int       // ID of the entity in its respective table
  workspace    Workspace @relation("WorkspaceItems", fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId  Int
  parent       Item?     @relation("ItemHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  parentId     Int?
  children     Item[]    @relation("ItemHierarchy")
  order        Int       // Sorting order within parent
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@index([workspaceId, type])
  @@index([parentId, order])
  @@index([type, entityId]) // Index for efficient lookups
}

model Collection {
  id               Int       @id @default(autoincrement())
  name             String
  description      String?
  baseUrl          String?
  variables        Json
  auth             Json?     // Authentication configuration
  preRequestScript String?   // Script to run before requests
  testScript       String?   // Script to run after requests
  documentation    String?   // Collection documentation
  tags             Json?     // Array of tags
  version          String?   // Collection version
  workspace        Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId      Int
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  runs             CollectionRun[]
}

model Folder {
  id               Int       @id @default(autoincrement())
  name             String
  description      String?
  variables        Json?     // Folder-level variables
  auth             Json?     // Authentication configuration
  preRequestScript String?   // Script to run before requests in this folder
  testScript       String?   // Script to run after requests in this folder
  workspace        Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId      Int
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model Request {
  id           Int       @id @default(autoincrement())
  name         String
  method       String    // e.g., GET, POST, PUT, DELETE
  url          String
  headers      Json      // JSON object for headers
  params       Json?     // JSON object for query parameters
  body         Json?     // JSON object for request body
  description  String?   // Request description
  auth         Json?     // Authentication configuration
  preScript    String?   // Pre-request script
  testScript   String?   // Test script
  workspace    Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId  Int
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  examples     Example[]
}

model Example {
  id           Int       @id @default(autoincrement())
  name         String
  response     Json      // JSON object for response data
  request      Request   @relation(fields: [requestId], references: [id], onDelete: Cascade)
  requestId    Int
  workspace    Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId  Int
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

// Additional models for advanced features

model RequestHistory {
  id           Int       @id @default(autoincrement())
  requestId    Int?      // Optional - might not exist if request was deleted
  name         String    // Request name at time of execution
  method       String
  url          String
  headers      Json
  body         Json?
  response     Json?
  statusCode   Int?
  responseTime Int?      // in milliseconds
  error        String?
  environment  String?   // Environment name used
  workspace    Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId  Int
  executedAt   DateTime  @default(now())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model Environment {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  variables   Json      // Key-value pairs of environment variables
  isActive    Boolean   @default(false)
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([workspaceId, name])
}

model TestScript {
  id           Int       @id @default(autoincrement())
  name         String
  script       String    // JavaScript test script
  requestId    Int
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model CollectionRun {
  id           Int       @id @default(autoincrement())
  collection   Collection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  collectionId Int
  results      Json      // JSON array of test results
  variables    Json      
  startedAt    DateTime  @default(now())
  completedAt  DateTime?
  status       String    // 'running', 'completed', 'failed', 'cancelled'
  workspaceId  Int
}

model Settings {
  id           Int       @id @default(autoincrement())
  key          String    @unique
  value        Json
  userId       Int?
  workspaceId  Int?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model Log {
  id           Int       @id @default(autoincrement())
  level        String    // 'error', 'warn', 'info', 'debug'
  message      String
  metadata     Json?
  timestamp    DateTime  @default(now())
  userId       Int?
  workspaceId  Int?
}

model ChatSession {
  id          Int       @id @default(autoincrement())
  title       String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId Int
  messages    ChatMessage[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model ChatMessage {
  id            Int         @id @default(autoincrement())
  session       ChatSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  sessionId     Int
  workspace     Workspace   @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId   Int
  role          String      // 'user', 'assistant', 'system'
  content       String      // Text content
  images        Json?       // Array of image data/URLs
  metadata      Json?       // Additional metadata (model used, tokens, etc.)
  generatedRequest Json?    // Generated request JSON if applicable
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

model AppSettings {
  id          Int      @id @default(autoincrement())
  key         String   @unique
  value       Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}




