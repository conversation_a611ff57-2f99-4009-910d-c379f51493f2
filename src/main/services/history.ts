import { PrismaClient } from '@prisma/client';

export interface HistoryEntry {
  id: number;
  requestId?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  response?: any;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  environment?: string;
  workspaceId: number;
  executedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateHistoryData {
  requestId?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  response?: any;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  environment?: string;
  workspaceId: number;
}

export class HistoryService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get request history for a workspace
   */
  async getHistory(workspaceId: number, limit: number = 100, offset: number = 0): Promise<HistoryEntry[]> {
    try {
      const histories = await this.prisma.requestHistory.findMany({
        where: { workspaceId },
        orderBy: { executedAt: 'desc' },
        take: limit,
        skip: offset
      });

      return histories.map(history => ({
        ...history,
        headers: history.headers as Record<string, string>,
        body: history.body,
        response: history.response
      }));
    } catch (error) {
      console.error('Error fetching history:', error);
      throw new Error('Failed to fetch history');
    }
  }

  /**
   * Get history entry by ID
   */
  async getHistoryEntry(id: number): Promise<HistoryEntry | null> {
    try {
      const history = await this.prisma.requestHistory.findUnique({
        where: { id }
      });

      if (!history) return null;

      return {
        ...history,
        headers: history.headers as Record<string, string>,
        body: history.body,
        response: history.response
      };
    } catch (error) {
      console.error('Error fetching history entry:', error);
      throw new Error('Failed to fetch history entry');
    }
  }

  /**
   * Create a new history entry
   */
  async createHistoryEntry(data: CreateHistoryData): Promise<HistoryEntry> {
    try {
      const history = await this.prisma.requestHistory.create({
        data: {
          requestId: data.requestId,
          name: data.name,
          method: data.method,
          url: data.url,
          headers: data.headers,
          body: data.body,
          response: data.response,
          statusCode: data.statusCode,
          responseTime: data.responseTime,
          error: data.error,
          environment: data.environment,
          workspaceId: data.workspaceId
        }
      });

      return {
        ...history,
        headers: history.headers as Record<string, string>,
        body: history.body,
        response: history.response
      };
    } catch (error) {
      console.error('Error creating history entry:', error);
      throw new Error('Failed to create history entry');
    }
  }

  /**
   * Delete a history entry
   */
  async deleteHistoryEntry(id: number): Promise<void> {
    try {
      await this.prisma.requestHistory.delete({
        where: { id }
      });
    } catch (error) {
      console.error('Error deleting history entry:', error);
      throw new Error('Failed to delete history entry');
    }
  }

  /**
   * Clear all history for a workspace
   */
  async clearHistory(workspaceId: number): Promise<void> {
    try {
      await this.prisma.requestHistory.deleteMany({
        where: { workspaceId }
      });
    } catch (error) {
      console.error('Error clearing history:', error);
      throw new Error('Failed to clear history');
    }
  }

  /**
   * Get history statistics
   */
  async getHistoryStats(workspaceId: number): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
  }> {
    try {
      const total = await this.prisma.requestHistory.count({
        where: { workspaceId }
      });

      const successful = await this.prisma.requestHistory.count({
        where: { 
          workspaceId,
          statusCode: {
            gte: 200,
            lt: 400
          }
        }
      });

      const failed = total - successful;

      const avgResponseTime = await this.prisma.requestHistory.aggregate({
        where: { 
          workspaceId,
          responseTime: { not: null }
        },
        _avg: {
          responseTime: true
        }
      });

      return {
        totalRequests: total,
        successfulRequests: successful,
        failedRequests: failed,
        averageResponseTime: Math.round(avgResponseTime._avg.responseTime || 0)
      };
    } catch (error) {
      console.error('Error fetching history stats:', error);
      throw new Error('Failed to fetch history stats');
    }
  }

  /**
   * Search history entries
   */
  async searchHistory(
    workspaceId: number, 
    query: string, 
    limit: number = 50
  ): Promise<HistoryEntry[]> {
    try {
      const histories = await this.prisma.requestHistory.findMany({
        where: {
          workspaceId,
          OR: [
            { name: { contains: query } },
            { url: { contains: query } },
            { method: { contains: query } }
          ]
        },
        orderBy: { executedAt: 'desc' },
        take: limit
      });

      return histories.map(history => ({
        ...history,
        headers: history.headers as Record<string, string>,
        body: history.body,
        response: history.response
      }));
    } catch (error) {
      console.error('Error searching history:', error);
      throw new Error('Failed to search history');
    }
  }

  /**
   * Convert history entry to request format for replay
   */
  convertToRequest(history: HistoryEntry): {
    name: string;
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: string;
  } {
    return {
      name: `${history.name} (from history)`,
      method: history.method,
      url: history.url,
      headers: history.headers,
      body: typeof history.body === 'string' ? history.body : JSON.stringify(history.body, null, 2)
    };
  }
}
