import React, { useEffect, useRef } from 'react';

interface MenuHandlerProps {
  onNewRequest: () => void;
  onNewCollection: () => void;
  onImportPostman: () => void;
  onImportOpenAPI: () => void;
  onExportCollection: () => void;
  onSettings: () => void;
  onViewCollections: () => void;
  onViewEnvironments: () => void;
  onViewHistory: () => void;
  onAbout: () => void;
  onDocumentation: () => void;
  onShortcuts: () => void;
}

const MenuHandler: React.FC<MenuHandlerProps> = ({
  onNewRequest,
  onNewCollection,
  onImportPostman,
  onImportOpenAPI,
  onExportCollection,
  onSettings,
  onViewCollections,
  onViewEnvironments,
  onViewHistory,
  onAbout,
  onDocumentation,
  onShortcuts
}) => {
  const listenersRegistered = useRef(false);

  useEffect(() => {
    // Check if we're in Electron environment and listeners aren't already registered
    if (typeof window !== 'undefined' && window.electronAPI && !listenersRegistered.current) {
      const { electronAPI } = window;

      // Menu event handlers
      const handleNewRequest = () => onNewRequest();
      const handleNewCollection = () => onNewCollection();
      const handleImportPostman = () => onImportPostman();
      const handleImportOpenAPI = () => onImportOpenAPI();
      const handleExportCollection = () => onExportCollection();
      const handleSettings = () => onSettings();
      const handleViewCollections = () => onViewCollections();
      const handleViewEnvironments = () => onViewEnvironments();
      const handleViewHistory = () => onViewHistory();
      const handleAbout = () => onAbout();
      const handleDocumentation = () => onDocumentation();
      const handleShortcuts = () => onShortcuts();

      // Register menu event listeners
      electronAPI.onMenuEvent('menu:new-request', handleNewRequest);
      electronAPI.onMenuEvent('menu:new-collection', handleNewCollection);
      electronAPI.onMenuEvent('menu:import-postman', handleImportPostman);
      electronAPI.onMenuEvent('menu:import-openapi', handleImportOpenAPI);
      electronAPI.onMenuEvent('menu:export-collection', handleExportCollection);
      electronAPI.onMenuEvent('menu:settings', handleSettings);
      electronAPI.onMenuEvent('menu:view-collections', handleViewCollections);
      electronAPI.onMenuEvent('menu:view-environments', handleViewEnvironments);
      electronAPI.onMenuEvent('menu:view-history', handleViewHistory);
      electronAPI.onMenuEvent('menu:about', handleAbout);
      electronAPI.onMenuEvent('menu:documentation', handleDocumentation);
      electronAPI.onMenuEvent('menu:shortcuts', handleShortcuts);

      listenersRegistered.current = true;

      // Cleanup function
      return () => {
        if (listenersRegistered.current) {
          electronAPI.removeMenuEvent('menu:new-request', handleNewRequest);
          electronAPI.removeMenuEvent('menu:new-collection', handleNewCollection);
          electronAPI.removeMenuEvent('menu:import-postman', handleImportPostman);
          electronAPI.removeMenuEvent('menu:import-openapi', handleImportOpenAPI);
          electronAPI.removeMenuEvent('menu:export-collection', handleExportCollection);
          electronAPI.removeMenuEvent('menu:settings', handleSettings);
          electronAPI.removeMenuEvent('menu:view-collections', handleViewCollections);
          electronAPI.removeMenuEvent('menu:view-environments', handleViewEnvironments);
          electronAPI.removeMenuEvent('menu:view-history', handleViewHistory);
          electronAPI.removeMenuEvent('menu:about', handleAbout);
          electronAPI.removeMenuEvent('menu:documentation', handleDocumentation);
          electronAPI.removeMenuEvent('menu:shortcuts', handleShortcuts);
          listenersRegistered.current = false;
        }
      };
    }
  }, [
    onNewRequest,
    onNewCollection,
    onImportPostman,
    onImportOpenAPI,
    onExportCollection,
    onSettings,
    onViewCollections,
    onViewEnvironments,
    onViewHistory,
    onAbout,
    onDocumentation,
    onShortcuts
  ]);

  // This component doesn't render anything
  return null;
};

export default MenuHandler;
