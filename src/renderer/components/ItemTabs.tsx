import React, { useState, useEffect, useRef } from 'react';
import EnvironmentSelector from './EnvironmentSelector';
import TabSearchDropdown from './TabSearchDropdown';
import TabContextMenu from './TabContextMenu';
import ConfirmationDialog from './ConfirmationDialog';
import { TabManagementService } from '../services/TabManagementService';
import { useKeyboardShortcuts, SHORTCUTS, createTabNumberShortcuts } from '../hooks/useKeyboardShortcuts';

// Generic item interface that can represent any type of tab
export interface TabItem {
  id: string;
  type: 'request' | 'collection' | 'folder' | 'example' | 'environment' | 'history';
  data: any; // The full item data fetched from database
  hasUnsavedChanges?: boolean;
}



interface Environment {
  id: string;
  name: string;
  variables: Record<string, string>;
}

interface ItemTabsProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onForceCloseAll?: () => void;
  onNewTab: () => void;
  onSaveItem?: (tabId: string) => void;
  environments?: Environment[];
  activeEnvironment?: string;
  onEnvironmentChange?: (environmentId: string) => void;
}

const ItemTabs: React.FC<ItemTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  onTabClose,
  onForceCloseAll,
  onNewTab,
  onSaveItem,
  environments = [],
  activeEnvironment,
  onEnvironmentChange
}) => {
  const [visibleTabs, setVisibleTabs] = useState<TabItem[]>([]);
  const [hiddenTabs, setHiddenTabs] = useState<TabItem[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState<string | null>(null);
  const [showContextMenu, setShowContextMenu] = useState<string | null>(null);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    type: 'warning' as 'warning' | 'danger'
  });

  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Calculate visible and hidden tabs based on container width
  const calculateVisibleTabs = () => {
    if (!tabsContainerRef.current || tabs.length === 0) {
      setVisibleTabs(tabs);
      setHiddenTabs([]);
      return;
    }

    const container = tabsContainerRef.current;
    const containerWidth = container.offsetWidth;
    const dropdownButtonWidth = 40; // Width of the dropdown button
    const newTabButtonWidth = 40; // Width of the new tab button
    const availableWidth = containerWidth - dropdownButtonWidth - newTabButtonWidth - 20; // 20px for padding

    let totalWidth = 0;
    let visibleCount = 0;
    const tabWidth = 200; // Approximate width per tab

    for (let i = 0; i < tabs.length; i++) {
      if (totalWidth + tabWidth <= availableWidth) {
        totalWidth += tabWidth;
        visibleCount++;
      } else {
        break;
      }
    }

    // Ensure at least one tab is visible
    if (visibleCount === 0 && tabs.length > 0) {
      visibleCount = 1;
    }

    // Ensure active tab is always visible
    const activeTabIndex = tabs.findIndex(tab => tab.id === activeTab);
    if (activeTabIndex >= visibleCount && activeTabIndex !== -1) {
      // Move active tab to visible area
      const reorderedTabs = [...tabs];
      const activeTabItem = reorderedTabs.splice(activeTabIndex, 1)[0];
      reorderedTabs.splice(visibleCount - 1, 0, activeTabItem);
      
      setVisibleTabs(reorderedTabs.slice(0, visibleCount));
      setHiddenTabs(reorderedTabs.slice(visibleCount));
    } else {
      setVisibleTabs(tabs.slice(0, visibleCount));
      setHiddenTabs(tabs.slice(visibleCount));
    }
  };

  useEffect(() => {
    calculateVisibleTabs();
  }, [tabs, activeTab]);

  useEffect(() => {
    const handleResize = () => {
      calculateVisibleTabs();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [tabs]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Unified keyboard shortcuts
  const shortcuts = [
    SHORTCUTS.CLOSE_TAB,
    SHORTCUTS.NEW_TAB,
    SHORTCUTS.SAVE_GLOBAL,
    SHORTCUTS.CLOSE_ALL_TABS,
    SHORTCUTS.NEXT_TAB,
    SHORTCUTS.PREV_TAB,
    SHORTCUTS.RENAME_TAB,
    ...createTabNumberShortcuts(tabs.length)
  ];

  const shortcutHandlers = {
    'w': () => {
      if (activeTab) {
        handleTabClose(activeTab);
      }
    },
    't': () => {
      onNewTab();
    },
    's': () => {
      if (activeTab && onSaveItem) {
        onSaveItem(activeTab);
      }
    },
    'W': () => {
      handleCloseAllTabs();
    },
    'Tab': (event: KeyboardEvent) => {
      if (event.shiftKey) {
        // Previous tab
        const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        if (tabs[prevIndex]) {
          onTabChange(tabs[prevIndex].id);
        }
      } else {
        // Next tab
        const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        if (tabs[nextIndex]) {
          onTabChange(tabs[nextIndex].id);
        }
      }
    },
    'F2': () => {
      if (activeTab) {
        handleRenameTab(activeTab);
      }
    },
    // Tab number shortcuts
    '1': () => switchToTabByIndex(0),
    '2': () => switchToTabByIndex(1),
    '3': () => switchToTabByIndex(2),
    '4': () => switchToTabByIndex(3),
    '5': () => switchToTabByIndex(4),
    '6': () => switchToTabByIndex(5),
    '7': () => switchToTabByIndex(6),
    '8': () => switchToTabByIndex(7),
    '9': () => switchToTabByIndex(8)
  };

  const switchToTabByIndex = (index: number) => {
    if (index < tabs.length) {
      onTabChange(tabs[index].id);
    }
  };

  useKeyboardShortcuts(shortcuts, shortcutHandlers);

  // Helper function to determine if an item is new (not saved to database)
  const isNewItem = (tab: TabItem | undefined): boolean => {
    if (!tab) return false;
    return TabManagementService.isNewTabItem(tab);
  };

  const hasUnsavedChanges = (tab: TabItem | undefined): boolean => {
    if (!tab) return false;
    return TabManagementService.hasUnsavedChanges(tab);
  };

  // Helper function to get display name for a tab
  const getTabDisplayName = (tab: TabItem): string => {
    return TabManagementService.getTabDisplayName(tab);
  };

  // Helper function to get icon for a tab
  const getTabIcon = (tab: TabItem): string => {
    return TabManagementService.getTabIcon(tab);
  };

  // Helper function to get method badge for requests
  const getMethodBadge = (tab: TabItem): string | null => {
    return TabManagementService.getMethodBadge(tab);
  };

  // Helper function to get method color
  const getMethodColor = (method: string): string => {
    const colors: Record<string, string> = {
      'GET': '#10b981',
      'POST': '#3b82f6',
      'PUT': '#f59e0b',
      'PATCH': '#8b5cf6',
      'DELETE': '#ef4444',
      'HEAD': '#6b7280',
      'OPTIONS': '#6b7280'
    };
    return colors[method.toUpperCase()] || '#6b7280';
  };

  const handleTabClick = (tabId: string) => {
    onTabChange(tabId);
    setShowDropdown(false);

    // Ensure the tab is visible in the tab header
    ensureTabVisible(tabId);
  };

  const ensureTabVisible = (tabId: string) => {
    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    if (tabIndex === -1) return;

    // If tab is in hidden tabs, we need to recalculate visible tabs
    const hiddenTabIndex = hiddenTabs.findIndex(tab => tab.id === tabId);
    if (hiddenTabIndex !== -1) {
      // Force recalculation to make the tab visible
      setTimeout(() => {
        calculateVisibleTabs();
      }, 0);
    }
  };

  const handleTabClose = (tabId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    const tab = tabs.find(t => t.id === tabId);
    if (!tab) {
      console.warn(`Tab with id ${tabId} not found`);
      return;
    }

    if (hasUnsavedChanges(tab)) {
      setShowSaveDialog(tabId);
    } else {
      onTabClose(tabId);
    }
  };

  const handleSaveAndClose = (tabId: string) => {
    if (onSaveItem) {
      onSaveItem(tabId);
    }
    setShowSaveDialog(null);
    onTabClose(tabId);
  };

  const handleDiscardAndClose = (tabId: string) => {
    setShowSaveDialog(null);
    onTabClose(tabId);
  };

  const handleCancelClose = () => {
    setShowSaveDialog(null);
  };

  const handleRenameTab = (tabId: string) => {
    // TODO: Implement tab renaming functionality
    console.log('Rename tab:', tabId);
  };

  const handleTabContextMenu = (event: React.MouseEvent, tabId: string) => {
    event.preventDefault();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setShowContextMenu(tabId);
  };

  const handleCloseTabsToLeft = (tabIndex: number) => {
    const tabsToClose = tabs.slice(0, tabIndex);
    const unsavedTabs = tabsToClose.filter(tab => hasUnsavedChanges(tab));

    if (unsavedTabs.length > 0) {
      // Show confirmation dialog for unsaved tabs
      setConfirmDialog({
        isOpen: true,
        title: 'Close Tabs to the Left',
        message: `You have ${unsavedTabs.length} unsaved tab(s) to the left. Close anyway?`,
        onConfirm: () => {
          tabsToClose.forEach(tab => onTabClose(tab.id));
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        },
        type: 'warning'
      });
      return;
    }

    // Close all tabs to the left
    tabsToClose.forEach(tab => onTabClose(tab.id));
  };

  const handleCloseTabsToRight = (tabIndex: number) => {
    const tabsToClose = tabs.slice(tabIndex + 1);
    const unsavedTabs = tabsToClose.filter(tab => hasUnsavedChanges(tab));

    if (unsavedTabs.length > 0) {
      // Show confirmation dialog for unsaved tabs
      setConfirmDialog({
        isOpen: true,
        title: 'Close Tabs to the Right',
        message: `You have ${unsavedTabs.length} unsaved tab(s) to the right. Close anyway?`,
        onConfirm: () => {
          tabsToClose.forEach(tab => onTabClose(tab.id));
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        },
        type: 'warning'
      });
      return;
    }

    // Close all tabs to the right
    tabsToClose.forEach(tab => onTabClose(tab.id));
  };

  const handleCloseAllTabs = () => {
    const unsavedTabs = tabs.filter(tab => hasUnsavedChanges(tab));

    if (unsavedTabs.length > 0) {
      // Show confirmation dialog for unsaved tabs
      setConfirmDialog({
        isOpen: true,
        title: 'Close All Tabs',
        message: `You have ${unsavedTabs.length} unsaved tab(s). Close all anyway?`,
        onConfirm: () => {
          tabs.forEach(tab => onTabClose(tab.id));
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        },
        type: 'warning'
      });
      return;
    }

    // Close all tabs
    tabs.forEach(tab => onTabClose(tab.id));
  };

  const handleForceCloseAll = () => {
    // Force close all tabs without confirmation
    if (onForceCloseAll) {
      onForceCloseAll();
    } else {
      // Fallback to regular close all
      tabs.forEach(tab => onTabClose(tab.id));
    }
  };

  const handleDuplicateTab = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) {
      console.warn(`Tab with id ${tabId} not found for duplication`);
      return;
    }

    // For now, we'll use onNewTab and let the parent handle the duplication logic
    onNewTab();
  };

  if (tabs.length === 0) {
    return (
      <div className="request-tabs-container">
        <div className="request-tabs-left">
          <div className="request-tabs" ref={tabsContainerRef}>
            {/* Empty state */}
          </div>
        </div>
        <div className="request-tabs-right">
          <button
            className="new-tab-btn"
            onClick={onNewTab}
            title="New Tab (Ctrl+T)"
          >
            +
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="request-tabs-container">
      <div className="request-tabs-left">
        <div className="request-tabs" ref={tabsContainerRef}>
          {visibleTabs.map((tab) => {
            const isActive = tab.id === activeTab;
            const methodBadge = getMethodBadge(tab);
            const displayName = getTabDisplayName(tab);
            const unsaved = hasUnsavedChanges(tab);

            return (
              <div
                key={tab.id}
                className={`request-tab ${isActive ? 'active' : ''} ${unsaved ? 'unsaved' : ''}`}
                onClick={() => handleTabClick(tab.id)}
                onContextMenu={(e) => handleTabContextMenu(e, tab.id)}
                title={`${displayName}${unsaved ? ' (unsaved)' : ''}`}
              >
                <div className="tab-content">
                  {methodBadge && (
                    <span
                      className="tab-method"
                      style={{ backgroundColor: getMethodColor(methodBadge) }}
                    >
                      {methodBadge}
                    </span>
                  )}
                  <span className="tab-name">{displayName}</span>
                </div>

                <div className="tab-close-container">
                  {unsaved ? (
                    <span className="tab-unsaved-dot">●</span>
                  ) : (
                    <button
                      className="tab-close"
                      onClick={(e) => handleTabClose(tab.id, e)}
                      title="Close Tab (Ctrl+W)"
                    >
                      ×
                    </button>
                  )}
                </div>
              </div>
            );
          })}

          <button
            className="new-tab-btn"
            onClick={onNewTab}
            title="New Tab (Ctrl+T)"
          >
            +
          </button>
        </div>
      </div>

      <div className="request-tabs-right">
        {hiddenTabs.length > 0 && (
          <div className="tabs-dropdown" ref={dropdownRef}>
            <button
              className="tab-action-btn dropdown-btn"
              onClick={() => setShowDropdown(!showDropdown)}
              title={`${hiddenTabs.length} more tabs`}
            >
              ▼
            </button>
            {showDropdown && (
              <TabSearchDropdown
                tabs={hiddenTabs.map(tab => ({
                  id: tab.id,
                  name: getTabDisplayName(tab),
                  method: getMethodBadge(tab) || '',
                  url: tab.type === 'request' ? tab.data.url || '' : '',
                  hasUnsavedChanges: hasUnsavedChanges(tab)
                }))}
                activeTab={activeTab}
                onTabSelect={handleTabClick}
                onTabClose={handleTabClose}
              />
            )}
          </div>
        )}

        {environments && environments.length > 0 && (
          <EnvironmentSelector
            environments={environments}
            activeEnvironment={activeEnvironment}
            onEnvironmentChange={onEnvironmentChange}
          />
        )}
      </div>

      {/* Context Menu */}
      {showContextMenu && (() => {
        const contextTab = tabs.find(tab => tab.id === showContextMenu);
        const tabIndex = tabs.findIndex(tab => tab.id === showContextMenu);

        if (!contextTab) return null;

        return (
          <TabContextMenu
            isVisible={true}
            tabId={showContextMenu}
            tabIndex={tabIndex}
            totalTabs={tabs.length}
            hasUnsavedChanges={hasUnsavedChanges(contextTab)}
            position={contextMenuPosition}
            onClose={() => setShowContextMenu(null)}
            onCloseTab={(tabId) => {
              setShowContextMenu(null);
              handleTabClose(tabId);
            }}
            onCloseTabsToLeft={(index) => {
              setShowContextMenu(null);
              handleCloseTabsToLeft(index);
            }}
            onCloseTabsToRight={(index) => {
              setShowContextMenu(null);
              handleCloseTabsToRight(index);
            }}
            onCloseAllTabs={() => {
              setShowContextMenu(null);
              handleCloseAllTabs();
            }}
            onForceCloseAll={() => {
              setShowContextMenu(null);
              handleForceCloseAll();
            }}
            onDuplicateTab={(tabId) => {
              setShowContextMenu(null);
              handleDuplicateTab(tabId);
            }}
            onRenameTab={(tabId) => {
              setShowContextMenu(null);
              handleRenameTab(tabId);
            }}
          />
        );
      })()}

      {/* Save Dialog */}
      {showSaveDialog && (
        <>
          <div className="save-dialog-overlay" onClick={handleCancelClose} />
          <div className="save-dialog">
            <div className="save-dialog-header">
              <h3>Unsaved Changes</h3>
            </div>
            <div className="save-dialog-content">
              <p>You have unsaved changes. What would you like to do?</p>
            </div>
            <div className="save-dialog-actions">
              <button
                className="btn btn-primary"
                onClick={() => handleSaveAndClose(showSaveDialog)}
              >
                Save & Close
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => handleDiscardAndClose(showSaveDialog)}
              >
                Discard Changes
              </button>
              <button
                className="btn btn-outline"
                onClick={handleCancelClose}
              >
                Cancel
              </button>
            </div>
          </div>
        </>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={confirmDialog.onConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        type={confirmDialog.type}
      />
    </div>
  );
};

export default ItemTabs;
