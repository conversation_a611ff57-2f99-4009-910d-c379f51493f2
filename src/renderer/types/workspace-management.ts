export interface Workspace {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  settings: WorkspaceSettings;
  collections: string[]; // Collection IDs
  environments: string[]; // Environment IDs
}

export interface WorkspaceSettings {
  theme: 'light' | 'dark' | 'auto';
  defaultEnvironment?: string;
  autoSave: boolean;
  requestTimeout: number;
  followRedirects: boolean;
  validateSSL: boolean;
  proxySettings?: ProxySettings;
}

export interface ProxySettings {
  enabled: boolean;
  host: string;
  port: number;
  username?: string;
  password?: string;
  bypassList: string[];
}

export interface WorkspaceCreateRequest {
  name: string;
  description?: string;
  copyFrom?: string; // Copy settings from another workspace
}

export interface WorkspaceUpdateRequest {
  name?: string;
  description?: string;
  settings?: Partial<WorkspaceSettings>;
}

export interface WorkspaceStats {
  collectionsCount: number;
  requestsCount: number;
  environmentsCount: number;
  lastActivity: Date;
}

export interface WorkspaceExportData {
  workspace: Workspace;
  collections: any[];
  environments: any[];
  exportedAt: Date;
  version: string;
}

export interface WorkspaceImportResult {
  success: boolean;
  workspace?: Workspace;
  errors: string[];
  warnings: string[];
}

// Default workspace settings
export const DEFAULT_WORKSPACE_SETTINGS: WorkspaceSettings = {
  theme: 'auto',
  autoSave: true,
  requestTimeout: 30000,
  followRedirects: true,
  validateSSL: true,
  proxySettings: {
    enabled: false,
    host: '',
    port: 8080,
    bypassList: ['localhost', '127.0.0.1']
  }
};

// Default workspace
export const createDefaultWorkspace = (): Workspace => ({
  id: 'default',
  name: 'My Workspace',
  description: 'Default workspace for API testing',
  isDefault: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  settings: { ...DEFAULT_WORKSPACE_SETTINGS },
  collections: [],
  environments: []
});
