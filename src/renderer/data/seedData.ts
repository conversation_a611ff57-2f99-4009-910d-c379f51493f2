import { WorkspaceItem, WorkspaceData } from '../types/workspace';

export const generateSeedData = (): WorkspaceData => {
  const now = new Date();
  
  const items: WorkspaceItem[] = [
    // Sample API Collection
    {
      id: 'collection-1',
      name: 'Sample API Collection',
      type: 'collection',
      order: 0,
      expanded: true,
      description: 'A sample collection with various API endpoints',
      createdAt: now,
      updatedAt: now,
    },
    
    // Users folder
    {
      id: 'folder-1',
      name: 'Users',
      type: 'folder',
      parentId: 'collection-1',
      order: 0,
      expanded: true,
      description: 'User management endpoints',
      createdAt: now,
      updatedAt: now,
    },
    
    // User requests
    {
      id: 'request-1',
      name: 'Get All Users',
      type: 'request',
      parentId: 'folder-1',
      order: 0,
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/users',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      params: {
        'page': '1',
        'limit': '10'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-2',
      name: 'Get User by ID',
      type: 'request',
      parentId: 'folder-1',
      order: 1,
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/users/1',
      headers: {
        'Accept': 'application/json'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-3',
      name: 'Create User',
      type: 'request',
      parentId: 'folder-1',
      order: 2,
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/users',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'johndoe'
      }, null, 2),
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-4',
      name: 'Update User',
      type: 'request',
      parentId: 'folder-1',
      order: 3,
      method: 'PUT',
      url: 'https://jsonplaceholder.typicode.com/users/1',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'John Doe Updated',
        email: '<EMAIL>'
      }, null, 2),
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-5',
      name: 'Delete User',
      type: 'request',
      parentId: 'folder-1',
      order: 4,
      method: 'DELETE',
      url: 'https://jsonplaceholder.typicode.com/users/1',
      headers: {
        'Accept': 'application/json'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    // Posts folder
    {
      id: 'folder-2',
      name: 'Posts',
      type: 'folder',
      parentId: 'collection-1',
      order: 1,
      expanded: false,
      description: 'Blog post management endpoints',
      createdAt: now,
      updatedAt: now,
    },
    
    // Post requests
    {
      id: 'request-6',
      name: 'Get All Posts',
      type: 'request',
      parentId: 'folder-2',
      order: 0,
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: {
        'Accept': 'application/json'
      },
      params: {
        'userId': '1'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-7',
      name: 'Create Post',
      type: 'request',
      parentId: 'folder-2',
      order: 1,
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'Sample Post',
        body: 'This is a sample post content',
        userId: 1
      }, null, 2),
      createdAt: now,
      updatedAt: now,
    },
    
    // Comments folder (nested under Posts)
    {
      id: 'folder-3',
      name: 'Comments',
      type: 'folder',
      parentId: 'folder-2',
      order: 2,
      expanded: false,
      description: 'Comment management endpoints',
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-8',
      name: 'Get Post Comments',
      type: 'request',
      parentId: 'folder-3',
      order: 0,
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/1/comments',
      headers: {
        'Accept': 'application/json'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    // Authentication Collection
    {
      id: 'collection-2',
      name: 'Authentication APIs',
      type: 'collection',
      order: 1,
      expanded: false,
      description: 'Authentication and authorization endpoints',
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-9',
      name: 'Login',
      type: 'request',
      parentId: 'collection-2',
      order: 0,
      method: 'POST',
      url: 'https://api.example.com/auth/login',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      }, null, 2),
      createdAt: now,
      updatedAt: now,
    },
    
    {
      id: 'request-10',
      name: 'Refresh Token',
      type: 'request',
      parentId: 'collection-2',
      order: 1,
      method: 'POST',
      url: 'https://api.example.com/auth/refresh',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer {{refresh_token}}'
      },
      createdAt: now,
      updatedAt: now,
    },
    
    // Standalone request (not in any collection)
    {
      id: 'request-11',
      name: 'Health Check',
      type: 'request',
      order: 2,
      method: 'GET',
      url: 'https://api.example.com/health',
      headers: {
        'Accept': 'application/json'
      },
      createdAt: now,
      updatedAt: now,
    }
  ];
  
  return {
    id: 'workspace-1',
    name: 'My Workspace',
    items
  };
};
