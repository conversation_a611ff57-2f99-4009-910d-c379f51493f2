import React, { useState, useEffect } from 'react';
import { KeyValuePair } from '../types/request';

interface ParamsTableProps {
  params: Record<string, string> | KeyValuePair[];
  onChange: (params: KeyValuePair[]) => void;
}

const ParamsTable: React.FC<ParamsTableProps> = ({ params, onChange }) => {
  const [rows, setRows] = useState<KeyValuePair[]>([]);
  const [lastExternalParams, setLastExternalParams] = useState<Record<string, string> | KeyValuePair[]>([]);

  useEffect(() => {
    // Check if params changed from external source (different from what we last sent)
    const paramsChanged = JSON.stringify(params) !== JSON.stringify(lastExternalParams);

    if (paramsChanged) {
      let paramRows: KeyValuePair[];

      if (Array.isArray(params)) {
        // Already in KeyValuePair format
        paramRows = [...params];
      } else {
        // Convert from Record<string, string> format
        paramRows = Object.entries(params).map(([key, value]) => ({
          key,
          value,
          description: '',
          enabled: true
        }));
      }

      // Always have at least one empty row
      if (paramRows.length === 0) {
        paramRows.push({ key: '', value: '', description: '', enabled: false });
      }

      setRows(paramRows);
      setLastExternalParams(params);
    }
  }, [params, lastExternalParams]);

  const updateRow = (index: number, field: keyof KeyValuePair, value: string | boolean) => {
    const newRows = [...rows];
    newRows[index] = { ...newRows[index], [field]: value };

    // If this is the last row and user is typing, add a new empty row
    if (index === newRows.length - 1 && field === 'key' && value && typeof value === 'string') {
      newRows.push({ key: '', value: '', description: '', enabled: false });
    }

    setRows(newRows);

    // Filter out empty rows and pass the full KeyValuePair array
    const validParams = newRows.filter(row => row.key.trim() !== '');
    console.log('[ParamsTable] onChange called with validParams:', validParams);

    // Call onChange with KeyValuePair array
    onChange(validParams);

    // Use setTimeout to update lastExternalParams after the onChange has been processed
    setTimeout(() => {
      setLastExternalParams(validParams);
    }, 0);
  };

  const addRow = () => {
    setRows([...rows, { key: '', value: '', description: '', enabled: false }]);
  };

  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index);
    setRows(newRows);

    // Filter out empty rows and pass the full KeyValuePair array
    const validParams = newRows.filter(row => row.key.trim() !== '');
    onChange(validParams);

    // Update lastExternalParams after onChange
    setTimeout(() => {
      setLastExternalParams(validParams);
    }, 0);
  };

  return (
    <div className="params-table">
      <div className="table-wrapper">
        <table className="table">
          <thead>
            <tr>
              <th className="checkbox-col"></th>
              <th className="key-col">Key</th>
              <th className="value-col">Value</th>
              <th className="description-col">Description</th>
              <th className="actions-col"></th>
            </tr>
          </thead>
          <tbody>
            {rows.map((row, index) => (
              <tr key={index} className={!row.key && !row.value ? 'empty-row' : ''}>
                <td className="checkbox-col">
                  <input
                    type="checkbox"
                    checked={row.enabled}
                    onChange={(e) => updateRow(index, 'enabled', e.target.checked)}
                  />
                </td>
                <td className="key-col">
                  <input
                    type="text"
                    placeholder="Key"
                    value={row.key}
                    onChange={(e) => updateRow(index, 'key', e.target.value)}
                  />
                </td>
                <td className="value-col">
                  <input
                    type="text"
                    placeholder="Value"
                    value={row.value}
                    onChange={(e) => updateRow(index, 'value', e.target.value)}
                  />
                </td>
                <td className="description-col">
                  <input
                    type="text"
                    placeholder="Description"
                    value={row.description}
                    onChange={(e) => updateRow(index, 'description', e.target.value)}
                    onFocus={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                  />
                </td>
                <td className="actions-col">
                  <button
                    className="delete-btn"
                    onClick={() => deleteRow(index)}
                    disabled={rows.length === 1}
                    title="Delete parameter"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="3,6 5,6 21,6"/>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                    </svg>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <button className="add-row-btn" onClick={addRow}>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        Add Parameter
      </button>
    </div>
  );
};

export default ParamsTable;