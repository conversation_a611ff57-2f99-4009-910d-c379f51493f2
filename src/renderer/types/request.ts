/**
 * Request-related type definitions
 */

// Key-value pair with metadata for headers and params
export interface KeyValuePair {
  key: string;
  value: string;
  description?: string;
  enabled?: boolean;
}

export interface Request {
  id: string; // This will be the hierarchy item ID for tabs
  entityId?: number; // The actual database entity ID (for requests/collections/folders)
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string> | KeyValuePair[];
  params?: Record<string, string> | KeyValuePair[];
  body?: string;
  hasUnsavedChanges?: boolean;
}

export interface RequestData {
  id?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string> | KeyValuePair[];
  body?: any;
  workspaceId: number;
}

export interface RequestResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: any;
  responseTime: number;
  size: number;
}

export interface ExecuteRequestResult {
  success: boolean;
  response?: RequestResponse;
  error?: string;
}

export interface RequestAuth {
  type: 'none' | 'bearer' | 'basic' | 'api-key';
  token?: string;
  username?: string;
  password?: string;
  key?: string;
  value?: string;
}

export interface RequestMetadata {
  description?: string;
  auth?: RequestAuth;
  preScript?: string;
  testScript?: string;
}

export interface FullRequestData extends RequestData, RequestMetadata {
  entityId?: number;
  params?: Record<string, string> | KeyValuePair[];
  headers: Record<string, string> | KeyValuePair[];
  createdAt?: Date;
  updatedAt?: Date;
}
