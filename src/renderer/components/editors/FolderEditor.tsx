import React, { useState, useEffect } from 'react';
import { EditorDataLoader } from '../../utils/JsonUtils';
import { FullFolderData } from '../../types/folder';

interface FolderEditorProps {
  folderId: string;
  folderName: string;
  onSave: (data: FullFolderData) => void;
  onClose: () => void;
  onChange?: () => void; // Callback when data changes (to mark tab as dirty)
  readOnly?: boolean;
  initialData?: FullFolderData; // Pre-loaded data to avoid redundant API calls
}

const FolderEditor: React.FC<FolderEditorProps> = ({
  folderId,
  folderName,
  onSave,
  onClose,
  onChange,
  readOnly = false,
  initialData
}) => {
  const [folderData, setFolderData] = useState<FullFolderData>({
    id: folderId,
    name: folderName,
    description: '',
    variables: {},
    auth: { type: 'none' },
    preRequestScript: '',
    testScript: ''
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'auth' | 'variables' | 'scripts'>('overview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    // Load folder data from database or use initial data
    const loadData = async () => {
      try {
        // Use initial data if provided, otherwise load from API
        if (initialData) {
          console.log('[FolderEditor] Using pre-loaded initial data:', initialData);
          // Ensure the data has the expected structure
          const normalizedData = {
            ...initialData,
            variables: initialData.variables || {},
            auth: initialData.auth || { type: 'none' },
            preRequestScript: initialData.preRequestScript || '',
            testScript: initialData.testScript || ''
          };
          setFolderData(normalizedData);
          setHasUnsavedChanges(false);
        } else {
          console.log('[FolderEditor] Loading data from API for folder:', folderId);
          const data = await EditorDataLoader.loadFolderData(folderId);
          setFolderData(data);
          setHasUnsavedChanges(false); // Mark as saved since we just loaded from database
        }
      } catch (error) {
        console.error('Failed to load folder data:', error);
        // Set default data if loading fails
        setFolderData({
          id: folderId,
          name: folderName,
          description: '',
          variables: {},
          auth: { type: 'none' },
          preRequestScript: '',
          testScript: ''
        });
        setHasUnsavedChanges(false);
      }
    };

    loadData();
  }, [folderId, folderName, initialData]);

  // Listen for keyboard shortcut save event
  useEffect(() => {
    const handleSaveShortcut = () => {
      if (hasUnsavedChanges) {
        handleSave();
      }
    };

    window.addEventListener('editor-save-shortcut', handleSaveShortcut);
    return () => window.removeEventListener('editor-save-shortcut', handleSaveShortcut);
  }, [hasUnsavedChanges, folderData]);

  const handleInputChange = (field: keyof FullFolderData, value: any) => {
    setFolderData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleVariableChange = (key: string, value: string) => {
    setFolderData(prev => ({
      ...prev,
      variables: { ...prev.variables || {}, [key]: value }
    }));
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const addVariable = () => {
    const key = `variable_${Object.keys(folderData.variables || {}).length + 1}`;
    handleVariableChange(key, '');
  };

  const removeVariable = (key: string) => {
    setFolderData(prev => {
      const newVariables = { ...prev.variables || {} };
      delete newVariables[key];
      return { ...prev, variables: newVariables };
    });
    setHasUnsavedChanges(true);
    if (onChange) {
      onChange();
    }
  };

  const handleSave = () => {
    onSave(folderData);
    setHasUnsavedChanges(false);
  };

  return (
    <div className="folder-editor">
      <div className="editor-header">
        <div className="editor-title">
          <span className="editor-icon">📁</span>
          <h2>{folderData.name}</h2>
          {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
        </div>
        <div className="editor-actions">
          {!readOnly && (
            <button 
              className="btn btn-primary" 
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              Save
            </button>
          )}
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>

      <div className="editor-tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab ${activeTab === 'auth' ? 'active' : ''}`}
          onClick={() => setActiveTab('auth')}
        >
          Authorization
        </button>
        <button
          className={`tab ${activeTab === 'variables' ? 'active' : ''}`}
          onClick={() => setActiveTab('variables')}
        >
          Variables ({Object.keys(folderData.variables || {}).length})
        </button>
        <button
          className={`tab ${activeTab === 'scripts' ? 'active' : ''}`}
          onClick={() => setActiveTab('scripts')}
        >
          Scripts
        </button>
      </div>

      <div className="editor-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="form-group">
              <label htmlFor="folder-name">Folder Name</label>
              <input
                id="folder-name"
                type="text"
                value={folderData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                readOnly={readOnly}
                className="form-input"
              />
            </div>
            <div className="form-group">
              <label htmlFor="folder-description">Description</label>
              <textarea
                id="folder-description"
                value={folderData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                readOnly={readOnly}
                className="form-textarea"
                rows={4}
                placeholder="Describe what this folder contains..."
              />
            </div>
          </div>
        )}

        {activeTab === 'auth' && (
          <div className="auth-tab">
            <div className="form-group">
              <label htmlFor="auth-type">Authorization Type</label>
              <select
                id="auth-type"
                value={folderData.auth?.type || 'none'}
                onChange={(e) => handleInputChange('auth', { 
                  ...folderData.auth, 
                  type: e.target.value as any 
                })}
                disabled={readOnly}
                className="form-select"
              >
                <option value="none">No Auth</option>
                <option value="bearer">Bearer Token</option>
                <option value="basic">Basic Auth</option>
                <option value="api-key">API Key</option>
              </select>
            </div>

            {folderData.auth?.type === 'bearer' && (
              <div className="form-group">
                <label htmlFor="bearer-token">Token</label>
                <input
                  id="bearer-token"
                  type="text"
                  value={folderData.auth.token || ''}
                  onChange={(e) => handleInputChange('auth', { 
                    ...folderData.auth, 
                    token: e.target.value 
                  })}
                  readOnly={readOnly}
                  className="form-input"
                  placeholder="Enter bearer token or variable like {{token}}"
                />
              </div>
            )}

            {folderData.auth?.type === 'basic' && (
              <>
                <div className="form-group">
                  <label htmlFor="basic-username">Username</label>
                  <input
                    id="basic-username"
                    type="text"
                    value={folderData.auth.username || ''}
                    onChange={(e) => handleInputChange('auth', { 
                      ...folderData.auth, 
                      username: e.target.value 
                    })}
                    readOnly={readOnly}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="basic-password">Password</label>
                  <input
                    id="basic-password"
                    type="password"
                    value={folderData.auth.password || ''}
                    onChange={(e) => handleInputChange('auth', { 
                      ...folderData.auth, 
                      password: e.target.value 
                    })}
                    readOnly={readOnly}
                    className="form-input"
                  />
                </div>
              </>
            )}

            {folderData.auth?.type === 'api-key' && (
              <>
                <div className="form-group">
                  <label htmlFor="api-key">Key</label>
                  <input
                    id="api-key"
                    type="text"
                    value={folderData.auth.key || ''}
                    onChange={(e) => handleInputChange('auth', { 
                      ...folderData.auth, 
                      key: e.target.value 
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="API Key name (e.g., X-API-Key)"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="api-value">Value</label>
                  <input
                    id="api-value"
                    type="text"
                    value={folderData.auth.value || ''}
                    onChange={(e) => handleInputChange('auth', { 
                      ...folderData.auth, 
                      value: e.target.value 
                    })}
                    readOnly={readOnly}
                    className="form-input"
                    placeholder="API Key value"
                  />
                </div>
              </>
            )}
          </div>
        )}

        {activeTab === 'variables' && (
          <div className="variables-tab">
            <div className="variables-header">
              <h3>Folder Variables</h3>
              {!readOnly && (
                <button className="btn btn-sm btn-primary" onClick={addVariable}>
                  Add Variable
                </button>
              )}
            </div>
            <div className="variables-list">
              {Object.entries(folderData.variables || {}).map(([key, value]) => (
                <div key={key} className="variable-item">
                  <input
                    type="text"
                    value={key}
                    onChange={(e) => {
                      const newKey = e.target.value;
                      const newVariables = { ...folderData.variables || {} };
                      delete newVariables[key];
                      newVariables[newKey] = value;
                      handleInputChange('variables', newVariables);
                    }}
                    readOnly={readOnly}
                    className="form-input variable-key"
                    placeholder="Variable name"
                  />
                  <input
                    type="text"
                    value={value}
                    onChange={(e) => handleVariableChange(key, e.target.value)}
                    readOnly={readOnly}
                    className="form-input variable-value"
                    placeholder="Variable value"
                  />
                  {!readOnly && (
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => removeVariable(key)}
                    >
                      ✕
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'scripts' && (
          <div className="scripts-tab">
            <div className="script-section">
              <h3>Pre-request Script</h3>
              <textarea
                value={folderData.preRequestScript || ''}
                onChange={(e) => handleInputChange('preRequestScript', e.target.value)}
                readOnly={readOnly}
                className="form-textarea script-editor"
                rows={8}
                placeholder="// This script will run before every request in this folder"
              />
            </div>
            <div className="script-section">
              <h3>Test Script</h3>
              <textarea
                value={folderData.testScript || ''}
                onChange={(e) => handleInputChange('testScript', e.target.value)}
                readOnly={readOnly}
                className="form-textarea script-editor"
                rows={8}
                placeholder="// This script will run after every request in this folder"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FolderEditor;
