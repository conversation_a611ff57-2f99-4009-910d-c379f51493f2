import React, { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
// Removed old UI state hooks - now using AppContext
import { useAppContext } from '../contexts/AppContext';
import { TabItem } from './ItemTabs';
import { TabManagementService } from '../services/TabManagementService';

import WorkspacePanel from './WorkspacePanel';
import LeftSidebar from './LeftSidebar';
import MainPanel from './MainPanel';
import ResizableSplitter from './ResizableSplitter';
import MenuHandler from './MenuHandler';
import KeyboardShortcuts from './KeyboardShortcuts';
import QuickSearchBox from './QuickSearchBox';
import Settings from './Settings';
import Chat from './Chat';
import ImportExportDialog from './ImportExportDialog';
import WorkspaceCreateDialog from './workspace/WorkspaceCreateDialog';
import RequestSaveDialog from './dialogs/RequestSaveDialog';
import { WorkspaceItem } from '../types/workspace';
import { SearchResult } from '../services/SearchService';
import { ImportResult } from '../types/import-export';
import { Workspace } from '../types/workspace-management';
import { Request } from '../types/request';
import WorkspaceDataManager from '../services/WorkspaceDataManager';
import { parseCurlCommand, isCurlCommand } from '../utils/curlParser';
import './App.css';

const App: React.FC = () => {
  // Use AppContext for global state management
  const {
    state,
    addTab,
    addTabItem,
    removeTab,
    updateTabItem,
    setActiveTab,
    closeAllTabs,
    setLeftPanelWidth,
    setWorkspaceSection,
    setChatOpen,
    setActiveEnvironment,
    setQuickSearchOpen,
    setImportDialogOpen,
    setExportDialogOpen,
    setWorkspaceManagementOpen,
    setCreateWorkspaceOpen,
    setSaveDialogOpen,
    setRequestToSave,
    setExportCollection,
    showToast  } = useAppContext();

  // Extract commonly used state
  const {
    openTabs,
    activeTab,
    leftPanelWidth,
    workspaceSection,
    isChatOpen,
    activeEnvironment,
    isQuickSearchOpen,
    isImportDialogOpen,
    isExportDialogOpen,
    isCreateWorkspaceOpen,
    isSaveDialogOpen,
    requestToSave,
    exportCollection,
    toastMessage
  } = state;

  // Local state for components not yet migrated to context
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);

  // Handle Ctrl/Cmd+V for curl import
  useEffect(() => {
    const handleKeyDown = async (event: KeyboardEvent) => {
      const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0;
      const isCtrlOrCmd = isMac ? event.metaKey : event.ctrlKey;

      // Ctrl/Cmd + V
      if (isCtrlOrCmd && event.key === 'v') {
        // Don't interfere with normal paste operations in input fields
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement && (
          activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.contentEditable === 'true'
        )) {
          return;
        }

        try {
          const clipboardText = await navigator.clipboard.readText();

          if (isCurlCommand(clipboardText)) {
            event.preventDefault();
            const parsedRequest = parseCurlCommand(clipboardText);

            if (parsedRequest) {
              // Create a new unsaved request from the parsed curl
              const newRequest: Request = {
                id: `curl-import-${Date.now()}`,
                name: parsedRequest.name,
                method: parsedRequest.method,
                url: parsedRequest.url,
                headers: parsedRequest.headers,
                params: {},
                body: parsedRequest.body || '',
                hasUnsavedChanges: true
              };

              // Add to open tabs and make it active
              addTab(newRequest);

              // Show success notification
              showToast(`Imported request: ${parsedRequest.name}`);

              console.log('Created request from curl:', newRequest);
            }
          }
        } catch (error) {
          console.error('Failed to read clipboard:', error);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Environment state
  const [environments, setEnvironments] = useState<any[]>([]);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<number>(1);

  // Load environments from database
  const loadEnvironments = async () => {
    try {
      const workspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);
      setCurrentWorkspaceId(workspaceId);

      const [envs, activeEnv] = await Promise.all([
        window.electronAPI.environment.getAll(workspaceId),
        window.electronAPI.environment.getActive(workspaceId)
      ]);

      // Convert to format expected by EnvironmentSelector
      const formattedEnvs = envs.map(env => ({
        id: env.id.toString(),
        name: env.name,
        variables: env.variables.reduce((acc, variable) => {
          if (variable.enabled) {
            acc[variable.key] = variable.value;
          }
          return acc;
        }, {} as Record<string, string>)
      }));

      setEnvironments(formattedEnvs);

      if (activeEnv) {
        setActiveEnvironment(activeEnv.id.toString());
      }
    } catch (error) {
      console.error('Failed to load environments:', error);
    }
  };

  // Load environments on mount and when workspace changes
  useEffect(() => {
    loadEnvironments();

    // Listen for environment changes
    const handleEnvironmentChange = () => {
      loadEnvironments();
    };

    window.addEventListener('environment-created', handleEnvironmentChange);
    window.addEventListener('environment-updated', handleEnvironmentChange);
    window.addEventListener('environment-deleted', handleEnvironmentChange);
    window.addEventListener('environment-activated', handleEnvironmentChange);

    return () => {
      window.removeEventListener('environment-created', handleEnvironmentChange);
      window.removeEventListener('environment-updated', handleEnvironmentChange);
      window.removeEventListener('environment-deleted', handleEnvironmentChange);
      window.removeEventListener('environment-activated', handleEnvironmentChange);
    };
  }, []);

  // Workspace items state for quick search - loaded from database
  const [workspaceItems, setWorkspaceItems] = useState<WorkspaceItem[]>([]);

  // Load workspace items from database
  const loadWorkspaceItems = async () => {
    try {
      const activeWorkspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);
      const hierarchy = await window.electronAPI.hierarchy.getItems(activeWorkspaceId);

      // Convert hierarchy items to WorkspaceItem format
      const convertToWorkspaceItems = (items: any[]): WorkspaceItem[] => {
        return items.map(item => ({
          id: item.id.toString(),
          name: item.collection?.name || item.folder?.name || item.request?.name || item.example?.name || 'Untitled',
          type: item.type.toLowerCase() as 'collection' | 'folder' | 'request' | 'example',
          parentId: item.parentId?.toString(),
          order: item.order,
          method: item.request?.method,
          url: item.request?.url,
          description: item.collection?.description || item.folder?.description,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt),
          children: item.children ? convertToWorkspaceItems(item.children) : []
        }));
      };

      setWorkspaceItems(convertToWorkspaceItems(hierarchy));
    } catch (error) {
      console.error('Failed to load workspace items:', error);
      setWorkspaceItems([]);
    }
  };

  // Initialize app and load tabs from localStorage on mount
  React.useEffect(() => {
    // Initialize workspace data management
    WorkspaceDataManager.initialize();

    // Load workspace items for quick search
    loadWorkspaceItems();

    // UI state (including tabs) is automatically loaded by useUIState hook
  }, []);

  // Tab state is automatically persisted by UI state manager

  // Listen for tab updates from sidebar
  // Tab updates are now handled by UI state manager



  // Handle keyboard shortcuts for quick search
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + O to open quick search
      if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        setQuickSearchOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const openRequest = (request: Partial<Request> & { hierarchyId?: string; entityId?: number }) => {
    // Use hierarchy ID for tab identification, or create a new temporary ID
    let tabId = request.hierarchyId || request.id;
    if (!tabId) {
      // No ID provided, create a new temporary ID
      tabId = `request-${Date.now()}`;
    }

    const newRequest: Request = {
      id: request.entityId?.toString(), // Use hierarchy ID for tab identification
      name: request.name || 'Untitled Request',
      method: request.method || 'GET',
      url: request.url || '',
      headers: request.headers || {},
      params: request.params || {},
      body: request.body || '',
      hasUnsavedChanges: false // Existing requests start as saved
    };

    addTab(newRequest);
  };

  const closeTab = (tabId: string) => {
    removeTab(tabId);
  };

  const forceCloseAllTabs = () => {
    // Force close all tabs without checking for unsaved changes
    closeAllTabs();
  };

  // Initialize theme on app startup
  React.useEffect(() => {
    const initializeTheme = async () => {
      try {
        const theme = await window.electronAPI.settings.getTheme();
        applyTheme(theme);
      } catch (error) {
        console.error('Failed to load theme:', error);
        // Default to auto theme
        applyTheme('auto');
      }
    };

    initializeTheme();
  }, []);

  // Apply theme to document
  const applyTheme = (theme: 'light' | 'dark' | 'auto') => {
    if (theme === 'auto') {
      // Use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');

      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
      };

      // Remove previous listener if exists
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
      // Add new listener
      mediaQuery.addEventListener('change', handleSystemThemeChange);
    } else {
      document.documentElement.setAttribute('data-theme', theme);

      // Remove system theme listener when not in auto mode
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.removeEventListener('change', () => {});
    }
  };

  // Listen for workspace switching to close all tabs and reload workspace items
  React.useEffect(() => {
    const handleWorkspaceSwitched = () => {
      console.log('Workspace switched, closing all tabs and reloading workspace items');
      // Close all tabs when switching workspaces
      closeAllTabs();
      // Reload workspace items for the new workspace
      loadWorkspaceItems();
    };

    const handleItemSavedEvent = () => {
      // Reload workspace items when new items are saved
      loadWorkspaceItems();
    };

    const handleOpenRequestFromHistory = (event: CustomEvent) => {
      const request = event.detail;
      openRequest(request);
    };

    const handleThemeChanged = (event: CustomEvent) => {
      const theme = event.detail;
      applyTheme(theme);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      // AI Chat shortcut: Ctrl/Cmd + Shift + C
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        handleOpenChat();
      }

      // Settings shortcut: Ctrl/Cmd + ,
      if ((event.ctrlKey || event.metaKey) && event.key === ',') {
        event.preventDefault();
        handleOpenSettings();
      }
    };

    window.addEventListener('workspace-switched', handleWorkspaceSwitched as EventListener);
    window.addEventListener('item-saved', handleItemSavedEvent as EventListener);
    window.addEventListener('open-request-from-history', handleOpenRequestFromHistory as EventListener);
    window.addEventListener('theme-changed', handleThemeChanged as EventListener);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('workspace-switched', handleWorkspaceSwitched as EventListener);
      window.removeEventListener('item-saved', handleItemSavedEvent as EventListener);
      window.removeEventListener('open-request-from-history', handleOpenRequestFromHistory as EventListener);
      window.removeEventListener('theme-changed', handleThemeChanged as EventListener);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const updateRequest = (tabId: string, updates: Partial<Request>) => {
    console.log(`[App] updateRequest called for tab: ${tabId}`, updates);

    // Find the tab item
    const tabItem = openTabs.find(tab => tab.id === tabId);
    if (!tabItem || tabItem.type !== 'request') {
      console.warn(`[App] Tab ${tabId} not found or not a request`);
      return;
    }

    // Use TabManagementService to update the tab data
    const updatedTabItem = TabManagementService.updateTabItemData(tabItem, updates);

    // Override hasUnsavedChanges if explicitly provided
    if (updates.hasUnsavedChanges !== undefined) {
      updatedTabItem.hasUnsavedChanges = updates.hasUnsavedChanges;
    }

    console.log(`[App] Updated tab item:`, updatedTabItem);
    updateTabItem(tabId, updatedTabItem);

    // Also update the workspace data in localStorage
    const savedWorkspace = localStorage.getItem('apicool-workspace');
    if (savedWorkspace) {
      try {
        const workspace = JSON.parse(savedWorkspace);
        workspace.items = workspace.items.map((item: any) =>
          item.id === tabId ? {
            ...item,
            ...updates,
            updatedAt: new Date().toISOString()
          } : item
        );
        localStorage.setItem('apicool-workspace', JSON.stringify(workspace));

        // Trigger a custom event to notify sidebar of changes
        window.dispatchEvent(new CustomEvent('workspace-updated', { detail: workspace }));
      } catch (error) {
        console.error('Failed to update workspace:', error);
      }
    }
  };



  const handleItemSaved = (type: 'collection' | 'folder' | 'request', item: any) => {
    // Emit a custom event with the new item information for the tree to add it
    // This allows the tree to add the item without a full refresh, preserving expansion state
    window.dispatchEvent(new CustomEvent('item-saved', {
      detail: {
        type,
        item,
        workspaceId: parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10)
      }
    }));

    showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} "${item.name}" saved successfully`);
  };

  const handleSaveDialogSave = async (parentId: string | null, name: string) => {
    if (!requestToSave || !parentId) return;

    try {
      if (window.electronAPI) {
        // Get current workspace ID
        const activeWorkspaceId = parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10);

        const newRequest = await window.electronAPI.workspace.createRequest({
          name: name,
          method: requestToSave.method,
          url: requestToSave.url,
          headers: requestToSave.headers,
          body: requestToSave.body,
          parentId: parseInt(parentId),
          workspaceId: activeWorkspaceId
        });

        // The response contains both request entity and hierarchy item
        const requestEntity = newRequest.request || newRequest;
        const hierarchyItem = newRequest.item || newRequest;

        // Remove the old temporary tab
        removeTab(requestToSave.id);

        // Add a new tab with the hierarchy ID and entity ID
        const savedRequest: Request = {
          ...requestToSave,
          id: hierarchyItem.id.toString(), // Use hierarchy item ID for tab
          entityId: requestEntity.id, // Store entity ID for database operations
          name: name,
          hasUnsavedChanges: false // Explicitly mark as saved
        };

        addTab(savedRequest);
        setActiveTab(hierarchyItem.id.toString());

        showToast(`Request "${name}" saved successfully`);

        // Emit event for tree to add the new request without full refresh
        window.dispatchEvent(new CustomEvent('item-saved', {
          detail: {
            type: 'request',
            item: {
              id: hierarchyItem.id, // Use hierarchy item ID
              entityId: requestEntity.id, // Include entity ID
              name: requestEntity.name,
              method: requestEntity.method,
              url: requestEntity.url,
              description: requestEntity.description,
              order: hierarchyItem.order,
              createdAt: requestEntity.createdAt,
              updatedAt: requestEntity.updatedAt
            },
            parentId: parseInt(parentId),
            workspaceId: activeWorkspaceId
          }
        }));
      }
    } catch (error) {
      console.error('Failed to save new request:', error);
      showToast('Failed to save request');
    } finally {
      setSaveDialogOpen(false);
      setRequestToSave(null);
    }
  };

  const newTab = () => {
    const newRequest: Request = {
      id: `request-${Date.now()}`,
      name: 'Untitled Request',
      method: 'GET',
      url: '',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      params: {},
      body: ''
    };
    addTab(newRequest);
  };

  // Menu handlers
  const handleMenuNewRequest = () => {
    newTab();
  };

  const handleMenuNewCollection = () => {
    // TODO: Implement new collection creation
    console.log('New collection from menu');
  };

  const handleMenuImportPostman = () => {
    setImportDialogOpen(true);
  };

  const handleMenuImportOpenAPI = () => {
    setImportDialogOpen(true);
  };

  const handleMenuExportCollection = () => {
    // Export the first collection from workspace data
    if (workspaceItems.length > 0) {
      setExportCollection(workspaceItems[0]);
      setExportDialogOpen(true);
    }
  };

  const handleMenuSettings = () => {
    handleOpenSettings();
  };

  const handleMenuViewCollections = () => {
    setWorkspaceSection('collections');
  };

  const handleMenuViewEnvironments = () => {
    setWorkspaceSection('environments');
  };

  const handleMenuViewHistory = () => {
    setWorkspaceSection('history');
  };

  const handleMenuAbout = () => {
    // TODO: Implement about dialog
    console.log('Show about dialog');
  };

  const handleMenuDocumentation = () => {
    // TODO: Open documentation
    console.log('Open documentation');
  };

  const handleMenuShortcuts = () => {
    // TODO: Show keyboard shortcuts
    console.log('Show keyboard shortcuts');
  };

  // Editor tab management
  const openEditorTab = (type: 'folder' | 'collection' | 'example' | 'environment' | 'history', entityId: string, name: string, hierarchyId?: string, entityData?: any) => {
    // Create a proper TabItem for editors
    // Use hierarchy ID as tab ID if provided, otherwise use entity ID
    const tabId = hierarchyId || entityId;

    const editorTabItem: TabItem = {
      id: tabId, // Use hierarchy ID for tab identification
      type: type,
      data: entityData ? {
        // Use pre-loaded entity data if available
        ...entityData,
        id: entityId, // Ensure entity ID is set
        hierarchyId: hierarchyId, // Store hierarchy ID if available
        name: name // Use the display name from hierarchy
      } : {
        // Fallback to default data if no entity data provided
        id: entityId, // Store entity ID in data for database operations
        hierarchyId: hierarchyId, // Store hierarchy ID if available
        name: name,
        // Add type-specific default data
        ...(type === 'collection' && {
          description: '',
          variables: {},
          tags: [],
          version: '1.0.0'
        }),
        ...(type === 'folder' && {
          description: ''
        }),
        ...(type === 'example' && {
          description: '',
          method: 'GET',
          url: '',
          headers: {},
          body: ''
        }),
        ...(type === 'environment' && {
          variables: {}
        })
      },
      hasUnsavedChanges: false
    };

    // Check if tab is already open
    const existingTab = openTabs.find(tab => tab.id === editorTabItem.id);
    if (existingTab) {
      setActiveTab(editorTabItem.id);
      return;
    }

    // Add new editor tab using addTabItem for proper type handling
    addTabItem(editorTabItem);
  };

  // Settings and Chat handlers
  const handleOpenSettings = () => {
    setIsSettingsOpen(true);
  };

  const handleOpenChat = () => {
    setChatOpen(true);
  };

  const handleRequestGenerated = (request: any) => {
    // Convert AI-generated request to our format
    const newRequest: Request = {
      id: `generated-${Date.now()}`,
      name: request.name || 'Generated Request',
      method: request.method || 'GET',
      url: request.url || '',
      headers: request.headers || {},
      params: request.params || {},
      body: request.body || '',
      hasUnsavedChanges: false
    };

    openRequest(newRequest);
    setChatOpen(false); // Close chat after applying request
  };

  // QuickSearchBox handlers
  const handleQuickSearchItemSelect = (item: any) => {
    if (item.type === 'command') {
      // Handle command actions
      switch (item.action) {
        case 'create-request':
          newTab();
          break;
        case 'create-folder':
          // TODO: Implement folder creation
          console.log('Create new folder');
          break;
        case 'create-collection':
          handleMenuNewCollection();
          break;
        case 'launch-ai-chat':
          handleOpenChat();
          break;
        case 'run-collection':
          // TODO: Implement collection runner
          console.log('Run collection');
          break;
        case 'import-postman':
          handleMenuImportPostman();
          break;
        case 'export-collection':
          handleMenuExportCollection();
          break;
        case 'open-settings':
          handleMenuSettings();
          break;
        default:
          console.log('Unknown command:', item.action);
      }
    } else {
      // Handle SearchResult selection (request, folder, collection, example)
      const searchResult = item as SearchResult;

      if (searchResult.type === 'request') {
        // Open the request in a new tab
        openRequest({
          id: searchResult.id,
          name: searchResult.name,
          method: searchResult.method || 'GET',
          url: searchResult.url || '',
          headers: {},
          params: {},
          body: ''
        });
      } else if (searchResult.type === 'folder') {
        // Open folder editor in a new tab
        openEditorTab('folder', searchResult.id, searchResult.name);
      } else if (searchResult.type === 'collection') {
        // Open collection editor in a new tab
        openEditorTab('collection', searchResult.id, searchResult.name);
      } else if (searchResult.type === 'example') {
        // Open example editor in a new tab
        openEditorTab('example', searchResult.id, searchResult.name);
      }
    }
  };

  // Import/Export handlers
  const handleImportComplete = (results: ImportResult[]) => {
    console.log('Import completed:', results);
    // TODO: Add imported collections to workspace
    // For now, just log the results
    const successfulImports = results.filter(r => r.success);
    if (successfulImports.length > 0) {
      console.log(`Successfully imported ${successfulImports.length} collection(s)`);
    }
  };

  const handleExportComplete = (success: boolean) => {
    console.log('Export completed:', success);
    if (success) {
      console.log('Collection exported successfully');
    }
  };

  // Workspace management handlers
  const handleCreateWorkspace = () => {
    setCreateWorkspaceOpen(true);
  };

  const handleManageWorkspaces = () => {
    setWorkspaceManagementOpen(true);
  };

  const handleWorkspacePanelImport = () => {
    setImportDialogOpen(true);
  };

  const handleWorkspaceCreated = (workspace: Workspace) => {
    console.log('Workspace created:', workspace.name);
    // WorkspaceManager automatically switches to the new workspace
    // WorkspaceDataManager will create empty data and trigger UI updates
    // Close the create dialog
    setCreateWorkspaceOpen(false);
  };

  const activeRequest = openTabs.find(tab => tab.id === activeTab);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="app">
        <MenuHandler
          onNewRequest={handleMenuNewRequest}
          onNewCollection={handleMenuNewCollection}
          onImportPostman={handleMenuImportPostman}
          onImportOpenAPI={handleMenuImportOpenAPI}
          onExportCollection={handleMenuExportCollection}
          onSettings={handleMenuSettings}
          onViewCollections={handleMenuViewCollections}
          onViewEnvironments={handleMenuViewEnvironments}
          onViewHistory={handleMenuViewHistory}
          onAbout={handleMenuAbout}
          onDocumentation={handleMenuDocumentation}
          onShortcuts={handleMenuShortcuts}
        />

        <div className="app-header">
          <div className="header-left">
            <h1 className="app-title">ApiCool</h1>
          </div>

          <div className="header-right">
            <div className="header-actions">
              <button
                className="header-action-btn"
                onClick={handleOpenChat}
                title="AI Chat (Ctrl+Shift+C)"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  <path d="M8 9h8"/>
                  <path d="M8 13h6"/>
                </svg>
              </button>

              <button
                className="header-action-btn"
                onClick={handleOpenSettings}
                title="Settings (Ctrl+,)"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M12 1v6m0 6v6"/>
                  <path d="M1 12h6m6 0h6"/>
                  <path d="M4.22 4.22l4.24 4.24m5.66 5.66l4.24 4.24"/>
                  <path d="M19.78 4.22l-4.24 4.24m-5.66 5.66l-4.24 4.24"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="app-content resizable-layout">
          <LeftSidebar
            activeSection={workspaceSection}
            onSectionChange={setWorkspaceSection}
          />

          <div
            className="resizable-panel"
            style={{ width: leftPanelWidth }}
          >
            <WorkspacePanel
              onOpenRequest={openRequest}
              onOpenEditor={openEditorTab}
              activeSection={workspaceSection}
              onImportClick={handleWorkspacePanelImport}
              onCreateWorkspace={handleCreateWorkspace}
              onManageWorkspaces={handleManageWorkspaces}
            />
          </div>

          <ResizableSplitter
            direction="horizontal"
            onResize={setLeftPanelWidth}
          />

          <div className="resizable-panel" style={{ flex: 1 }}>
            <MainPanel
              openTabs={openTabs}
              activeTab={activeTab}
              activeRequest={activeRequest}
              onTabChange={setActiveTab}
              onTabClose={closeTab}
              onForceCloseAll={forceCloseAllTabs}
              onNewTab={newTab}
              onUpdateRequest={updateRequest}
              onUpdateTabItem={updateTabItem}
              onItemSaved={handleItemSaved}
              environments={environments}
              activeEnvironment={activeEnvironment}
              onEnvironmentChange={async (environmentId: string) => {
                setActiveEnvironment(environmentId);

                // If an environment is selected, set it as active in the database
                if (environmentId && currentWorkspaceId) {
                  try {
                    await window.electronAPI.environment.setActive(parseInt(environmentId), currentWorkspaceId);
                    // Reload environments to sync state
                    loadEnvironments();
                  } catch (error) {
                    console.error('Failed to set active environment:', error);
                  }
                }
              }}
            />
          </div>

          {isChatOpen && (
            <>
              <ResizableSplitter
                direction="horizontal"
                onResize={() => {}}
              />

              <div className="resizable-panel chat-panel" style={{ width: '400px', minWidth: '300px', maxWidth: '600px' }}>
                <Chat
                  workspaceId={parseInt(localStorage.getItem('apicool-active-workspace') || '1', 10)}
                  onRequestGenerated={handleRequestGenerated}
                  onClose={() => setChatOpen(false)}
                  onShowToast={(message) => showToast(message, 4000)}
                />
              </div>
            </>
          )}
        </div>

        <KeyboardShortcuts />

        <QuickSearchBox
          isOpen={isQuickSearchOpen}
          onClose={() => setQuickSearchOpen(false)}
          workspaceItems={workspaceItems}
          onItemSelect={handleQuickSearchItemSelect}
        />

        <ImportExportDialog
          isOpen={isImportDialogOpen}
          mode="import"
          onClose={() => setImportDialogOpen(false)}
          onImportComplete={handleImportComplete}
          onExportComplete={handleExportComplete}
        />

        <ImportExportDialog
          isOpen={isExportDialogOpen}
          mode="export"
          collection={exportCollection}
          onClose={() => setExportDialogOpen(false)}
          onImportComplete={handleImportComplete}
          onExportComplete={handleExportComplete}
        />

        <WorkspaceCreateDialog
          isOpen={isCreateWorkspaceOpen}
          onClose={() => setCreateWorkspaceOpen(false)}
          onWorkspaceCreated={handleWorkspaceCreated}
        />

        <RequestSaveDialog
          isOpen={isSaveDialogOpen}
          requestName={requestToSave?.name || ''}
          onClose={() => {
            setSaveDialogOpen(false);
            setRequestToSave(null);
          }}
          onSave={handleSaveDialogSave}
        />

        {isSettingsOpen && (
          <Settings onClose={() => setIsSettingsOpen(false)} />
        )}

        {/* Toast Notification */}
        {toastMessage && (
          <div className="toast-notification">
            {toastMessage}
          </div>
        )}

        {/* Bottom border to connect all borders */}
        <div className="app-bottom-border"></div>
      </div>
    </DndProvider>
  );
};

export default App;
