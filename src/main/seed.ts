import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Create or get default workspace
    let workspace = await prisma.workspace.findFirst({
      where: { name: 'My Workspace' }
    });

    if (!workspace) {
      workspace = await prisma.workspace.create({
        data: {
          name: 'My Workspace',
          description: 'Default workspace for API testing'
        }
      });
      console.log('✅ Created default workspace:', workspace.name);
    } else {
      console.log('✅ Using existing workspace:', workspace.name);
    }



    // Create a sample collection
    const collection = await prisma.collection.create({
      data: {
        name: 'Sample API Collection',
        description: 'A collection of sample API requests',
        variables: {
          base_url: 'https://jsonplaceholder.typicode.com',
          api_version: 'v1',
          timeout: '5000'
        },
        workspaceId: workspace.id
      }
    });

    // Create collection item in hierarchy
    const collectionItem = await prisma.item.create({
      data: {
        type: 'COLLECTION',
        entityId: collection.id,
        workspaceId: workspace.id,
        parentId: null,
        order: 1
      }
    });

    console.log('✅ Created sample collection:', collection.name);

    // Create a folder inside the collection
    const folder = await prisma.folder.create({
      data: {
        name: 'User Management',
        description: 'API endpoints for user management',
        workspaceId: workspace.id
      }
    });

    // Create folder item in hierarchy
    const folderItem = await prisma.item.create({
      data: {
        type: 'FOLDER',
        entityId: folder.id,
        workspaceId: workspace.id,
        parentId: collectionItem.id,
        order: 1
      }
    });

    console.log('✅ Created sample folder:', folder.name);

    // Create sample requests
    const requests = [
      {
        name: 'Get All Users',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/users',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: null,
        parentId: folderItem.id,
        order: 1
      },
      {
        name: 'Get User by ID',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: null,
        parentId: folderItem.id,
        order: 2
      },
      {
        name: 'Create New User',
        method: 'POST',
        url: 'https://jsonplaceholder.typicode.com/users',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: {
          name: 'John Doe',
          username: 'johndoe',
          email: '<EMAIL>',
          phone: '************** x56442',
          website: 'hildegard.org'
        },
        parentId: folderItem.id,
        order: 3
      },
      {
        name: 'Update User',
        method: 'PUT',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: {
          id: 1,
          name: 'John Doe Updated',
          username: 'johndoe',
          email: '<EMAIL>'
        },
        parentId: folderItem.id,
        order: 4
      },
      {
        name: 'Delete User',
        method: 'DELETE',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Accept': 'application/json'
        },
        body: null,
        parentId: folderItem.id,
        order: 5
      }
    ];

    for (const requestData of requests) {
      const request = await prisma.request.create({
        data: {
          name: requestData.name,
          method: requestData.method,
          url: requestData.url,
          headers: requestData.headers as any,
          body: requestData.body as any,
          workspaceId: workspace.id
        }
      });

      await prisma.item.create({
        data: {
          type: 'REQUEST',
          entityId: request.id,
          workspaceId: workspace.id,
          parentId: requestData.parentId,
          order: requestData.order
        }
      });

      console.log('✅ Created request:', request.name);
    }

    // Create a standalone request (not in a folder)
    const standaloneRequest = await prisma.request.create({
      data: {
        name: 'Get Posts',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/posts',
        headers: JSON.stringify({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }),
        body: undefined,
        workspaceId: workspace.id
      }
    });

    await prisma.item.create({
      data: {
        type: 'REQUEST',
        entityId: standaloneRequest.id,
        workspaceId: workspace.id,
        parentId: collectionItem.id,
        order: 2
      }
    });

    console.log('✅ Created standalone request:', standaloneRequest.name);

    // Create an example response for one of the requests
    const getUserRequestItem = await prisma.item.findFirst({
      where: {
        type: 'REQUEST',
        workspaceId: workspace.id
      }
    });

    if (getUserRequestItem) {
      // Get the actual request entity
      const requestEntity = await prisma.request.findUnique({
        where: { id: getUserRequestItem.entityId }
      });

      if (requestEntity) {
        const example = await prisma.example.create({
        data: {
          name: 'Success Response',
          response: JSON.stringify({
            status: 200,
            statusText: 'OK',
            headers: {
              'content-type': 'application/json; charset=utf-8',
              'cache-control': 'max-age=43200'
            },
            data: {
              id: 1,
              name: 'Leanne Graham',
              username: 'Bret',
              email: '<EMAIL>',
              address: {
                street: 'Kulas Light',
                suite: 'Apt. 556',
                city: 'Gwenborough',
                zipcode: '92998-3874'
              },
              phone: '************** x56442',
              website: 'hildegard.org'
            },
            responseTime: 245,
            size: 1024
          }),
          requestId: requestEntity!.id,
          workspaceId: workspace.id
        }
      });

        // Create example item as child of the request item
        await prisma.item.create({
          data: {
            type: 'EXAMPLE',
            entityId: example.id,
            workspaceId: workspace.id,
            parentId: getUserRequestItem.id, // Make it child of the request item
            order: 1
          }
        });

        console.log('✅ Created example response:', example.name);
      }
    }

    // Create default settings
    const defaultSettings = [
      { key: 'theme', value: { mode: 'auto' } },
      { key: 'language', value: 'en' },
      { key: 'autoSave', value: true },
      { key: 'requestTimeout', value: 30000 }
    ];

    for (const setting of defaultSettings) {
      await prisma.settings.upsert({
        where: { key: setting.key },
        update: { value: JSON.stringify(setting.value) },
        create: {
          key: setting.key,
          value: JSON.stringify(setting.value)
        }
      });
    }

    console.log('✅ Created default settings');

    // Create environments
    const environments = [
      {
        name: 'Development',
        description: 'Development environment for testing',
        variables: [
          { key: 'baseUrl', value: 'https://api.dev.example.com', enabled: true, description: 'Base API URL' },
          { key: 'apiKey', value: 'dev_api_key_123', enabled: true, description: 'API Key for authentication' },
          { key: 'timeout', value: '5000', enabled: true, description: 'Request timeout in ms' },
          { key: 'version', value: 'v1', enabled: true, description: 'API version' }
        ],
        isActive: true
      },
      {
        name: 'Staging',
        description: 'Staging environment for pre-production testing',
        variables: [
          { key: 'baseUrl', value: 'https://api.staging.example.com', enabled: true, description: 'Base API URL' },
          { key: 'apiKey', value: 'staging_api_key_456', enabled: true, description: 'API Key for authentication' },
          { key: 'timeout', value: '10000', enabled: true, description: 'Request timeout in ms' },
          { key: 'version', value: 'v1', enabled: true, description: 'API version' },
          { key: 'debugMode', value: 'true', enabled: false, description: 'Enable debug logging' }
        ],
        isActive: false
      },
      {
        name: 'Production',
        description: 'Production environment',
        variables: [
          { key: 'baseUrl', value: 'https://api.example.com', enabled: true, description: 'Base API URL' },
          { key: 'apiKey', value: 'prod_api_key_789', enabled: true, description: 'API Key for authentication' },
          { key: 'timeout', value: '15000', enabled: true, description: 'Request timeout in ms' },
          { key: 'version', value: 'v2', enabled: true, description: 'API version' },
          { key: 'rateLimitHeader', value: 'X-RateLimit-Remaining', enabled: true, description: 'Rate limit header' }
        ],
        isActive: false
      }
    ];

    for (const envData of environments) {
      const environment = await prisma.environment.upsert({
        where: {
          workspaceId_name: {
            workspaceId: workspace.id,
            name: envData.name
          }
        },
        update: {
          description: envData.description,
          variables: envData.variables,
          isActive: envData.isActive
        },
        create: {
          name: envData.name,
          description: envData.description,
          variables: envData.variables,
          isActive: envData.isActive,
          workspaceId: workspace.id
        }
      });
      console.log('✅ Created/updated environment:', environment.name);
    }

    // Create another collection - E-commerce API
    const ecommerceCollection = await prisma.collection.create({
      data: {
        name: 'E-commerce API',
        description: 'API endpoints for e-commerce operations',
        variables: {
          store_url: 'https://api.shop.example.com',
          api_version: 'v2',
          currency: 'USD'
        },
        workspaceId: workspace.id
      }
    });

    const ecommerceCollectionItem = await prisma.item.create({
      data: {
        type: 'COLLECTION',
        entityId: ecommerceCollection.id,
        workspaceId: workspace.id,
        parentId: null,
        order: 2
      }
    });

    console.log('✅ Created e-commerce collection:', ecommerceCollection.name);

    // Create folders in e-commerce collection
    const productFolder = await prisma.folder.create({
      data: {
        name: 'Products',
        description: 'Product management endpoints',
        workspaceId: workspace.id
      }
    });

    const productFolderItem = await prisma.item.create({
      data: {
        type: 'FOLDER',
        entityId: productFolder.id,
        workspaceId: workspace.id,
        parentId: ecommerceCollectionItem.id,
        order: 1
      }
    });

    const orderFolder = await prisma.folder.create({
      data: {
        name: 'Orders',
        description: 'Order management endpoints',
        workspaceId: workspace.id
      }
    });

    const orderFolderItem = await prisma.item.create({
      data: {
        type: 'FOLDER',
        entityId: orderFolder.id,
        workspaceId: workspace.id,
        parentId: ecommerceCollectionItem.id,
        order: 2
      }
    });

    console.log('✅ Created e-commerce folders');

    // Create e-commerce requests
    const ecommerceRequests = [
      {
        name: 'Get All Products',
        method: 'GET',
        url: '{{baseUrl}}/products',
        headers: {
          'Authorization': 'Bearer {{apiKey}}',
          'Content-Type': 'application/json'
        },
        body: null,
        parentId: productFolderItem.id,
        order: 1
      },
      {
        name: 'Create Product',
        method: 'POST',
        url: '{{baseUrl}}/products',
        headers: {
          'Authorization': 'Bearer {{apiKey}}',
          'Content-Type': 'application/json'
        },
        body: {
          name: 'Awesome Product',
          description: 'This is an awesome product',
          price: 29.99,
          category: 'electronics',
          stock: 100
        },
        parentId: productFolderItem.id,
        order: 2
      },
      {
        name: 'Get Product by ID',
        method: 'GET',
        url: '{{baseUrl}}/products/123',
        headers: {
          'Authorization': 'Bearer {{apiKey}}',
          'Content-Type': 'application/json'
        },
        body: null,
        parentId: productFolderItem.id,
        order: 3
      },
      {
        name: 'Get All Orders',
        method: 'GET',
        url: '{{baseUrl}}/orders',
        headers: {
          'Authorization': 'Bearer {{apiKey}}',
          'Content-Type': 'application/json'
        },
        body: null,
        parentId: orderFolderItem.id,
        order: 1
      },
      {
        name: 'Create Order',
        method: 'POST',
        url: '{{baseUrl}}/orders',
        headers: {
          'Authorization': 'Bearer {{apiKey}}',
          'Content-Type': 'application/json'
        },
        body: {
          customerId: 'cust_123',
          items: [
            { productId: 'prod_123', quantity: 2, price: 29.99 },
            { productId: 'prod_456', quantity: 1, price: 49.99 }
          ],
          shippingAddress: {
            street: '123 Main St',
            city: 'Anytown',
            state: 'CA',
            zipCode: '12345'
          }
        },
        parentId: orderFolderItem.id,
        order: 2
      }
    ];

    for (const requestData of ecommerceRequests) {
      const request = await prisma.request.create({
        data: {
          name: requestData.name,
          method: requestData.method,
          url: requestData.url,
          headers: requestData.headers as any,
          body: requestData.body as any,
          workspaceId: workspace.id
        }
      });

      await prisma.item.create({
        data: {
          type: 'REQUEST',
          entityId: request.id,
          workspaceId: workspace.id,
          parentId: requestData.parentId,
          order: requestData.order
        }
      });

      console.log('✅ Created e-commerce request:', request.name);
    }

    // Create sample request history entries
    const historyEntries = [
      {
        name: 'Get All Users',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/users',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: null,
        response: {
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: [
            { id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>' },
            { id: 2, name: 'Ervin Howell', username: 'Antonette', email: '<EMAIL>' }
          ]
        },
        statusCode: 200,
        responseTime: 245,
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
      },
      {
        name: 'Create New User',
        method: 'POST',
        url: 'https://jsonplaceholder.typicode.com/users',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: {
          name: 'John Doe',
          username: 'johndoe',
          email: '<EMAIL>'
        },
        response: {
          status: 201,
          headers: { 'content-type': 'application/json' },
          data: {
            id: 11,
            name: 'John Doe',
            username: 'johndoe',
            email: '<EMAIL>'
          }
        },
        statusCode: 201,
        responseTime: 189,
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 10) // 10 minutes ago
      },
      {
        name: 'Get User by ID',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: null,
        response: {
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: {
            id: 1,
            name: 'Leanne Graham',
            username: 'Bret',
            email: '<EMAIL>',
            phone: '************** x56442'
          }
        },
        statusCode: 200,
        responseTime: 156,
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago
      },
      {
        name: 'Get All Products',
        method: 'GET',
        url: 'https://api.dev.example.com/products',
        headers: {
          'Authorization': 'Bearer dev_api_key_123',
          'Content-Type': 'application/json'
        },
        body: null,
        response: {
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: {
            products: [
              { id: 1, name: 'Laptop', price: 999.99, category: 'electronics' },
              { id: 2, name: 'Mouse', price: 29.99, category: 'electronics' }
            ],
            total: 2,
            page: 1
          }
        },
        statusCode: 200,
        responseTime: 312,
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 20) // 20 minutes ago
      },
      {
        name: 'Create Product',
        method: 'POST',
        url: 'https://api.dev.example.com/products',
        headers: {
          'Authorization': 'Bearer dev_api_key_123',
          'Content-Type': 'application/json'
        },
        body: {
          name: 'Awesome Product',
          description: 'This is an awesome product',
          price: 29.99,
          category: 'electronics'
        },
        response: {
          status: 201,
          headers: { 'content-type': 'application/json' },
          data: {
            id: 3,
            name: 'Awesome Product',
            description: 'This is an awesome product',
            price: 29.99,
            category: 'electronics',
            createdAt: '2024-01-15T10:30:00Z'
          }
        },
        statusCode: 201,
        responseTime: 278,
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 25) // 25 minutes ago
      },
      {
        name: 'Get Posts',
        method: 'GET',
        url: 'https://jsonplaceholder.typicode.com/posts',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: null,
        response: null,
        statusCode: 500,
        responseTime: null,
        error: 'Network timeout after 5000ms',
        environment: 'Development',
        executedAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        name: 'Update User',
        method: 'PUT',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: {
          id: 1,
          name: 'John Doe Updated',
          username: 'johndoe',
          email: '<EMAIL>'
        },
        response: {
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: {
            id: 1,
            name: 'John Doe Updated',
            username: 'johndoe',
            email: '<EMAIL>'
          }
        },
        statusCode: 200,
        responseTime: 198,
        environment: 'Staging',
        executedAt: new Date(Date.now() - 1000 * 60 * 60) // 1 hour ago
      },
      {
        name: 'Delete User',
        method: 'DELETE',
        url: 'https://jsonplaceholder.typicode.com/users/1',
        headers: {
          'Accept': 'application/json'
        },
        body: null,
        response: {
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: {}
        },
        statusCode: 200,
        responseTime: 134,
        environment: 'Staging',
        executedAt: new Date(Date.now() - 1000 * 60 * 90) // 1.5 hours ago
      }
    ];

    for (const historyData of historyEntries) {
      const history = await prisma.requestHistory.create({
        data: {
          name: historyData.name,
          method: historyData.method,
          url: historyData.url,
          headers: historyData.headers,
          body: historyData.body,
          response: historyData.response,
          statusCode: historyData.statusCode,
          responseTime: historyData.responseTime,
          error: historyData.error,
          environment: historyData.environment,
          workspaceId: workspace.id,
          executedAt: historyData.executedAt
        }
      });
      console.log('✅ Created history entry:', history.name);
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Summary:
    - 1 Workspace created
    - 2 Collections created
    - 3 Folders created
    - 11 Requests created
    - 1 Example response created
    - 3 Environments created
    - 8 History entries created
    - 4 Default settings created`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
