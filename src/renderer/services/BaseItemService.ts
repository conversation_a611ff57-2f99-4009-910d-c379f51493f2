/**
 * Base service interface for all item types
 */
export interface BaseItemService<T> {
  /**
   * Create a new item
   */
  create(data: Partial<T>, workspaceId: number): Promise<T>;

  /**
   * Update an existing item
   */
  update(id: string | number, data: Partial<T>): Promise<T>;

  /**
   * Get an item by ID
   */
  get(id: string | number): Promise<T | null>;

  /**
   * Delete an item
   */
  delete(id: string | number): Promise<boolean>;

  /**
   * Check if an item is new (not saved to database)
   */
  isNew(data: any): boolean;

  /**
   * Get the display name for an item
   */
  getDisplayName(data: any): string;

  /**
   * Get default data for a new item
   */
  getDefaultData(): Partial<T>;
}

/**
 * Base item data interface
 */
export interface BaseItemData {
  id?: string | number;
  entityId?: number;
  hierarchyId?: string;
  name: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Service registry to manage all item services
 */
export class ServiceRegistry {
  private static services = new Map<string, BaseItemService<any>>();

  static register<T>(type: string, service: BaseItemService<T>) {
    this.services.set(type, service);
  }

  static get<T>(type: string): BaseItemService<T> | undefined {
    return this.services.get(type);
  }

  static getService<T>(type: string): BaseItemService<T> {
    const service = this.services.get(type);
    if (!service) {
      throw new Error(`No service registered for type: ${type}`);
    }
    return service;
  }

  static getAllTypes(): string[] {
    return Array.from(this.services.keys());
  }
}
