{"name": "apicool", "version": "1.0.0", "description": "Advanced API testing tool with AI integration and multi-workspace support", "main": "dist/main/main.js", "scripts": {"build": "npm run build:main && npm run build:preload && npm run build:renderer", "build:main": "tsc", "build:preload": "webpack --config webpack.preload.config.js --mode=production", "build:renderer": "webpack --mode=production", "start": "npm run build && electron .", "dev": "npm run dev:main", "dev:main": "tsc --noEmitOnError false && electron .", "dev:renderer": "webpack serve --mode=development", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:reset-dev": "prisma migrate reset --force && prisma migrate dev && npm run db:seed", "db:studio": "prisma studio", "db:seed": "tsc src/main/seed.ts --outDir dist --target ES2020 --module commonjs --esModuleInterop --skipLibCheck && node dist/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/jinge1936/ApiCool.git"}, "keywords": ["api", "testing", "electron", "postman", "ai"], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/jinge1936/ApiCool/issues"}, "homepage": "https://github.com/jinge1936/ApiCool#readme", "devDependencies": {"@types/axios": "^0.14.4", "@types/node": "^24.1.0", "css-loader": "^7.1.2", "electron": "^37.2.5", "electron-builder": "^26.0.12", "html-webpack-plugin": "^5.6.3", "prisma": "^6.13.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.9.2", "webpack": "^5.101.0", "webpack-cli": "^6.0.1"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@prisma/client": "^6.13.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "axios": "^1.11.0", "highlight.js": "^11.11.1", "marked": "^16.1.1", "monaco-editor": "^0.52.2", "node-cron": "^4.2.1", "react": "^19.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "winston": "^3.17.0"}}