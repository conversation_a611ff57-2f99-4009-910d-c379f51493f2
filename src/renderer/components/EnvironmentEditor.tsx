import React, { useState, useEffect } from 'react';

interface EnvironmentVariable {
  key: string;
  value: string;
  enabled: boolean;
  description?: string;
}

interface Environment {
  id: number;
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  isActive: boolean;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

interface EnvironmentEditorProps {
  workspaceId: number;
  environmentId?: number;
  isNewEnvironment?: boolean;
}

const EnvironmentEditor: React.FC<EnvironmentEditorProps> = ({
  workspaceId,
  environmentId,
  isNewEnvironment = false
}) => {
  const [environment, setEnvironment] = useState<Environment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Form state for editing environment
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    variables: [{ key: '', value: '', enabled: true, description: '' }] as EnvironmentVariable[]
  });

  useEffect(() => {
    if (isNewEnvironment) {
      // Initialize for new environment
      setFormData({
        name: '',
        description: '',
        variables: [{ key: '', value: '', enabled: true, description: '' }]
      });
      setLoading(false);
    } else if (environmentId) {
      // Load specific environment
      loadEnvironment();
    }
  }, [workspaceId, environmentId, isNewEnvironment]);

  const loadEnvironment = async () => {
    if (!environmentId) return;

    try {
      setLoading(true);
      const environments = await window.electronAPI.environment.getAll(workspaceId);
      const env = environments.find(e => e.id === environmentId);

      if (env) {
        setEnvironment(env);
        setFormData({
          name: env.name,
          description: env.description || '',
          variables: env.variables.length > 0 ? env.variables : [{ key: '', value: '', enabled: true, description: '' }]
        });
      } else {
        setError('Environment not found');
      }
    } catch (err) {
      setError('Failed to load environment');
      console.error('Error loading environment:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      if (!formData.name.trim()) {
        setError('Environment name is required');
        return;
      }

      if (isNewEnvironment) {
        // Create new environment
        const newEnv = await window.electronAPI.environment.create({
          name: formData.name,
          description: formData.description,
          variables: formData.variables.filter(v => v.key.trim()),
          workspaceId
        });
        setEnvironment(newEnv);
        setHasChanges(false);
        // Dispatch event to refresh environments list
        window.dispatchEvent(new CustomEvent('environment-created', { detail: newEnv }));
      } else if (environmentId) {
        // Update existing environment
        const updatedEnv = await window.electronAPI.environment.update(environmentId, {
          name: formData.name,
          description: formData.description,
          variables: formData.variables.filter(v => v.key.trim())
        });
        setEnvironment(updatedEnv);
        setHasChanges(false);
        // Dispatch event to refresh environments list
        window.dispatchEvent(new CustomEvent('environment-updated', { detail: updatedEnv }));
      }
    } catch (err) {
      setError(err.message || 'Failed to save environment');
    }
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const addVariable = () => {
    setFormData(prev => ({
      ...prev,
      variables: [...prev.variables, { key: '', value: '', enabled: true, description: '' }]
    }));
    setHasChanges(true);
  };

  const updateVariable = (index: number, field: keyof EnvironmentVariable, value: any) => {
    setFormData(prev => {
      const updatedVariables = [...prev.variables];
      updatedVariables[index] = { ...updatedVariables[index], [field]: value };
      return { ...prev, variables: updatedVariables };
    });
    setHasChanges(true);
  };

  const removeVariable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter((_, i) => i !== index)
    }));
    setHasChanges(true);
  };

  if (loading) {
    return <div className="environment-editor loading">Loading environment...</div>;
  }

  return (
    <div className="environment-editor">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="environment-form">
        <div className="form-header">
          <h3>{isNewEnvironment ? 'Create Environment' : `Edit Environment: ${environment?.name || 'Loading...'}`}</h3>
          <div className="form-actions">
            <button
              className="btn btn-primary"
              onClick={handleSave}
              disabled={!hasChanges && !isNewEnvironment}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                <polyline points="17,21 17,13 7,13 7,21"/>
                <polyline points="7,3 7,8 15,8"/>
              </svg>
              {isNewEnvironment ? 'Create' : 'Save'}
            </button>
          </div>
        </div>

        <div className="form-section">
          <div className="form-group">
            <label>Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleFormChange('name', e.target.value)}
              placeholder="Environment name"
            />
          </div>
          <div className="form-group">
            <label>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleFormChange('description', e.target.value)}
              placeholder="Optional description"
              rows={2}
            />
          </div>
        </div>

        <div className="variables-section">
          <div className="variables-header">
            <h4>Variables</h4>
            <button className="btn btn-primary" onClick={addVariable}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Add Variable
            </button>
          </div>

          <div className="variables-table">
            <div className="table-header">
              <div className="col-enabled">Enabled</div>
              <div className="col-key">Key</div>
              <div className="col-value">Value</div>
              <div className="col-description">Description</div>
              <div className="col-actions">Actions</div>
            </div>

            {formData.variables.map((variable, index) => (
              <div key={index} className="table-row">
                <div className="col-enabled">
                  <input
                    type="checkbox"
                    checked={variable.enabled}
                    onChange={(e) => updateVariable(index, 'enabled', e.target.checked)}
                  />
                </div>
                <div className="col-key">
                  <input
                    type="text"
                    value={variable.key}
                    onChange={(e) => updateVariable(index, 'key', e.target.value)}
                    placeholder="Variable key"
                  />
                </div>
                <div className="col-value">
                  <input
                    type="text"
                    value={variable.value}
                    onChange={(e) => updateVariable(index, 'value', e.target.value)}
                    placeholder="Variable value"
                  />
                </div>
                <div className="col-description">
                  <input
                    type="text"
                    value={variable.description || ''}
                    onChange={(e) => updateVariable(index, 'description', e.target.value)}
                    placeholder="Optional description"
                  />
                </div>
                <div className="col-actions">
                  <button
                    className="btn-icon btn-danger"
                    onClick={() => removeVariable(index)}
                    title="Remove variable"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="3,6 5,6 21,6"/>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnvironmentEditor;
