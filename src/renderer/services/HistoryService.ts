import { BaseItemService } from './BaseItemService';
import { HistoryData } from '../types/history';

export class HistoryService implements BaseItemService<HistoryData> {
  async create(data: Partial<HistoryData>, workspaceId: number): Promise<HistoryData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const historyEntry = await window.electronAPI.history.create({
        name: data.name || `${data.method || 'GET'} ${data.url || ''}`.trim(),
        method: data.method || 'GET',
        url: data.url || '',
        headers: data.headers || {},
        body: data.body || '',
        response: data.response,
        executedAt: data.executedAt || new Date(),
        workspaceId
      });

      return {
        id: historyEntry.id,
        entityId: historyEntry.id,
        name: historyEntry.name,
        method: historyEntry.method,
        url: historyEntry.url,
        headers: historyEntry.headers,
        body: historyEntry.body,
        response: historyEntry.response,
        executedAt: historyEntry.executedAt,
        workspaceId: historyEntry.workspaceId,
        createdAt: historyEntry.createdAt,
        updatedAt: historyEntry.updatedAt
      };
    } catch (error) {
      console.error('Failed to create history entry:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<HistoryData>): Promise<HistoryData> {
    // History entries are typically read-only, so we'll just return the current data
    // since the API doesn't support updating history entries
    const current = await this.get(id);
    if (!current) {
      throw new Error('History entry not found');
    }

    // Return the current data since history entries are read-only
    return current;
  }

  async get(id: string | number): Promise<HistoryData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const historyEntry = await window.electronAPI.history.getEntry(entityId);

      if (!historyEntry) return null;

      return {
        id: historyEntry.id,
        entityId: historyEntry.id,
        name: historyEntry.name,
        method: historyEntry.method,
        url: historyEntry.url,
        headers: historyEntry.headers,
        body: historyEntry.body,
        response: historyEntry.response,
        executedAt: historyEntry.executedAt,
        workspaceId: historyEntry.workspaceId,
        createdAt: historyEntry.createdAt,
        updatedAt: historyEntry.updatedAt
      };
    } catch (error) {
      console.error('Failed to get history entry:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.history.delete(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete history entry:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // History entries are never "new" since they're created from executed requests
    return false;
  }

  getDisplayName(data: any): string {
    return data.name || `${data.method || 'GET'} ${data.url || ''}`.trim() || 'History Entry';
  }

  getDefaultData(): Partial<HistoryData> {
    return {
      name: 'History Entry',
      method: 'GET',
      url: '',
      headers: {},
      body: '',
      executedAt: new Date(),
      workspaceId: 1
    };
  }

  /**
   * History entries don't need save dialog - they're read-only
   */
  needsSaveDialog(data: any): boolean {
    return false;
  }
}
