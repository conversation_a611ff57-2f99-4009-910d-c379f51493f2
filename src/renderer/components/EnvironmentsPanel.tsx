import React, { useState } from 'react';

interface Environment {
  id: string;
  name: string;
  variables: Record<string, string>;
  isActive: boolean;
}

const EnvironmentsPanel: React.FC = () => {
  const [environments] = useState<Environment[]>([
    {
      id: 'env-1',
      name: 'Development',
      variables: {
        'base_url': 'https://api-dev.example.com',
        'api_key': 'dev_key_123'
      },
      isActive: true
    },
    {
      id: 'env-2',
      name: 'Staging',
      variables: {
        'base_url': 'https://api-staging.example.com',
        'api_key': 'staging_key_456'
      },
      isActive: false
    },
    {
      id: 'env-3',
      name: 'Production',
      variables: {
        'base_url': 'https://api.example.com',
        'api_key': 'prod_key_789'
      },
      isActive: false
    }
  ]);

  return (
    <div className="environments-panel">
      <div className="environments-list">
        {environments.map(env => (
          <div 
            key={env.id} 
            className={`environment-item ${env.isActive ? 'active' : ''}`}
          >
            <div className="environment-header">
              <span className="environment-icon">🌍</span>
              <span className="environment-name">{env.name}</span>
              {env.isActive && <span className="environment-badge">Active</span>}
            </div>
            <div className="environment-variables">
              {Object.entries(env.variables).map(([key, value]) => (
                <div key={key} className="environment-variable">
                  <span className="variable-key">{key}:</span>
                  <span className="variable-value">{value}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EnvironmentsPanel;
