import React, { useState, useEffect } from 'react';
import { Workspace } from '../../types/workspace-management';
import WorkspaceManager from '../../services/WorkspaceManager';

interface WorkspaceSwitcherProps {
  onWorkspaceChange?: (workspace: Workspace) => void;
  onCreateWorkspace?: () => void;
  onManageWorkspaces?: () => void;
}

const WorkspaceSwitcher: React.FC<WorkspaceSwitcherProps> = ({
  onWorkspaceChange,
  onCreateWorkspace,
  onManageWorkspaces
}) => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [activeWorkspace, setActiveWorkspace] = useState<Workspace | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    // Load workspaces on mount
    loadWorkspaces();

    // Listen for workspace changes
    const handleWorkspaceChange = (event: CustomEvent) => {
      setActiveWorkspace(event.detail.workspace);
      if (onWorkspaceChange) {
        onWorkspaceChange(event.detail.workspace);
      }
    };

    const handleWorkspaceCreated = () => {
      loadWorkspaces();
    };

    const handleWorkspaceDeleted = () => {
      loadWorkspaces();
    };

    window.addEventListener('workspace-changed', handleWorkspaceChange as EventListener);
    window.addEventListener('workspace-created', handleWorkspaceCreated);
    window.addEventListener('workspace-deleted', handleWorkspaceDeleted);

    return () => {
      window.removeEventListener('workspace-changed', handleWorkspaceChange as EventListener);
      window.removeEventListener('workspace-created', handleWorkspaceCreated);
      window.removeEventListener('workspace-deleted', handleWorkspaceDeleted);
    };
  }, [onWorkspaceChange]);

  const loadWorkspaces = async () => {
    try {
      if (window.electronAPI) {
        // Load workspaces from database
        const dbWorkspaces = await window.electronAPI.workspace.getAll();

        // Convert database workspaces to the expected format
        const formattedWorkspaces = dbWorkspaces.map((ws: any) => ({
          id: ws.id.toString(),
          name: ws.name,
          description: ws.description,
          isDefault: ws.id === 1, // First workspace is default
          createdAt: new Date(ws.createdAt),
          updatedAt: new Date(ws.updatedAt),
          settings: {
            theme: 'auto' as const,
            autoSave: true,
            requestTimeout: 30000,
            followRedirects: true,
            validateSSL: true
          },
          collections: [],
          environments: []
        }));

        setWorkspaces(formattedWorkspaces);

        // Set active workspace (default to first one if none set)
        const activeWorkspaceId = localStorage.getItem('apicool-active-workspace');
        let activeWorkspace = formattedWorkspaces.find((ws: any) => ws.id === activeWorkspaceId);

        // If no active workspace found or none set, use the first one
        if (!activeWorkspace && formattedWorkspaces.length > 0) {
          activeWorkspace = formattedWorkspaces[0];
        }

        setActiveWorkspace(activeWorkspace);

        // Store active workspace ID
        if (activeWorkspace) {
          localStorage.setItem('apicool-active-workspace', activeWorkspace.id);
        }
      } else {
        // Fallback to localStorage for development
        const allWorkspaces = WorkspaceManager.getWorkspaces();
        const active = WorkspaceManager.getActiveWorkspace();
        setWorkspaces(allWorkspaces);
        setActiveWorkspace(active);
      }
    } catch (error) {
      console.error('Failed to load workspaces from database:', error);
      // Fallback to localStorage
      const allWorkspaces = WorkspaceManager.getWorkspaces();
      const active = WorkspaceManager.getActiveWorkspace();
      setWorkspaces(allWorkspaces);
      setActiveWorkspace(active);
    }
  };

  const handleWorkspaceSelect = (workspace: Workspace) => {
    // Store the active workspace ID
    localStorage.setItem('apicool-active-workspace', workspace.id);
    setActiveWorkspace(workspace);
    setIsDropdownOpen(false);

    // Dispatch workspace switched event for other components
    window.dispatchEvent(new CustomEvent('workspace-switched', {
      detail: {
        workspaceId: workspace.id,
        workspaceName: workspace.name,
        workspace: workspace
      }
    }));
  };

  const handleCreateNew = () => {
    setIsDropdownOpen(false);
    if (onCreateWorkspace) {
      onCreateWorkspace();
    }
  };

  const handleManage = () => {
    setIsDropdownOpen(false);
    if (onManageWorkspaces) {
      onManageWorkspaces();
    }
  };

  return (
    <div className="workspace-switcher">
      <button
        className="workspace-switcher-button"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        title="Switch workspace"
      >
        <span className="workspace-icon">🏢</span>
        <span className="workspace-name">{activeWorkspace?.name || 'Loading...'}</span>
        <span className="workspace-arrow">{isDropdownOpen ? '▲' : '▼'}</span>
      </button>

      {isDropdownOpen && (
        <div className="workspace-dropdown">
          <div className="workspace-dropdown-header">
            <span>Workspaces</span>
          </div>

          <div className="workspace-list">
            {workspaces.map(workspace => (
              <button
                key={workspace.id}
                className={`workspace-item ${workspace.id === activeWorkspace?.id ? 'active' : ''}`}
                onClick={() => handleWorkspaceSelect(workspace)}
              >
                <div className="workspace-item-content">
                  <span className="workspace-item-name">{workspace.name}</span>
                  {workspace.isDefault && (
                    <span className="workspace-badge">Default</span>
                  )}
                </div>
                {workspace.description && (
                  <span className="workspace-item-desc">{workspace.description}</span>
                )}
              </button>
            ))}
          </div>

          <div className="workspace-dropdown-actions">
            <button
              className="workspace-action-btn"
              onClick={handleCreateNew}
            >
              <span className="action-icon">➕</span>
              Create Workspace
            </button>
            <button
              className="workspace-action-btn"
              onClick={handleManage}
            >
              <span className="action-icon">⚙️</span>
              Manage Workspaces
            </button>
          </div>
        </div>
      )}

      {isDropdownOpen && (
        <div
          className="workspace-dropdown-overlay"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default WorkspaceSwitcher;
