import React, { useState, useEffect } from 'react';

interface HistoryEntry {
  id: number;
  requestId?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  response?: any;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  environment?: string;
  workspaceId: number;
  executedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface HistoryEntryViewerProps {
  workspaceId: number;
  historyId: number;
  onOpenRequest?: (historyEntry: HistoryEntry) => void;
}

const HistoryEntryViewer: React.FC<HistoryEntryViewerProps> = ({ 
  workspaceId, 
  historyId, 
  onOpenRequest 
}) => {
  const [historyEntry, setHistoryEntry] = useState<HistoryEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadHistoryEntry();
  }, [workspaceId, historyId]);

  const loadHistoryEntry = async () => {
    try {
      setLoading(true);
      const entry = await window.electronAPI.history.getEntry(historyId);
      if (entry) {
        setHistoryEntry(entry);
      } else {
        setError('History entry not found');
      }
    } catch (err) {
      setError('Failed to load history entry');
      console.error('Error loading history entry:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEntry = async () => {
    if (!historyEntry) return;
    if (!confirm('Are you sure you want to delete this history entry?')) return;

    try {
      await window.electronAPI.history.delete(historyEntry.id);
      // Dispatch event to refresh history list
      window.dispatchEvent(new CustomEvent('history-entry-deleted', { detail: historyEntry.id }));
    } catch (err) {
      setError('Failed to delete history entry');
    }
  };

  const handleReplayRequest = () => {
    if (historyEntry && onOpenRequest) {
      onOpenRequest(historyEntry);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return 'N/A';
    return `${time}ms`;
  };

  const getStatusColor = (statusCode?: number) => {
    if (!statusCode) return 'gray';
    if (statusCode >= 200 && statusCode < 300) return 'green';
    if (statusCode >= 300 && statusCode < 400) return 'orange';
    if (statusCode >= 400) return 'red';
    return 'gray';
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET': return 'blue';
      case 'POST': return 'green';
      case 'PUT': return 'orange';
      case 'DELETE': return 'red';
      case 'PATCH': return 'purple';
      default: return 'gray';
    }
  };

  if (loading) {
    return <div className="history-entry-viewer loading">Loading history entry...</div>;
  }

  if (!historyEntry) {
    return (
      <div className="history-entry-viewer error">
        <div className="error-message">
          {error || 'History entry not found'}
        </div>
      </div>
    );
  }

  return (
    <div className="history-entry-viewer">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="history-details">
        <div className="details-header">
          <div className="details-title">
            <span className={`method-badge ${getMethodColor(historyEntry.method)}`}>
              {historyEntry.method}
            </span>
            <h3>{historyEntry.name}</h3>
            {historyEntry.statusCode && (
              <span className={`status-badge ${getStatusColor(historyEntry.statusCode)}`}>
                {historyEntry.statusCode}
              </span>
            )}
          </div>
          <div className="details-actions">
            <button 
              className="btn-primary"
              onClick={handleReplayRequest}
            >
              Open as Request
            </button>
            <button 
              className="btn-danger"
              onClick={handleDeleteEntry}
            >
              Delete
            </button>
          </div>
        </div>

        <div className="details-content">
          <div className="detail-section">
            <h4>Request Details</h4>
            <div className="detail-item">
              <strong>URL:</strong> {historyEntry.url}
            </div>
            <div className="detail-item">
              <strong>Executed:</strong> {formatDate(historyEntry.executedAt)}
            </div>
            {historyEntry.environment && (
              <div className="detail-item">
                <strong>Environment:</strong> {historyEntry.environment}
              </div>
            )}
            {historyEntry.responseTime && (
              <div className="detail-item">
                <strong>Response Time:</strong> {formatResponseTime(historyEntry.responseTime)}
              </div>
            )}
          </div>

          {Object.keys(historyEntry.headers).length > 0 && (
            <div className="detail-section">
              <h4>Headers</h4>
              <div className="headers-list">
                {Object.entries(historyEntry.headers).map(([key, value]) => (
                  <div key={key} className="header-item">
                    <strong>{key}:</strong> {value}
                  </div>
                ))}
              </div>
            </div>
          )}

          {historyEntry.body && (
            <div className="detail-section">
              <h4>Request Body</h4>
              <pre className="code-block">
                {typeof historyEntry.body === 'string' 
                  ? historyEntry.body 
                  : JSON.stringify(historyEntry.body, null, 2)
                }
              </pre>
            </div>
          )}

          {historyEntry.response && (
            <div className="detail-section">
              <h4>Response</h4>
              <pre className="code-block">
                {typeof historyEntry.response === 'string' 
                  ? historyEntry.response 
                  : JSON.stringify(historyEntry.response, null, 2)
                }
              </pre>
            </div>
          )}

          {historyEntry.error && (
            <div className="detail-section">
              <h4>Error</h4>
              <div className="error-block">{historyEntry.error}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryEntryViewer;
