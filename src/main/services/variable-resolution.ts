interface Variable {
  name: string;
  initialValue: string;
  currentValue: string;
  type?: 'string' | 'number' | 'boolean' | 'secret';
  description?: string;
}

interface VariableScope {
  environment: Record<string, Variable>;
  collection: Record<string, Variable>;
  runtime: Record<string, Variable>;
}

export class VariableResolutionService {
  private variableScope: VariableScope = {
    environment: {},
    collection: {},
    runtime: {}
  };

  /**
   * Initialize variables from environment and collection
   */
  public initializeScope(
    environmentVariables: Record<string, Variable> = {},
    collectionVariables: Record<string, Variable> = {}
  ): void {
    this.variableScope.environment = { ...environmentVariables };
    this.variableScope.collection = { ...collectionVariables };
    this.variableScope.runtime = {};
  }

  /**
   * Get variable value with precedence: runtime > collection > environment
   */
  public getVariable(name: string): string | undefined {
    // Check runtime variables first (highest precedence)
    if (this.variableScope.runtime[name]) {
      return this.variableScope.runtime[name].currentValue;
    }

    // Check collection variables (medium precedence)
    if (this.variableScope.collection[name]) {
      return this.variableScope.collection[name].currentValue;
    }

    // Check environment variables (lowest precedence)
    if (this.variableScope.environment[name]) {
      return this.variableScope.environment[name].currentValue;
    }

    return undefined;
  }

  /**
   * Set a runtime variable (highest precedence)
   */
  public setVariable(name: string, value: string, type: Variable['type'] = 'string'): void {
    this.variableScope.runtime[name] = {
      name,
      initialValue: value,
      currentValue: value,
      type
    };
  }

  /**
   * Update an existing variable's current value
   */
  public updateVariable(name: string, value: string): boolean {
    // Update runtime variable first if it exists
    if (this.variableScope.runtime[name]) {
      this.variableScope.runtime[name].currentValue = value;
      return true;
    }

    // Update collection variable if it exists
    if (this.variableScope.collection[name]) {
      this.variableScope.collection[name].currentValue = value;
      return true;
    }

    // Update environment variable if it exists
    if (this.variableScope.environment[name]) {
      this.variableScope.environment[name].currentValue = value;
      return true;
    }

    return false;
  }

  /**
   * Resolve template strings like {{variableName}} in text
   */
  public resolveTemplate(template: string): string {
    if (!template || typeof template !== 'string') {
      return template;
    }

    return template.replace(/\{\{([^}]+)\}\}/g, (match, variableName) => {
      const trimmedName = variableName.trim();
      const value = this.getVariable(trimmedName);
      
      if (value !== undefined) {
        return value;
      }

      // Return the original template if variable not found
      console.warn(`Variable '${trimmedName}' not found, keeping template: ${match}`);
      return match;
    });
  }

  /**
   * Resolve variables in an object recursively
   */
  public resolveObject(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (typeof obj === 'string') {
      return this.resolveTemplate(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveObject(item));
    }

    if (typeof obj === 'object') {
      const resolved: any = {};
      for (const [key, value] of Object.entries(obj)) {
        resolved[key] = this.resolveObject(value);
      }
      return resolved;
    }

    return obj;
  }

  /**
   * Get all variables in current scope with precedence applied
   */
  public getAllVariables(): Record<string, Variable> {
    const allVariables: Record<string, Variable> = {};

    // Start with environment variables (lowest precedence)
    Object.assign(allVariables, this.variableScope.environment);

    // Override with collection variables (medium precedence)
    Object.assign(allVariables, this.variableScope.collection);

    // Override with runtime variables (highest precedence)
    Object.assign(allVariables, this.variableScope.runtime);

    return allVariables;
  }

  /**
   * Get variables by scope
   */
  public getVariablesByScope(scope: keyof VariableScope): Record<string, Variable> {
    return { ...this.variableScope[scope] };
  }

  /**
   * Clear runtime variables (useful for collection runs)
   */
  public clearRuntimeVariables(): void {
    this.variableScope.runtime = {};
  }

  /**
   * Export current variable state
   */
  public exportVariables(): VariableScope {
    return {
      environment: { ...this.variableScope.environment },
      collection: { ...this.variableScope.collection },
      runtime: { ...this.variableScope.runtime }
    };
  }

  /**
   * Import variable state
   */
  public importVariables(scope: VariableScope): void {
    this.variableScope = {
      environment: { ...scope.environment },
      collection: { ...scope.collection },
      runtime: { ...scope.runtime }
    };
  }

  /**
   * Check if a string contains template variables
   */
  public hasTemplateVariables(text: string): boolean {
    if (!text || typeof text !== 'string') {
      return false;
    }
    return /\{\{[^}]+\}\}/.test(text);
  }

  /**
   * Extract variable names from a template string
   */
  public extractVariableNames(template: string): string[] {
    if (!template || typeof template !== 'string') {
      return [];
    }

    const matches = template.match(/\{\{([^}]+)\}\}/g);
    if (!matches) {
      return [];
    }

    return matches.map(match => {
      const variableName = match.replace(/\{\{|\}\}/g, '').trim();
      return variableName;
    });
  }

  /**
   * Validate that all variables in a template can be resolved
   */
  public validateTemplate(template: string): { isValid: boolean; missingVariables: string[] } {
    const variableNames = this.extractVariableNames(template);
    const missingVariables: string[] = [];

    for (const name of variableNames) {
      if (this.getVariable(name) === undefined) {
        missingVariables.push(name);
      }
    }

    return {
      isValid: missingVariables.length === 0,
      missingVariables
    };
  }
}

export type { Variable, VariableScope };
