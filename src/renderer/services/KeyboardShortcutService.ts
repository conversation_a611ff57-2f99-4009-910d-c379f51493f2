/**
 * Centralized keyboard shortcut service to avoid conflicts and duplication
 */

export interface ShortcutHandler {
  key: string;
  ctrlOrCmd?: boolean;
  shift?: boolean;
  alt?: boolean;
  handler: (event: KeyboardEvent) => void;
  description: string;
  context?: string; // Optional context for conditional shortcuts
}

export class KeyboardShortcutService {
  private static instance: KeyboardShortcutService;
  private handlers: Map<string, ShortcutHandler[]> = new Map();
  private isListening = false;

  static getInstance(): KeyboardShortcutService {
    if (!KeyboardShortcutService.instance) {
      KeyboardShortcutService.instance = new KeyboardShortcutService();
    }
    return KeyboardShortcutService.instance;
  }

  private constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }

  private getShortcutKey(shortcut: Omit<ShortcutHandler, 'handler' | 'description'>): string {
    const parts = [];
    if (shortcut.ctrlOrCmd) parts.push('ctrl');
    if (shortcut.shift) parts.push('shift');
    if (shortcut.alt) parts.push('alt');
    parts.push(shortcut.key.toLowerCase());
    return parts.join('+');
  }

  private isMac(): boolean {
    return navigator.platform.toUpperCase().indexOf('MAC') >= 0 || 
           navigator.userAgent.includes('Mac');
  }

  private handleKeyDown(event: KeyboardEvent): void {
    const isMac = this.isMac();
    const isCtrlOrCmd = isMac ? event.metaKey : event.ctrlKey;

    // Build the current shortcut key
    const parts = [];
    if (isCtrlOrCmd) parts.push('ctrl');
    if (event.shiftKey) parts.push('shift');
    if (event.altKey) parts.push('alt');
    parts.push(event.key.toLowerCase());
    const shortcutKey = parts.join('+');

    // Find matching handlers
    const handlers = this.handlers.get(shortcutKey) || [];
    
    for (const handler of handlers) {
      // Check if handler matches current context
      if (this.shouldExecuteHandler(handler, event)) {
        event.preventDefault();
        handler.handler(event);
        break; // Execute only the first matching handler
      }
    }
  }

  private shouldExecuteHandler(handler: ShortcutHandler, event: KeyboardEvent): boolean {
    // If no context specified, always execute
    if (!handler.context) return true;

    // Context-specific logic
    switch (handler.context) {
      case 'tabs':
        // Execute if we're in the main app area (not in input fields)
        return !this.isInInputField(event.target as Element);
      
      case 'editor':
        // Execute if we're in an editor context
        return this.isInEditorContext(event.target as Element);
      
      default:
        return true;
    }
  }

  private isInInputField(target: Element): boolean {
    if (!target) return false;
    
    const tagName = target.tagName.toLowerCase();
    const inputTypes = ['input', 'textarea', 'select'];
    
    if (inputTypes.includes(tagName)) return true;
    
    // Check for contenteditable
    if (target.getAttribute('contenteditable') === 'true') return true;
    
    // Check for Monaco editor
    if (target.closest('.monaco-editor')) return true;
    
    return false;
  }

  private isInEditorContext(target: Element): boolean {
    if (!target) return false;
    return target.closest('.request-editor') !== null;
  }

  register(shortcut: ShortcutHandler): void {
    const key = this.getShortcutKey(shortcut);
    
    if (!this.handlers.has(key)) {
      this.handlers.set(key, []);
    }
    
    this.handlers.get(key)!.push(shortcut);
    console.log(`[KeyboardShortcut] Registered: ${key} - ${shortcut.description}`);
  }

  unregister(shortcut: Omit<ShortcutHandler, 'handler' | 'description'>): void {
    const key = this.getShortcutKey(shortcut);
    const handlers = this.handlers.get(key);
    
    if (handlers) {
      const filtered = handlers.filter(h => 
        h.context !== shortcut.context || 
        h.key !== shortcut.key ||
        h.ctrlOrCmd !== shortcut.ctrlOrCmd ||
        h.shift !== shortcut.shift ||
        h.alt !== shortcut.alt
      );
      
      if (filtered.length === 0) {
        this.handlers.delete(key);
      } else {
        this.handlers.set(key, filtered);
      }
    }
  }

  startListening(): void {
    if (!this.isListening) {
      document.addEventListener('keydown', this.handleKeyDown);
      this.isListening = true;
      console.log('[KeyboardShortcut] Started listening for shortcuts');
    }
  }

  stopListening(): void {
    if (this.isListening) {
      document.removeEventListener('keydown', this.handleKeyDown);
      this.isListening = false;
      console.log('[KeyboardShortcut] Stopped listening for shortcuts');
    }
  }

  getRegisteredShortcuts(): ShortcutHandler[] {
    const allShortcuts: ShortcutHandler[] = [];
    this.handlers.forEach(handlers => {
      allShortcuts.push(...handlers);
    });
    return allShortcuts;
  }

  clear(): void {
    this.handlers.clear();
    console.log('[KeyboardShortcut] Cleared all shortcuts');
  }
}

// Export singleton instance
export const keyboardShortcuts = KeyboardShortcutService.getInstance();
