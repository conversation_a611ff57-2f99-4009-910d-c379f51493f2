# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# Electron build outputs
release/
app/dist/
packages/*/dist/
packages/*/build/

# TypeScript
*.tsbuildinfo
.tscache/

# Webpack
.webpack/

# Prisma
/src/generated/prisma
prisma/migrations/*/migration.sql.bak

# Database
*.db
*.sqlite
*.sqlite3
dev.db*
test.db*
database.db*

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt/
dist/

# Gatsby files
.cache/
public/

# Storybook build outputs
.out/
.storybook-out/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Electron
# Electron build outputs
app/dist/
release/

# Electron cache
.electron/
electron-builder-cache/

# Test coverage
coverage/
.nyc_output/

# Jest
jest_coverage/

# Cypress
cypress/videos/
cypress/screenshots/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Local development
.local/
.cache/

# Backup files
*.bak
*.backup
*.old
*.orig

# Archive files
*.zip
*.tar.gz
*.rar

# Package files
*.dmg
*.pkg
*.deb
*.rpm
*.msi
*.exe
*.app

# API keys and secrets (additional patterns)
secrets.json
config/secrets.json
.secrets
*.pem
*.key
*.crt
*.p12

# Development tools
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.history/

# Webpack bundle analyzer
bundle-analyzer-report.html

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Husky
.husky/_/

# Auto-generated files
src/types/generated/
generated/
auto-generated/

# Lock files (choose one based on your package manager)
# Uncomment the one you DON'T use:
# package-lock.json  # if using yarn
# yarn.lock          # if using npm
# pnpm-lock.yaml     # if using npm or yarn