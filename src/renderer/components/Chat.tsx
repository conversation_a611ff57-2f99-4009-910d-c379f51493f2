import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface ChatMessage {
  id: number;
  sessionId: number;
  workspaceId: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: string[];
  metadata?: any;
  generatedRequest?: any;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatSession {
  id: number;
  title: string;
  workspaceId: number;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'ollama' | 'gemini' | 'anthropic' | 'siliconflow' | 'alibaba' | 'custom';
  baseUrl?: string;
  apiKey?: string;
  models: string[];
}

interface ChatProps {
  workspaceId: number;
  onRequestGenerated?: (request: any) => void;
  onClose?: () => void;
  onShowToast?: (message: string) => void;
}

const Chat: React.FC<ChatProps> = ({ workspaceId, onRequestGenerated, onClose, onShowToast }) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [activeSession, setActiveSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isComposing, setIsComposing] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    loadSessions();
    loadProviders();
  }, [workspaceId]);

  // Listen for settings changes to update providers
  useEffect(() => {
    const handleSettingsChanged = async (event: any) => {
      // Reload providers from the AI service to get the latest data
      await window.electronAPI.ai.reloadProviders();
      await loadProviders();

      // If this is an Ollama provider update with specific provider info
      if (event.detail?.provider) {
        const { provider, isOllama, defaultModel } = event.detail;

        if (isOllama && defaultModel) {
          // Switch to the updated Ollama provider and its selected model
          setSelectedProvider(provider.id);
          setSelectedModel(defaultModel);
        }
      }
    };

    window.addEventListener('ai-settings-changed', handleSettingsChanged);
    return () => window.removeEventListener('ai-settings-changed', handleSettingsChanged);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, [inputMessage]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadSessions = async () => {
    try {
      const sessionsData = await window.electronAPI.chat.getSessions(workspaceId);
      setSessions(sessionsData);
      
      if (sessionsData.length > 0 && !activeSession) {
        setActiveSession(sessionsData[0]);
        loadMessages(sessionsData[0].id);
      }
    } catch (err) {
      console.error('Error loading sessions:', err);
    }
  };

  const loadProviders = async () => {
    try {
      const providersData = await window.electronAPI.ai.getProviders();
      setProviders(providersData);

      // Get AI settings to determine default provider and model
      const aiSettings = await window.electronAPI.settings.getAI();

      if (providersData.length > 0) {
        // Use default provider if available, otherwise use first provider
        const defaultProvider = aiSettings.defaultProvider || providersData[0].id;
        const provider = providersData.find(p => p.id === defaultProvider) || providersData[0];

        setSelectedProvider(provider.id);

        // Use default model if available and belongs to the selected provider
        const defaultModel = aiSettings.defaultModel;
        if (defaultModel && provider.models.includes(defaultModel)) {
          setSelectedModel(defaultModel);
        } else {
          setSelectedModel(provider.models[0] || '');
        }
      }
    } catch (err) {
      console.error('Error loading providers:', err);
    }
  };

  const loadMessages = async (sessionId: number) => {
    try {
      const messagesData = await window.electronAPI.chat.getMessages(sessionId);
      setMessages(messagesData);
    } catch (err) {
      console.error('Error loading messages:', err);
    }
  };

  const createNewSession = async () => {
    try {
      const newSession = await window.electronAPI.chat.createSession({
        title: 'New Chat',
        workspaceId
      });
      
      setSessions(prev => [newSession, ...prev]);
      setActiveSession(newSession);
      setMessages([]);
    } catch (err) {
      console.error('Failed to create new session:', err);
      onShowToast?.('Failed to create new chat session');
    }
  };

  const deleteSession = async (sessionId: number) => {
    if (!confirm('Are you sure you want to delete this chat session?')) return;

    try {
      await window.electronAPI.chat.deleteSession(sessionId);
      setSessions(prev => prev.filter(s => s.id !== sessionId));
      
      if (activeSession?.id === sessionId) {
        const remainingSessions = sessions.filter(s => s.id !== sessionId);
        if (remainingSessions.length > 0) {
          setActiveSession(remainingSessions[0]);
          loadMessages(remainingSessions[0].id);
        } else {
          setActiveSession(null);
          setMessages([]);
        }
      }
    } catch (err) {
      console.error('Failed to delete session:', err);
      onShowToast?.('Failed to delete chat session');
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const maxFileSize = 10 * 1024 * 1024; // 10MB
    const maxImages = 5;

    if (selectedImages.length + files.length > maxImages) {
      onShowToast?.(`You can only attach up to ${maxImages} images at once.`);
      event.target.value = '';
      return;
    }

    Array.from(files).forEach(file => {
      if (!file.type.startsWith('image/')) {
        onShowToast?.(`"${file.name}" is not a valid image file.`);
        return;
      }

      if (file.size > maxFileSize) {
        onShowToast?.(`"${file.name}" is too large. Maximum file size is 10MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setSelectedImages(prev => [...prev, result]);
      };
      reader.onerror = () => {
        onShowToast?.(`Failed to read image file "${file.name}".`);
      };
      reader.readAsDataURL(file);
    });

    // Clear the input
    event.target.value = '';
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() && selectedImages.length === 0) return;
    if (!activeSession) {
      await createNewSession();
      return;
    }

    const provider = providers.find(p => p.id === selectedProvider);
    if (!provider) {
      onShowToast?.('No AI provider selected. Please configure an AI provider in settings.');
      return;
    }

    try {
      // Only show loading for non-Ollama providers
      const isOllama = provider.type === 'ollama';
      if (!isOllama) {
        setLoading(true);
      }
      setError(null);

      // Add user message
      const userMessage = await window.electronAPI.chat.addMessage({
        sessionId: activeSession.id,
        workspaceId,
        role: 'user',
        content: inputMessage,
        images: selectedImages
      });

      setMessages(prev => [...prev, userMessage]);
      const currentInputMessage = inputMessage;
      setInputMessage('');
      setSelectedImages([]);

      // Prepare AI request
      const aiRequest = {
        messages: [...messages, userMessage].map(msg => ({
          role: msg.role,
          content: msg.content,
          images: msg.images
        })),
        model: selectedModel,
        provider,
        temperature: 0.7,
        maxTokens: 2048
      };

      if (isOllama) {
        // Handle Ollama streaming
        let streamingContent = '';
        let streamingMessageId: number | null = null;

        // Create initial streaming message
        const initialStreamMessage = await window.electronAPI.chat.addMessage({
          sessionId: activeSession.id,
          workspaceId,
          role: 'assistant',
          content: '',
          metadata: null,
          generatedRequest: null
        });

        streamingMessageId = initialStreamMessage.id;
        setMessages(prev => [...prev, initialStreamMessage]);

        // Set up stream chunk listener
        window.electronAPI.ai.onStreamChunk((chunk: any) => {
          if (!chunk.done) {
            streamingContent += chunk.content;
            // Update the streaming message in real-time
            setMessages(prev => prev.map(msg =>
              msg.id === streamingMessageId
                ? { ...msg, content: streamingContent }
                : msg
            ));
          }
        });

        try {
          // Send streaming request
          const aiResponse = await window.electronAPI.ai.sendStreamMessage(aiRequest);

          // Update final message with complete content and metadata
          const finalMessage = await window.electronAPI.chat.updateMessage(streamingMessageId, {
            content: aiResponse.content,
            metadata: aiResponse.usage,
            generatedRequest: aiResponse.generatedRequest
          });

          setMessages(prev => prev.map(msg =>
            msg.id === streamingMessageId ? finalMessage : msg
          ));

        } finally {
          // Clean up stream listener
          window.electronAPI.ai.removeStreamChunkListener();
        }
      } else {
        // Handle non-streaming providers
        const aiResponse = await window.electronAPI.ai.sendMessage(aiRequest);

        // Add assistant message
        const assistantMessage = await window.electronAPI.chat.addMessage({
          sessionId: activeSession.id,
          workspaceId,
          role: 'assistant',
          content: aiResponse.content,
          metadata: aiResponse.usage,
          generatedRequest: aiResponse.generatedRequest
        });

        setMessages(prev => [...prev, assistantMessage]);
      }

      // Update session title if it's the first message
      if (messages.length === 0) {
        const title = currentInputMessage.slice(0, 50) + (currentInputMessage.length > 50 ? '...' : '');
        await window.electronAPI.chat.updateSession(activeSession.id, title);
        setSessions(prev => prev.map(s =>
          s.id === activeSession.id ? { ...s, title } : s
        ));
      }

    } catch (err) {
      console.error('Error sending message:', err);

      // Show specific error message based on the error type
      let errorMessage = 'Failed to send message';
      if (err instanceof Error) {
        if (err.message.includes('Ollama server is not running')) {
          errorMessage = 'Ollama server is not running. Please start Ollama and try again.';
        } else if (err.message.includes('Model') && err.message.includes('not found')) {
          errorMessage = 'Selected model not found in Ollama. Please check if the model is installed.';
        } else if (err.message.includes('502') || err.message.includes('Bad Gateway')) {
          errorMessage = 'Cannot connect to Ollama server. Please ensure Ollama is running on the correct port.';
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and AI provider settings.';
        } else if (err.message.includes('unauthorized') || err.message.includes('401')) {
          errorMessage = 'Authentication failed. Please check your API key in settings.';
        } else if (err.message.includes('quota') || err.message.includes('limit')) {
          errorMessage = 'API quota exceeded. Please check your usage limits.';
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (err.message) {
          errorMessage = `Error: ${err.message}`;
        }
      }

      onShowToast?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle Enter key
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      if (inputMessage.trim() || selectedImages.length > 0) {
        sendMessage();
      }
    }

    // Handle Ctrl/Cmd + Enter for force send
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (inputMessage.trim() || selectedImages.length > 0) {
        sendMessage();
      }
    }

    // Handle Escape to clear input
    if (e.key === 'Escape') {
      setInputMessage('');
      setSelectedImages([]);
      textareaRef.current?.blur();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputMessage(e.target.value);
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  const clearInput = () => {
    setInputMessage('');
    setSelectedImages([]);
    textareaRef.current?.focus();
  };

  const pasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInputMessage(prev => prev + text);
      textareaRef.current?.focus();
    } catch (err) {
      console.error('Failed to read clipboard:', err);
    }
  };

  const applyGeneratedRequest = (request: any) => {
    if (onRequestGenerated) {
      onRequestGenerated(request);
    }
  };

  return (
    <div className="chat-panel-container">
      <div className="chat-panel-header">
        <h3>🤖 AI Assistant</h3>
        <div className="chat-header-actions">
          <div className="chat-session-dropdown">
            <select
              value={activeSession?.id || ''}
              onChange={(e) => {
                const sessionId = parseInt(e.target.value);
                const session = sessions.find(s => s.id === sessionId);
                if (session) {
                  setActiveSession(session);
                  loadMessages(session.id);
                }
              }}
              className="session-selector"
            >
              <option value="">Select conversation...</option>
              {sessions.map(session => (
                <option key={session.id} value={session.id}>
                  {session.title}
                </option>
              ))}
            </select>
          </div>
          <button className="btn-icon btn-primary" onClick={createNewSession} title="New Chat">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
          </button>
          {onClose && (
            <button className="btn-icon btn-secondary" onClick={onClose} title="Close">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          )}
        </div>
      </div>

      <div className="chat-panel-content">
        {/* Show recent conversations in a compact list */}
        {sessions.length > 1 && (
          <div className="chat-conversations">
            <div className="conversations-header">Recent Conversations</div>
            <div className="conversations-list">
              {sessions.slice(0, 5).map(session => (
                <div
                  key={session.id}
                  className={`conversation-item ${activeSession?.id === session.id ? 'active' : ''}`}
                  onClick={() => {
                    setActiveSession(session);
                    loadMessages(session.id);
                  }}
                >
                  <div className="conversation-title">{session.title}</div>
                  <button
                    className="conversation-delete"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                    title="Delete conversation"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <line x1="18" y1="6" x2="6" y2="18"/>
                      <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="chat-main">
        <div className="chat-controls">
          <div className="provider-selector">
            <select
              value={selectedProvider}
              onChange={(e) => {
                setSelectedProvider(e.target.value);
                const provider = providers.find(p => p.id === e.target.value);
                if (provider && provider.models.length > 0) {
                  setSelectedModel(provider.models[0]);
                }
              }}
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>
          </div>

          <div className="model-selector">
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
            >
              {providers.find(p => p.id === selectedProvider)?.models.map(model => (
                <option key={model} value={model}>
                  {model}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="chat-messages">
          {messages.map(message => (
            <div key={message.id} className={`message ${message.role}`}>
              <div className="message-header">
                <span className="message-role">
                  {message.role === 'user' ? 'You' : 'AI'}
                </span>
                <span className="message-time">
                  {new Date(message.createdAt).toLocaleTimeString()}
                </span>
              </div>
              
              <div className="message-content">
                {message.images && message.images.length > 0 && (
                  <div className="message-images">
                    {message.images.map((image, index) => (
                      <img key={index} src={image} alt="Uploaded" className="message-image" />
                    ))}
                  </div>
                )}
                
                <ReactMarkdown
                  components={{
                    code: ({ node, inline, className, children, ...props }: any) => {
                      const match = /language-(\w+)/.exec(className || '');
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={tomorrow}
                          language={match[1]}
                          PreTag="div"
                          {...props}
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      );
                    }
                  }}
                >
                  {message.content}
                </ReactMarkdown>

                {message.generatedRequest && (
                  <div className="generated-request">
                    <div className="request-header">
                      <span>Generated Request:</span>
                      <button
                        className="btn-primary btn-small"
                        onClick={() => applyGeneratedRequest(message.generatedRequest)}
                      >
                        Apply Request
                      </button>
                    </div>
                    <pre className="request-json">
                      {JSON.stringify(message.generatedRequest, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {loading && (
            <div className="message assistant">
              <div className="message-header">
                <span className="message-role">AI</span>
              </div>
              <div className="message-content">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        <div className="chat-input">
          {selectedImages.length > 0 && (
            <div className="selected-images">
              <div className="selected-images-header">
                <span className="images-count">{selectedImages.length} image{selectedImages.length > 1 ? 's' : ''} selected</span>
                <button
                  className="clear-images-btn"
                  onClick={() => setSelectedImages([])}
                  title="Clear all images"
                >
                  Clear all
                </button>
              </div>
              <div className="selected-images-grid">
                {selectedImages.map((image, index) => (
                  <div key={index} className="selected-image">
                    <img src={image} alt={`Selected image ${index + 1}`} />
                    <button
                      className="remove-image-btn"
                      onClick={() => removeImage(index)}
                      title="Remove image"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Main input row */}
          <div className="input-main-row">
            <div className="input-field-container">
              <textarea
                ref={textareaRef}
                value={inputMessage}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onCompositionStart={handleCompositionStart}
                onCompositionEnd={handleCompositionEnd}
                placeholder="Ask AI to help with your API requests..."
                disabled={loading}
                rows={1}
                className="chat-textarea"
              />

              {inputMessage && (
                <button
                  className="clear-input-btn"
                  onClick={clearInput}
                  title="Clear input (Esc)"
                  disabled={loading}
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                  </svg>
                </button>
              )}
            </div>

            <button
              className="send-btn"
              onClick={sendMessage}
              disabled={loading || (!inputMessage.trim() && selectedImages.length === 0)}
              title={loading ? 'Sending...' : 'Send message (Enter)'}
            >
              {loading ? (
                <div className="loading-spinner">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                </div>
              ) : (
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="send-icon">
                  <path d="M22 2L11 13"/>
                  <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                </svg>
              )}
            </button>
          </div>

          {/* Bottom actions row */}
          <div className="input-bottom-actions">
            <div className="action-buttons">
              <button
                className="action-btn attachment-btn"
                onClick={() => fileInputRef.current?.click()}
                title="Attach images"
                disabled={loading}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"/>
                </svg>
              </button>

              <button
                className="action-btn paste-btn"
                onClick={pasteFromClipboard}
                title="Paste from clipboard"
                disabled={loading}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                </svg>
              </button>
            </div>

            <div className="input-info">
              {(inputMessage.length > 0 || selectedImages.length > 0) && (
                <div className="input-stats">
                  {inputMessage.length > 0 && (
                    <span className="char-count">{inputMessage.length} chars</span>
                  )}
                  {selectedImages.length > 0 && (
                    <span className="image-count">{selectedImages.length} image{selectedImages.length > 1 ? 's' : ''}</span>
                  )}
                </div>
              )}

              <div className="input-hints">
                <kbd>↵</kbd> Send • <kbd>Shift</kbd>+<kbd>↵</kbd> New line
              </div>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            style={{ display: 'none' }}
            onChange={handleImageUpload}
          />
        </div>
        </div>
      </div>
    </div>
  );
};

export default Chat;
