import React, { useState, useCallback } from 'react';

interface Variable {
  name: string;
  initialValue: string;
  currentValue: string;
  type?: 'string' | 'number' | 'boolean' | 'secret';
  description?: string;
}

interface RequestExecutorData {
  id: string;
  name: string;
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
  preScript?: string;
  testScript?: string;
}

interface RequestResult {
  id: string;
  name: string;
  status: 'success' | 'error' | 'timeout';
  statusCode?: number;
  statusText?: string;
  responseTime: number;
  responseSize: number;
  responseHeaders: Record<string, string>;
  responseBody: any;
  error?: string;
  testResults?: TestResult[];
  cookies?: Cookie[];
}

interface TestResult {
  name: string;
  status: 'pass' | 'fail';
  message?: string;
}

interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: string;
  httpOnly?: boolean;
  secure?: boolean;
}

interface UseRequestExecutorProps {
  onRequestResult: (result: RequestResult) => void;
  onError: (error: string) => void;
}

const useRequestExecutor = ({
  onRequestResult,
  onError
}: UseRequestExecutorProps) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionProgress, setExecutionProgress] = useState<{
    current: number;
    total: number;
    currentRequest?: string;
  }>({ current: 0, total: 0 });

  /**
   * Execute a single request
   */
  const executeRequest = useCallback(async (
    requestData: RequestExecutorData,
    environmentVariables: Record<string, Variable> = {},
    collectionVariables: Record<string, Variable> = {},
    workspaceId?: number,
    environmentName?: string
  ): Promise<RequestResult> => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    setIsExecuting(true);
    setExecutionProgress({ current: 1, total: 1, currentRequest: requestData.name });

    try {
      const result = await window.electronAPI.request.execute(
        requestData,
        environmentVariables,
        collectionVariables,
        workspaceId,
        environmentName
      );

      onRequestResult(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError(errorMessage);
      throw error;
    } finally {
      setIsExecuting(false);
      setExecutionProgress({ current: 0, total: 0 });
    }
  }, [onRequestResult, onError]);

  /**
   * Execute a collection of requests
   */
  const executeCollection = useCallback(async (
    requests: RequestExecutorData[],
    environmentVariables: Record<string, Variable> = {},
    collectionVariables: Record<string, Variable> = {},
    options: {
      stopOnError?: boolean;
      delay?: number;
      timeout?: number;
    } = {}
  ): Promise<RequestResult[]> => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    setIsExecuting(true);
    setExecutionProgress({ current: 0, total: requests.length });

    try {
      // Set timeout if specified
      if (options.timeout) {
        await window.electronAPI.request.setTimeout(options.timeout);
      }

      const results = await window.electronAPI.collection.run(
        requests,
        environmentVariables,
        collectionVariables,
        options
      );

      // Report each result
      results.forEach((result: RequestResult) => {
        onRequestResult(result);
      });

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError(errorMessage);
      throw error;
    } finally {
      setIsExecuting(false);
      setExecutionProgress({ current: 0, total: 0 });
    }
  }, [onRequestResult, onError]);

  /**
   * Get current variable state from the runner
   */
  const getVariableState = useCallback(async () => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      return await window.electronAPI.request.getVariableState();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get variable state';
      onError(errorMessage);
      throw error;
    }
  }, [onError]);

  /**
   * Format response size for display
   */
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Format response time for display
   */
  const formatTime = (ms: number): string => {
    if (ms < 1000) {
      return `${ms}ms`;
    } else {
      return `${(ms / 1000).toFixed(2)}s`;
    }
  };

  /**
   * Get status color based on HTTP status code
   */
  const getStatusColor = (statusCode?: number): string => {
    if (!statusCode) return '#6c757d'; // gray for unknown
    if (statusCode >= 200 && statusCode < 300) return '#28a745'; // green for success
    if (statusCode >= 300 && statusCode < 400) return '#ffc107'; // yellow for redirect
    if (statusCode >= 400 && statusCode < 500) return '#fd7e14'; // orange for client error
    if (statusCode >= 500) return '#dc3545'; // red for server error
    return '#6c757d'; // gray for other
  };

  return {
    // Execution methods
    executeRequest,
    executeCollection,
    getVariableState,

    // State
    isExecuting,
    executionProgress,

    // Utility methods
    formatSize,
    formatTime,
    getStatusColor
  };
};

export default useRequestExecutor;
export type { RequestExecutorData, RequestResult, TestResult, Cookie, Variable, UseRequestExecutorProps };
