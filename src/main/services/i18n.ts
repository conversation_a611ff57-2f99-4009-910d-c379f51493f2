import * as fs from 'fs';
import * as path from 'path';

export type SupportedLanguage = 'en' | 'es' | 'fr' | 'de' | 'zh' | 'ja';

export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
}

export interface TranslationData {
  [key: string]: string | TranslationData;
}

export class I18nService {
  private currentLanguage: SupportedLanguage = 'en';
  private translations: Map<SupportedLanguage, TranslationData> = new Map();
  private fallbackLanguage: SupportedLanguage = 'en';

  constructor() {
    this.loadTranslations();
  }

  async setLanguage(language: SupportedLanguage): Promise<void> {
    if (this.isLanguageSupported(language)) {
      this.currentLanguage = language;
      await this.saveLanguagePreference();
    } else {
      throw new Error(`Language ${language} is not supported`);
    }
  }

  getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  getSupportedLanguages(): LanguageInfo[] {
    return [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'es', name: 'Spanish', nativeName: 'Español' },
      { code: 'fr', name: 'French', nativeName: 'Français' },
      { code: 'de', name: 'German', nativeName: 'Deutsch' },
      { code: 'zh', name: 'Chinese', nativeName: '中文' },
      { code: 'ja', name: 'Japanese', nativeName: '日本語' }
    ];
  }

  translate(key: string, params?: Record<string, string>): string {
    const translation = this.getTranslation(key, this.currentLanguage) || 
                       this.getTranslation(key, this.fallbackLanguage) || 
                       key;

    // Replace parameters in translation
    if (params) {
      return Object.entries(params).reduce((text, [param, value]) => {
        return text.replace(new RegExp(`{{${param}}}`, 'g'), value);
      }, translation);
    }

    return translation;
  }

  // Shorthand for translate
  t(key: string, params?: Record<string, string>): string {
    return this.translate(key, params);
  }

  private getTranslation(key: string, language: SupportedLanguage): string | null {
    const translations = this.translations.get(language);
    if (!translations) return null;

    // Support nested keys like 'menu.file.open'
    const keys = key.split('.');
    let current: any = translations;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }

    return typeof current === 'string' ? current : null;
  }

  private isLanguageSupported(language: string): language is SupportedLanguage {
    return this.getSupportedLanguages().some(lang => lang.code === language);
  }

  private async loadTranslations(): Promise<void> {
    const localesDir = path.join(__dirname, '../../renderer/locales');
    
    try {
      // Load default English translations
      this.translations.set('en', {
        app: {
          name: 'ApiCool',
          description: 'Advanced API Testing Tool'
        },
        menu: {
          file: 'File',
          edit: 'Edit',
          view: 'View',
          help: 'Help'
        },
        workspace: {
          create: 'Create Workspace',
          delete: 'Delete Workspace',
          rename: 'Rename Workspace'
        },
        request: {
          new: 'New Request',
          send: 'Send',
          save: 'Save',
          method: 'Method',
          url: 'URL',
          headers: 'Headers',
          body: 'Body',
          response: 'Response'
        },
        collection: {
          new: 'New Collection',
          import: 'Import Collection',
          export: 'Export Collection',
          run: 'Run Collection'
        },
        ai: {
          generate: 'Generate Request',
          prompt: 'Describe what you want to test...'
        },
        settings: {
          theme: 'Theme',
          language: 'Language',
          backup: 'Backup',
          ai: 'AI Settings'
        },
        common: {
          ok: 'OK',
          cancel: 'Cancel',
          save: 'Save',
          delete: 'Delete',
          edit: 'Edit',
          close: 'Close',
          loading: 'Loading...',
          error: 'Error',
          success: 'Success'
        }
      });

      // TODO: Load other language files from the locales directory
      // For now, we'll use English as the only language

    } catch (error) {
      console.error('Error loading translations:', error);
    }
  }

  async addTranslation(language: SupportedLanguage, translations: TranslationData): Promise<void> {
    this.translations.set(language, translations);
  }

  async saveLanguagePreference(): Promise<void> {
    // TODO: Save to user preferences/database
    console.log('Saving language preference:', this.currentLanguage);
  }

  async loadLanguagePreference(): Promise<void> {
    // TODO: Load from user preferences/database
    console.log('Loading language preference');
  }

  // Get all translations for a specific language (useful for frontend)
  getTranslations(language?: SupportedLanguage): TranslationData | null {
    const lang = language || this.currentLanguage;
    return this.translations.get(lang) || null;
  }

  // Format numbers according to locale
  formatNumber(number: number): string {
    try {
      return new Intl.NumberFormat(this.getLocaleCode()).format(number);
    } catch {
      return number.toString();
    }
  }

  // Format dates according to locale
  formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
    try {
      return new Intl.DateTimeFormat(this.getLocaleCode(), options).format(date);
    } catch {
      return date.toLocaleDateString();
    }
  }

  private getLocaleCode(): string {
    const localeMap: Record<SupportedLanguage, string> = {
      'en': 'en-US',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'zh': 'zh-CN',
      'ja': 'ja-JP'
    };

    return localeMap[this.currentLanguage] || 'en-US';
  }
}
