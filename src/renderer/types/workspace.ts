export interface WorkspaceItem {
  id: string;
  name: string;
  type: 'collection' | 'folder' | 'request' | 'example';
  parentId?: string;
  children?: WorkspaceItem[];
  order: number;
  
  // Request-specific properties
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';
  url?: string;
  headers?: Record<string, string>;
  params?: Record<string, string>;
  body?: string;
  hasUnsavedChanges?: boolean;
  
  // Folder/Collection properties
  expanded?: boolean;
  description?: string;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkspaceData {
  id: string;
  name: string;
  items: WorkspaceItem[];
}

export interface DragDropItem {
  id: string;
  type: 'collection' | 'folder' | 'request' | 'example';
  name: string;
  parentId?: string;
}

export const HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'] as const;

export const getMethodColor = (method: string): string => {
  switch (method) {
    case 'GET': return '#28a745';
    case 'POST': return '#007bff';
    case 'PUT': return '#ffc107';
    case 'DELETE': return '#dc3545';
    case 'PATCH': return '#6f42c1';
    case 'HEAD': return '#17a2b8';
    case 'OPTIONS': return '#6c757d';
    default: return '#6c757d';
  }
};

export const getItemIcon = (item: WorkspaceItem): string => {
  switch (item.type) {
    case 'collection': return '📚';
    case 'folder': return item.expanded ? '📂' : '📁';
    case 'request': return '🔗';
    case 'example': return '📄';
    default: return '📄';
  }
};
