import {
  ImportResult,
  Imported<PERSON>oll<PERSON>tion,
  ImportedFolder,
  ImportedRequest,
  ImportedExample,
  ImportedAuth,
  PostmanCollection,
  PostmanItem,
  PostmanRequest,
  PostmanResponse,
  PostmanAuth,
  PostmanUrl,
  PostmanHeader,
  PostmanVariable
} from '../../types/import-export';

export class PostmanImporter {
  static async importCollection(fileContent: string): Promise<ImportResult> {
    try {
      const postmanData = JSON.parse(fileContent) as PostmanCollection;
      
      if (!this.isValidPostmanCollection(postmanData)) {
        return {
          success: false,
          collections: [],
          errors: ['Invalid Postman collection format'],
          warnings: []
        };
      }

      const collection = this.convertPostmanCollection(postmanData);
      
      return {
        success: true,
        collections: [collection],
        errors: [],
        warnings: []
      };
    } catch (error) {
      return {
        success: false,
        collections: [],
        errors: [`Failed to parse Postman collection: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  private static isValidPostmanCollection(data: any): data is PostmanCollection {
    return (
      data &&
      typeof data === 'object' &&
      data.info &&
      typeof data.info.name === 'string' &&
      Array.isArray(data.item)
    );
  }

  private static convertPostmanCollection(postman: PostmanCollection): ImportedCollection {
    const collection: ImportedCollection = {
      id: this.generateId(),
      name: postman.info.name,
      description: postman.info.description || '',
      version: postman.info.version || '1.0.0',
      folders: [],
      requests: [],
      variables: this.convertVariables(postman.variable || []),
      auth: this.convertAuth(postman.auth)
    };

    // Process items (folders and requests)
    this.processItems(postman.item, collection);

    return collection;
  }

  private static processItems(items: PostmanItem[], parent: ImportedCollection | ImportedFolder) {
    for (const item of items) {
      if (item.item) {
        // This is a folder
        const folder = this.convertFolder(item);
        if ('folders' in parent) {
          parent.folders.push(folder);
        } else {
          parent.subfolders.push(folder);
        }
      } else if (item.request) {
        // This is a request
        const request = this.convertRequest(item);
        parent.requests.push(request);
      }
    }
  }

  private static convertFolder(item: PostmanItem): ImportedFolder {
    const folder: ImportedFolder = {
      id: this.generateId(),
      name: item.name,
      description: item.description || '',
      requests: [],
      subfolders: [],
      variables: {},
      auth: undefined
    };

    if (item.item) {
      this.processItems(item.item, folder);
    }

    return folder;
  }

  private static convertRequest(item: PostmanItem): ImportedRequest {
    const request = item.request!;
    
    const importedRequest: ImportedRequest = {
      id: this.generateId(),
      name: item.name,
      description: item.description || request.description || '',
      method: request.method.toUpperCase(),
      url: this.convertUrl(request.url),
      headers: this.convertHeaders(request.header || []),
      params: this.extractQueryParams(request.url),
      body: this.convertBody(request.body),
      auth: this.convertAuth(request.auth),
      tests: '', // Postman tests would need special handling
      preRequestScript: '', // Postman pre-request scripts would need special handling
      examples: this.convertExamples(item.response || [], request)
    };

    return importedRequest;
  }

  private static convertUrl(url: PostmanUrl | string): string {
    if (typeof url === 'string') {
      return url;
    }
    
    return url.raw || '';
  }

  private static convertHeaders(headers: PostmanHeader[]): Record<string, string> {
    const result: Record<string, string> = {};
    
    for (const header of headers) {
      if (!header.disabled) {
        result[header.key] = header.value;
      }
    }
    
    return result;
  }

  private static extractQueryParams(url: PostmanUrl | string): Record<string, string> {
    const result: Record<string, string> = {};
    
    if (typeof url === 'object' && url.query) {
      for (const param of url.query) {
        if (!param.disabled) {
          result[param.key] = param.value;
        }
      }
    }
    
    return result;
  }

  private static convertBody(body: any): string {
    if (!body) return '';
    
    switch (body.mode) {
      case 'raw':
        return body.raw || '';
      case 'formdata':
        return this.convertFormData(body.formdata || []);
      case 'urlencoded':
        return this.convertUrlEncoded(body.urlencoded || []);
      default:
        return '';
    }
  }

  private static convertFormData(formdata: any[]): string {
    const data: Record<string, string> = {};
    for (const item of formdata) {
      if (!item.disabled && item.type === 'text') {
        data[item.key] = item.value;
      }
    }
    return JSON.stringify(data, null, 2);
  }

  private static convertUrlEncoded(urlencoded: any[]): string {
    const data: Record<string, string> = {};
    for (const item of urlencoded) {
      if (!item.disabled) {
        data[item.key] = item.value;
      }
    }
    return JSON.stringify(data, null, 2);
  }

  private static convertAuth(auth?: PostmanAuth): ImportedAuth | undefined {
    if (!auth) return undefined;

    switch (auth.type) {
      case 'bearer':
        const bearerToken = auth.bearer?.find(item => item.key === 'token')?.value;
        return bearerToken ? {
          type: 'bearer',
          bearer: { token: bearerToken }
        } : undefined;

      case 'basic':
        const username = auth.basic?.find(item => item.key === 'username')?.value;
        const password = auth.basic?.find(item => item.key === 'password')?.value;
        return (username && password) ? {
          type: 'basic',
          basic: { username, password }
        } : undefined;

      case 'apikey':
        const key = auth.apikey?.find(item => item.key === 'key')?.value;
        const value = auth.apikey?.find(item => item.key === 'value')?.value;
        const inLocation = auth.apikey?.find(item => item.key === 'in')?.value as 'header' | 'query';
        return (key && value) ? {
          type: 'api-key',
          apikey: { key, value, in: inLocation || 'header' }
        } : undefined;

      default:
        return { type: 'none' };
    }
  }

  private static convertVariables(variables: PostmanVariable[]): Record<string, string> {
    const result: Record<string, string> = {};
    
    for (const variable of variables) {
      if (!variable.disabled) {
        result[variable.key] = variable.value;
      }
    }
    
    return result;
  }

  private static convertExamples(responses: PostmanResponse[], request: PostmanRequest): ImportedExample[] {
    return responses.map(response => ({
      id: this.generateId(),
      name: response.name,
      description: '',
      request: {
        method: request.method,
        url: this.convertUrl(request.url),
        headers: this.convertHeaders(request.header || []),
        body: this.convertBody(request.body)
      },
      response: {
        status: response.code,
        statusText: response.status,
        headers: this.convertHeaders(response.header || []),
        body: response.body,
        responseTime: 0
      }
    }));
  }

  private static generateId(): string {
    return 'imported_' + Math.random().toString(36).substr(2, 9);
  }
}

export default PostmanImporter;
