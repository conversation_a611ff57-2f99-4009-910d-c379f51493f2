import React, { useState, useEffect } from 'react';
import { Workspace, WorkspaceStats } from '../../types/workspace-management';
import WorkspaceManager from '../../services/WorkspaceManager';

interface WorkspaceManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateWorkspace: () => void;
}

const WorkspaceManagementDialog: React.FC<WorkspaceManagementDialogProps> = ({
  isOpen,
  onClose,
  onCreateWorkspace
}) => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [activeWorkspace, setActiveWorkspace] = useState<Workspace | null>(null);
  const [editingWorkspace, setEditingWorkspace] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadWorkspaces();
    }
  }, [isOpen]);

  const loadWorkspaces = () => {
    const allWorkspaces = WorkspaceManager.getWorkspaces();
    const active = WorkspaceManager.getActiveWorkspace();
    setWorkspaces(allWorkspaces);
    setActiveWorkspace(active);
  };

  const handleSetActive = (workspace: Workspace) => {
    WorkspaceManager.setActiveWorkspace(workspace.id);
    setActiveWorkspace(workspace);
  };

  const handleStartEdit = (workspace: Workspace) => {
    setEditingWorkspace(workspace.id);
    setEditName(workspace.name);
    setEditDescription(workspace.description || '');
  };

  const handleSaveEdit = () => {
    if (!editingWorkspace) return;

    const success = WorkspaceManager.updateWorkspace(editingWorkspace, {
      name: editName,
      description: editDescription
    });

    if (success) {
      loadWorkspaces();
      setEditingWorkspace(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingWorkspace(null);
    setEditName('');
    setEditDescription('');
  };

  const handleDelete = (workspaceId: string) => {
    const success = WorkspaceManager.deleteWorkspace(workspaceId);
    if (success) {
      loadWorkspaces();
      setConfirmDelete(null);
    }
  };

  const handleDuplicate = (workspace: Workspace) => {
    const newName = `${workspace.name} (Copy)`;
    const duplicated = WorkspaceManager.duplicateWorkspace(workspace.id, newName);
    if (duplicated) {
      loadWorkspaces();
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!isOpen) return null;

  return (
    <div className="workspace-dialog-overlay">
      <div className="workspace-management-dialog">
        <div className="dialog-header">
          <h2>Manage Workspaces</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="dialog-content">
          <div className="workspace-management-header">
            <button
              className="btn btn-primary"
              onClick={() => {
                onClose();
                onCreateWorkspace();
              }}
            >
              ➕ Create New Workspace
            </button>
          </div>

          <div className="workspace-list-management">
            {workspaces.map(workspace => (
              <div
                key={workspace.id}
                className={`workspace-management-item ${
                  workspace.id === activeWorkspace?.id ? 'active' : ''
                }`}
              >
                <div className="workspace-item-header">
                  <div className="workspace-item-info">
                    {editingWorkspace === workspace.id ? (
                      <div className="workspace-edit-form">
                        <input
                          type="text"
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className="form-input workspace-name-input"
                          placeholder="Workspace name"
                        />
                        <textarea
                          value={editDescription}
                          onChange={(e) => setEditDescription(e.target.value)}
                          className="form-textarea workspace-desc-input"
                          placeholder="Description"
                          rows={2}
                        />
                        <div className="edit-actions">
                          <button
                            className="btn btn-sm btn-primary"
                            onClick={handleSaveEdit}
                            disabled={!editName.trim()}
                          >
                            Save
                          </button>
                          <button
                            className="btn btn-sm btn-secondary"
                            onClick={handleCancelEdit}
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="workspace-name-section">
                          <h3 className="workspace-item-name">
                            {workspace.name}
                            {workspace.isDefault && (
                              <span className="workspace-badge">Default</span>
                            )}
                            {workspace.id === activeWorkspace?.id && (
                              <span className="workspace-badge active-badge">Active</span>
                            )}
                          </h3>
                          {workspace.description && (
                            <p className="workspace-item-description">
                              {workspace.description}
                            </p>
                          )}
                        </div>
                        <div className="workspace-meta">
                          <div className="workspace-dates">
                            <div>Created: {formatDate(workspace.createdAt)}</div>
                            <div>Updated: {formatDate(workspace.updatedAt)}</div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  {editingWorkspace !== workspace.id && (
                    <div className="workspace-actions">
                      {workspace.id !== activeWorkspace?.id && (
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => handleSetActive(workspace)}
                        >
                          Set Active
                        </button>
                      )}
                      <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => handleStartEdit(workspace)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => handleDuplicate(workspace)}
                      >
                        Duplicate
                      </button>
                      {!workspace.isDefault && (
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => setConfirmDelete(workspace.id)}
                        >
                          Delete
                        </button>
                      )}
                    </div>
                  )}
                </div>

                {confirmDelete === workspace.id && (
                  <div className="delete-confirmation">
                    <p>Are you sure you want to delete "{workspace.name}"?</p>
                    <div className="confirmation-actions">
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => handleDelete(workspace.id)}
                      >
                        Yes, Delete
                      </button>
                      <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => setConfirmDelete(null)}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="dialog-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkspaceManagementDialog;
