import React from 'react';
import { Item } from '../../../utils/types';

interface TreeViewProps {
  items: Item[];
  selectedItemId: number | null;
  onItemSelect: (itemId: number) => void;
  workspaceId: number | null;
}

export const TreeView: React.FC<TreeViewProps> = ({
  items,
  selectedItemId,
  onItemSelect,
  workspaceId
}) => {
  const renderItem = (item: Item, level: number = 0) => {
    const isSelected = item.id === selectedItemId;
    const hasChildren = item.children && item.children.length > 0;
    
    const getItemName = () => {
      switch (item.type) {
        case 'COLLECTION':
          return item.collection?.name || 'Untitled Collection';
        case 'FOLDER':
          return item.folder?.name || 'Untitled Folder';
        case 'REQUEST':
          return item.request?.name || 'Untitled Request';
        case 'EXAMPLE':
          return item.example?.name || 'Untitled Example';
        default:
          return 'Unknown Item';
      }
    };

    const getItemIcon = () => {
      switch (item.type) {
        case 'COLLECTION':
          return '📁';
        case 'FOLDER':
          return '📂';
        case 'REQUEST':
          return '🔗';
        case 'EXAMPLE':
          return '📄';
        default:
          return '❓';
      }
    };

    return (
      <div key={item.id} className="tree-item-container">
        <div 
          className={`tree-item ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${level * 20 + 12}px` }}
          onClick={() => onItemSelect(item.id)}
        >
          <span className="item-icon">{getItemIcon()}</span>
          <span className="item-name">{getItemName()}</span>
          {item.type === 'REQUEST' && item.request && (
            <span className="request-method">{item.request.method}</span>
          )}
        </div>
        
        {hasChildren && (
          <div className="tree-children">
            {item.children!.map(child => renderItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!workspaceId) {
    return (
      <div className="tree-view">
        <div className="empty-state">
          <p>No workspace selected</p>
        </div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="tree-view">
        <div className="tree-header">
          <h3>Collections</h3>
          <button className="btn btn-ghost btn-sm">+</button>
        </div>
        <div className="empty-state">
          <p>No collections yet</p>
          <button className="btn btn-primary btn-sm">Create Collection</button>
        </div>
      </div>
    );
  }

  return (
    <div className="tree-view">
      <div className="tree-header">
        <h3>Collections</h3>
        <button className="btn btn-ghost btn-sm">+</button>
      </div>
      
      <div className="tree-content">
        {items.map(item => renderItem(item))}
      </div>
      

    </div>
  );
};
