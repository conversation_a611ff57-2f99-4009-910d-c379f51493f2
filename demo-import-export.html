<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import/Export System Demo - ApiCool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .demo-header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .features-section {
            padding: 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 30px;
            text-align: center;
            color: #333;
        }
        
        .import-export-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .feature-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            border: 1px solid #e9ecef;
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .panel-icon {
            font-size: 2em;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .panel-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .format-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin: 20px 0;
        }
        
        .format-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.2s;
        }
        
        .format-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .format-icon {
            font-size: 1.5em;
            margin-bottom: 8px;
        }
        
        .format-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .format-desc {
            font-size: 0.8em;
            color: #666;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-check {
            color: #28a745;
            font-weight: bold;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 40px;
            border-top: 1px solid #e9ecef;
        }
        
        .demo-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .demo-step {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px auto;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .step-desc {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .github-demo {
            background: linear-gradient(135deg, #24292e 0%, #40464e 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .github-demo h3 {
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .github-url {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 10px;
            color: white;
            font-family: monospace;
            margin: 10px 0;
        }
        
        .cta-section {
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .status-complete {
            background: #d4edda;
            color: #155724;
        }
        
        .status-progress {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>📥📤 Import/Export System</h1>
            <p>Seamlessly migrate and share API collections across platforms</p>
        </div>
        
        <div class="features-section">
            <h2 class="section-title">Universal Import & Export</h2>
            
            <div class="import-export-grid">
                <div class="feature-panel">
                    <div class="panel-header">
                        <div class="panel-icon">📥</div>
                        <h3 class="panel-title">Import Collections</h3>
                    </div>
                    
                    <div class="format-list">
                        <div class="format-item">
                            <div class="format-icon">🟠</div>
                            <div class="format-name">Postman</div>
                            <div class="format-desc">v2.1 Collections</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🟢</div>
                            <div class="format-name">OpenAPI</div>
                            <div class="format-desc">3.0 & Swagger</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🟣</div>
                            <div class="format-name">Insomnia</div>
                            <div class="format-desc">Export Format</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🔵</div>
                            <div class="format-name">HAR</div>
                            <div class="format-desc">HTTP Archive</div>
                        </div>
                    </div>
                    
                    <ul class="feature-list">
                        <li><span class="feature-check">✓</span> Drag & drop file import</li>
                        <li><span class="feature-check">✓</span> Multiple file processing</li>
                        <li><span class="feature-check">✓</span> Auto-format detection</li>
                        <li><span class="feature-check">✓</span> GitHub repository import</li>
                        <li><span class="feature-check">✓</span> Validation & error reporting</li>
                    </ul>
                </div>
                
                <div class="feature-panel">
                    <div class="panel-header">
                        <div class="panel-icon">📤</div>
                        <h3 class="panel-title">Export Collections</h3>
                    </div>
                    
                    <div class="format-list">
                        <div class="format-item">
                            <div class="format-icon">🟠</div>
                            <div class="format-name">Postman</div>
                            <div class="format-desc">v2.1 Compatible</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🟢</div>
                            <div class="format-name">OpenAPI</div>
                            <div class="format-desc">3.0 Spec</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🟣</div>
                            <div class="format-name">Insomnia</div>
                            <div class="format-desc">Native Format</div>
                        </div>
                        <div class="format-item">
                            <div class="format-icon">🔵</div>
                            <div class="format-name">HAR</div>
                            <div class="format-desc">Archive Format</div>
                        </div>
                    </div>
                    
                    <ul class="feature-list">
                        <li><span class="feature-check">✓</span> Configurable export options</li>
                        <li><span class="feature-check">✓</span> Include/exclude examples</li>
                        <li><span class="feature-check">✓</span> Include/exclude tests</li>
                        <li><span class="feature-check">✓</span> Include/exclude variables</li>
                        <li><span class="feature-check">✓</span> Include/exclude auth</li>
                    </ul>
                </div>
            </div>
            
            <div class="github-demo">
                <h3>🐙 GitHub Integration</h3>
                <p>Import collections directly from GitHub repositories</p>
                <div class="github-url">https://github.com/owner/repo/path/to/collections</div>
                <p>Automatically detects and imports all collection files from public repositories</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="section-title">How It Works</h2>
            
            <div class="demo-steps">
                <div class="demo-step">
                    <div class="step-number">1</div>
                    <div class="step-title">Choose Import Source</div>
                    <div class="step-desc">Drag & drop files, browse local files, or enter GitHub repository URL</div>
                </div>
                
                <div class="demo-step">
                    <div class="step-number">2</div>
                    <div class="step-title">Auto-Detection</div>
                    <div class="step-desc">System automatically detects file format and validates structure</div>
                </div>
                
                <div class="demo-step">
                    <div class="step-number">3</div>
                    <div class="step-title">Smart Conversion</div>
                    <div class="step-desc">Converts to ApiCool format while preserving all metadata and structure</div>
                </div>
                
                <div class="demo-step">
                    <div class="step-number">4</div>
                    <div class="step-title">Integration</div>
                    <div class="step-desc">Seamlessly integrates imported collections into your workspace</div>
                </div>
            </div>
        </div>
        
        <div class="cta-section">
            <h2>🚀 Implementation Status</h2>
            <p>
                <strong>Postman Import/Export:</strong> <span class="status-badge status-complete">Complete</span><br>
                <strong>OpenAPI Import:</strong> <span class="status-badge status-complete">Complete</span><br>
                <strong>GitHub Integration:</strong> <span class="status-badge status-complete">Complete</span><br>
                <strong>Drag & Drop UI:</strong> <span class="status-badge status-complete">Complete</span><br>
                <strong>Export Options:</strong> <span class="status-badge status-complete">Complete</span><br>
                <strong>Insomnia & HAR:</strong> <span class="status-badge status-progress">Planned</span>
            </p>
            <a href="file:///d:/projects/ApiCool/dist/renderer/index.html" class="cta-button">
                🧪 Test Import/Export
            </a>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.querySelectorAll('.format-item').forEach(item => {
            item.addEventListener('click', function() {
                const formatName = this.querySelector('.format-name').textContent;
                alert(`${formatName} format selected!\n\nTry this in the main application by accessing File > Import or File > Export.`);
            });
        });

        // Simulate drag and drop
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
            document.body.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        });

        document.addEventListener('dragleave', function(e) {
            document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        });

        document.addEventListener('drop', function(e) {
            e.preventDefault();
            document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            alert('📁 Files dropped!\n\nIn the main application, this would trigger the import process.');
        });
    </script>
</body>
</html>
