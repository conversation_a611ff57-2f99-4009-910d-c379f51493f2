import React, { useState, useEffect } from 'react';

interface EnvironmentVariable {
  key: string;
  value: string;
  enabled: boolean;
  description?: string;
}

interface Environment {
  id: number;
  name: string;
  description?: string;
  variables: EnvironmentVariable[];
  isActive: boolean;
  workspaceId: number;
  createdAt: Date;
  updatedAt: Date;
}

interface EnvironmentsListProps {
  workspaceId: number;
  onOpenEnvironmentEditor: (environmentId: string, environmentName: string) => void;
}

const EnvironmentsList: React.FC<EnvironmentsListProps> = ({ 
  workspaceId, 
  onOpenEnvironmentEditor 
}) => {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [activeEnvironment, setActiveEnvironment] = useState<Environment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadEnvironments();
  }, [workspaceId]);

  const loadEnvironments = async () => {
    try {
      setLoading(true);
      const [envs, activeEnv] = await Promise.all([
        window.electronAPI.environment.getAll(workspaceId),
        window.electronAPI.environment.getActive(workspaceId)
      ]);
      setEnvironments(envs);
      setActiveEnvironment(activeEnv);
    } catch (err) {
      setError('Failed to load environments');
      console.error('Error loading environments:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSetActive = async (id: number) => {
    try {
      const updatedEnv = await window.electronAPI.environment.setActive(id, workspaceId);
      setActiveEnvironment(updatedEnv);
      setEnvironments(prev => prev.map(env => ({
        ...env,
        isActive: env.id === id
      })));

      // Emit event to sync with other components
      window.dispatchEvent(new CustomEvent('environment-activated', { detail: updatedEnv }));
    } catch (err) {
      setError('Failed to set active environment');
    }
  };

  const handleDeleteEnvironment = async (id: number) => {
    if (!confirm('Are you sure you want to delete this environment?')) return;

    try {
      await window.electronAPI.environment.delete(id);
      setEnvironments(prev => prev.filter(env => env.id !== id));
      if (activeEnvironment?.id === id) {
        setActiveEnvironment(null);
      }

      // Emit event to sync with other components
      window.dispatchEvent(new CustomEvent('environment-deleted', { detail: { id } }));
    } catch (err) {
      setError('Failed to delete environment');
    }
  };

  const handleDuplicateEnvironment = async (id: number) => {
    const originalEnv = environments.find(env => env.id === id);
    if (!originalEnv) return;

    const newName = prompt('Enter name for duplicated environment:', `${originalEnv.name} Copy`);
    if (!newName) return;

    try {
      const duplicatedEnv = await window.electronAPI.environment.duplicate(id, newName);
      setEnvironments(prev => [...prev, duplicatedEnv]);
    } catch (err) {
      setError('Failed to duplicate environment');
    }
  };

  const handleCreateNew = () => {
    onOpenEnvironmentEditor('new', 'New Environment');
  };

  if (loading) {
    return <div className="environments-list loading">Loading environments...</div>;
  }

  return (
    <div className="environments-list">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="environments-header">
        <h3>Environments</h3>
        <button className="btn-primary btn-small" onClick={handleCreateNew}>
          + New Environment
        </button>
      </div>

      <div className="environments-content">
        {environments.length === 0 ? (
          <div className="environments-empty">
            <p>No environments found</p>
            <button className="btn-primary" onClick={handleCreateNew}>
              Create First Environment
            </button>
          </div>
        ) : (
          <div className="environments-items">
            {environments.map(env => (
              <div
                key={env.id}
                className={`environment-item ${env.isActive ? 'active' : ''}`}
                onClick={() => onOpenEnvironmentEditor(env.id.toString(), env.name)}
                onDoubleClick={() => !env.isActive && handleSetActive(env.id)}
              >
                <div className="environment-info">
                  <div className="environment-header-row">
                    <div className="environment-name">
                      {env.name}
                      {env.isActive && <span className="active-badge">Active</span>}
                    </div>
                    <div className="environment-actions" onClick={(e) => e.stopPropagation()}>
                      <button
                        className="btn-icon btn-primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          onOpenEnvironmentEditor(env.id.toString(), env.name);
                        }}
                        title="Edit Environment"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                      </button>
                      {!env.isActive && (
                        <button
                          className="btn-icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetActive(env.id);
                          }}
                          title="Set as Active"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="20,6 9,17 4,12"/>
                          </svg>
                        </button>
                      )}
                      <button
                        className="btn-icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateEnvironment(env.id);
                        }}
                        title="Duplicate"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                      </button>
                      <button
                        className="btn-icon btn-danger"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteEnvironment(env.id);
                        }}
                        title="Delete"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="3,6 5,6 21,6"/>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  {env.description && (
                    <div className="environment-description">{env.description}</div>
                  )}
                  <div className="environment-variables-summary">
                    {env.variables.length} variable{env.variables.length !== 1 ? 's' : ''} 
                    ({env.variables.filter(v => v.enabled).length} enabled)
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnvironmentsList;
