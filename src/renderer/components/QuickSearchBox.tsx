import React, { useState, useEffect, useRef, useMemo } from 'react';
import { WorkspaceItem } from '../types/workspace';
import SearchService, { SearchResult } from '../services/SearchService';

interface QuickSearchBoxProps {
  isOpen: boolean;
  onClose: () => void;
  workspaceItems: WorkspaceItem[];
  onItemSelect: (item: SearchResult | Command) => void;
}

interface Command {
  id: string;
  type: 'command';
  name: string;
  description: string;
  icon: string;
  action: string;
}

const COMMANDS: Command[] = [
  {
    id: 'create-request',
    type: 'command',
    name: 'Create New Request',
    description: 'Create a new HTTP request',
    icon: '🔗',
    action: 'create-request'
  },
  {
    id: 'create-folder',
    type: 'command',
    name: 'Create New Folder',
    description: 'Create a new folder to organize requests',
    icon: '📁',
    action: 'create-folder'
  },
  {
    id: 'create-collection',
    type: 'command',
    name: 'Create New Collection',
    description: 'Create a new collection',
    icon: '📚',
    action: 'create-collection'
  },
  {
    id: 'launch-ai-chat',
    type: 'command',
    name: 'Launch AI Chat',
    description: 'Open AI assistant for help',
    icon: '🤖',
    action: 'launch-ai-chat'
  },
  {
    id: 'run-collection',
    type: 'command',
    name: 'Run Collection',
    description: 'Execute all requests in a collection',
    icon: '▶️',
    action: 'run-collection'
  },
  {
    id: 'import-postman',
    type: 'command',
    name: 'Import Postman Collection',
    description: 'Import from Postman format',
    icon: '📥',
    action: 'import-postman'
  },
  {
    id: 'export-collection',
    type: 'command',
    name: 'Export Collection',
    description: 'Export collection to Postman format',
    icon: '📤',
    action: 'export-collection'
  },
  {
    id: 'open-settings',
    type: 'command',
    name: 'Open Settings',
    description: 'Configure application settings',
    icon: '⚙️',
    action: 'open-settings'
  }
];

const QuickSearchBox: React.FC<QuickSearchBoxProps> = ({
  isOpen,
  onClose,
  workspaceItems,
  onItemSelect
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const isCommandMode = query.startsWith('/');
  const searchQuery = isCommandMode ? query.slice(1) : query;

  // State for async search results
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Filter items based on search query
  const filteredItems = useMemo(() => {
    if (isCommandMode) {
      return COMMANDS.filter(command =>
        command.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        command.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else {
      return searchResults;
    }
  }, [isCommandMode, searchQuery, searchResults]);

  // Load search results asynchronously
  useEffect(() => {
    if (isCommandMode) return;

    const loadSearchResults = async () => {
      setIsLoading(true);
      try {
        let results: SearchResult[];
        if (!searchQuery.trim()) {
          // Show recent items when no query
          results = await SearchService.getRecentItems(10);
        } else {
          // Search all items across workspaces
          results = await SearchService.searchAllItems(searchQuery);
        }
        setSearchResults(results);
      } catch (error) {
        console.error('Failed to load search results:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadSearchResults();
  }, [searchQuery, isCommandMode]);

  // Reset selection when items change
  useEffect(() => {
    setSelectedIndex(0);
  }, [filteredItems]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, filteredItems.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredItems[selectedIndex]) {
            onItemSelect(filteredItems[selectedIndex]);
            onClose();
            setQuery('');
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredItems, onItemSelect, onClose]);

  // Scroll selected item into view
  useEffect(() => {
    if (listRef.current) {
      const selectedElement = listRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  const handleItemClick = (item: SearchResult | Command) => {
    onItemSelect(item);
    onClose();
    setQuery('');
  };

  const getSearchResultIcon = (item: SearchResult) => {
    switch (item.type) {
      case 'request': return '🔗';
      case 'folder': return '📁';
      case 'collection': return '📚';
      case 'example': return '📄';
      default: return '📄';
    }
  };

  const getSearchResultDescription = (item: SearchResult): string => {
    const parts: string[] = [];

    // Add type
    parts.push(item.type);

    // Add method for requests
    if (item.method && item.type === 'request') {
      parts.push(item.method);
    }

    // Add workspace name if available
    if (item.workspaceName) {
      parts.push(`in ${item.workspaceName}`);
    }

    // Add path if available
    if (item.path && item.path.length > 0) {
      parts.push(`• ${item.path.join(' > ')}`);
    }

    // Add URL for requests
    if (item.url && item.type === 'request') {
      parts.push(`• ${item.url}`);
    }

    return parts.join(' ');
  };

  if (!isOpen) return null;

  return (
    <div className="quick-search-overlay">
      <div className="quick-search-box">
        <div className="quick-search-header">
          <div className="search-input-container">
            <span className="search-icon">
              {isCommandMode ? '⚡' : '🔍'}
            </span>
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={isCommandMode ? "Enter command..." : "Search requests, folders, collections... (type / for commands)"}
              className="search-input"
            />
          </div>
          <button onClick={onClose} className="close-btn">
            ✕
          </button>
        </div>

        <div className="quick-search-content">
          {isLoading && !isCommandMode ? (
            <div className="search-loading">
              <p>Searching...</p>
            </div>
          ) : filteredItems.length > 0 ? (
            <div ref={listRef} className="search-results">
              {filteredItems.map((item, index) => (
                <div
                  key={item.id}
                  className={`search-result-item ${index === selectedIndex ? 'selected' : ''}`}
                  onClick={() => handleItemClick(item)}
                >
                  <span className="item-icon">
                    {isCommandMode ? (item as Command).icon : getSearchResultIcon(item as SearchResult)}
                  </span>
                  <div className="item-content">
                    <div className="item-name">
                      {isCommandMode ? (item as Command).name : (item as SearchResult).name}
                    </div>
                    <div className="item-description">
                      {isCommandMode
                        ? (item as Command).description
                        : getSearchResultDescription(item as SearchResult)
                      }
                    </div>
                  </div>
                  {!isCommandMode && (
                    <div className="item-type">
                      {(item as SearchResult).type}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="search-empty">
              <p>
                {isCommandMode
                  ? "No commands found"
                  : query
                    ? "No items found"
                    : "Start typing to search..."
                }
              </p>
              {!isCommandMode && !query && (
                <p className="search-hint">
                  Type <code>/</code> to access commands
                </p>
              )}
            </div>
          )}
        </div>

        <div className="quick-search-footer">
          <div className="search-shortcuts">
            <span className="shortcut">↑↓ Navigate</span>
            <span className="shortcut">↵ Select</span>
            <span className="shortcut">Esc Close</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickSearchBox;
