import React, { useState, useRef, useEffect } from 'react';

interface ResizableSplitterProps {
  direction: 'horizontal' | 'vertical';
  onResize?: (size: number) => void;
  className?: string;
}

const ResizableSplitter: React.FC<ResizableSplitterProps> = ({
  direction,
  onResize,
  className = ''
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const splitterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !onResize) return;

      const rect = splitterRef.current?.parentElement?.getBoundingClientRect();
      if (!rect) return;

      if (direction === 'horizontal') {
        const newSize = e.clientX - rect.left;
        onResize(Math.max(200, Math.min(600, newSize))); // Min 200px, Max 600px
      } else {
        const newSize = e.clientY - rect.top;
        const maxSize = rect.height - 200; // Leave at least 200px for the other panel
        onResize(Math.max(200, Math.min(maxSize, newSize))); // Min 200px, Max container height - 200px
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = direction === 'horizontal' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, direction, onResize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  return (
    <div
      ref={splitterRef}
      className={`resizable-splitter ${direction} ${className} ${isHovered ? 'hovered' : ''} ${isDragging ? 'dragging' : ''}`}
      onMouseDown={handleMouseDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    />
  );
};

export default ResizableSplitter;
