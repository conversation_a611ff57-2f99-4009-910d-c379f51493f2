<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workspace Management - ApiCool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .demo-header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .layout-demo {
            padding: 40px;
            background: #f8f9fa;
        }
        
        .layout-preview {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .layout-header {
            background: #343a40;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .layout-content {
            display: flex;
            height: 300px;
        }
        
        .sidebar-demo {
            width: 60px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            gap: 15px;
        }
        
        .sidebar-item {
            width: 40px;
            height: 40px;
            background: #007acc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .sidebar-item.active {
            background: #0056b3;
            transform: scale(1.1);
        }
        
        .workspace-panel-demo {
            width: 300px;
            background: white;
            border-right: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
        }
        
        .workspace-actions-demo {
            display: flex;
            gap: 6px;
            align-items: center;
            padding: 8px 12px;
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        
        .workspace-switcher-demo {
            flex: 1;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            min-height: 28px;
        }
        
        .action-btn-demo {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 4px 10px;
            font-size: 12px;
            cursor: pointer;
            min-height: 28px;
            transition: all 0.2s;
        }
        
        .action-btn-demo:hover {
            background: #f8f9fa;
            border-color: #007acc;
        }
        
        .main-panel-demo {
            flex: 1;
            background: #fafafa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #dee2e6;
            transition: transform 0.2s;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.5;
        }
        
        .implementation-status {
            background: white;
            padding: 40px;
            border-top: 1px solid #dee2e6;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
        }
        
        .status-complete { background: #28a745; }
        .status-progress { background: #ffc107; }
        
        .cta-section {
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🏢 Workspace Management</h1>
            <p>Organized layout with compact controls and multi-workspace support</p>
        </div>
        
        <div class="layout-demo">
            <h2>✨ New Layout Structure</h2>
            <p>Sidebar moved to the far left, workspace switcher integrated with action buttons for a more compact design.</p>
            
            <div class="layout-preview">
                <div class="layout-header">ApiCool - Improved Layout</div>
                <div class="layout-content">
                    <div class="sidebar-demo">
                        <div class="sidebar-item active">📁</div>
                        <div class="sidebar-item">🌐</div>
                        <div class="sidebar-item">⏰</div>
                    </div>
                    
                    <div class="workspace-panel-demo">
                        <div class="workspace-actions-demo">
                            <div class="workspace-switcher-demo">
                                🏢 My Workspace ▼
                            </div>
                            <div class="action-btn-demo">New</div>
                            <div class="action-btn-demo">Import</div>
                        </div>
                        <div style="flex: 1; padding: 20px; background: #fafafa;">
                            <div style="font-weight: 600; margin-bottom: 10px;">📚 Sample API Collection</div>
                            <div style="margin-left: 20px; color: #666;">
                                <div>📁 Users</div>
                                <div>📁 Authentication APIs</div>
                                <div>📁 Posts</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="main-panel-demo">
                        Request Editor & Response Viewer
                    </div>
                </div>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🏢</div>
                <div class="feature-title">Multi-Workspace Support</div>
                <div class="feature-description">
                    Create and manage multiple workspaces for different projects. 
                    Each workspace maintains its own collections, environments, and settings.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">Compact Design</div>
                <div class="feature-description">
                    Streamlined workspace switcher and action buttons take up minimal space 
                    while providing quick access to essential functions.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Improved Layout</div>
                <div class="feature-description">
                    Sidebar moved to the far left for better organization. 
                    Workspace panel focuses on content with integrated controls.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Quick Switching</div>
                <div class="feature-description">
                    Switch between workspaces instantly with the dropdown. 
                    Create new workspaces or manage existing ones with one click.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💾</div>
                <div class="feature-title">Persistent Storage</div>
                <div class="feature-description">
                    Workspace settings, collections, and state are automatically saved 
                    and restored when switching between workspaces.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📥</div>
                <div class="feature-title">Integrated Import</div>
                <div class="feature-description">
                    Import functionality moved to the workspace panel for better 
                    accessibility and workflow integration.
                </div>
            </div>
        </div>
        
        <div class="implementation-status">
            <h2>🚀 Implementation Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-indicator status-complete"></div>
                    <div>Layout Restructuring</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-complete"></div>
                    <div>Workspace Switcher</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-complete"></div>
                    <div>Compact Action Buttons</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-complete"></div>
                    <div>Workspace Management Service</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-complete"></div>
                    <div>Create Workspace Dialog</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-progress"></div>
                    <div>Workspace Settings</div>
                </div>
            </div>
        </div>
        
        <div class="cta-section">
            <h2>🎯 Ready to Test</h2>
            <p>The new layout and workspace management system is ready for testing!</p>
            <a href="file:///d:/projects/ApiCool/dist/renderer/index.html" class="cta-button">
                🚀 Launch ApiCool
            </a>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelectorAll('.action-btn-demo').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent;
                alert(`${action} button clicked!\n\nIn the actual app, this would ${action.toLowerCase()} collections.`);
            });
        });

        document.querySelector('.workspace-switcher-demo').addEventListener('click', function() {
            alert('Workspace Switcher clicked!\n\nIn the actual app, this would show a dropdown with all available workspaces.');
        });
    </script>
</body>
</html>
