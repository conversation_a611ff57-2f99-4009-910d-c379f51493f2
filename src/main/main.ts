import { app, <PERSON><PERSON>erWindow, ipc<PERSON><PERSON>, <PERSON>u } from 'electron';
import * as path from 'path';
import { RequestRunnerService } from './services/request-runner';
import type { RequestRunnerData, CollectionRunOptions } from './services/request-runner';
import type { Variable } from './services/variable-resolution';

// Services
import { WorkspaceService } from './services/workspace';
import { HierarchyService } from './services/hierarchy';
import { RequestService } from './services/request';
import { AuthService } from './services/auth';
import { AIService } from './services/ai-service';
import { CollectionService } from './services/collection';
import { BackupService } from './services/backup';
import { LoggingService } from './services/logging';
import { ThemeService } from './services/theme';
import { I18nService } from './services/i18n';
import { EnvironmentService } from './services/environment';
import { HistoryService } from './services/history';
import { ChatService } from './services/chat';
import { SettingsService } from './services/settings';

// Initialize services
const workspaceService = new WorkspaceService();
const hierarchyService = new HierarchyService();
const requestService = new RequestService();
const authService = new AuthService();
const collectionService = new CollectionService();
const backupService = new BackupService();
const loggingService = new LoggingService();
const themeService = new ThemeService();
const i18nService = new I18nService();
const environmentService = new EnvironmentService(workspaceService.prisma);
const historyService = new HistoryService(workspaceService.prisma);
const chatService = new ChatService(workspaceService.prisma);
const settingsService = new SettingsService(workspaceService.prisma);
const newAIService = new AIService(settingsService);
const requestRunnerService = new RequestRunnerService(historyService);

let mainWindow: BrowserWindow;

function createCustomMenu() {
  const template: any[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Request',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow?.webContents.send('menu:new-request');
          }
        },
        {
          label: 'New Collection',
          accelerator: 'CmdOrCtrl+Shift+N',
          click: () => {
            mainWindow?.webContents.send('menu:new-collection');
          }
        },
        { type: 'separator' },
        {
          label: 'Import',
          submenu: [
            {
              label: 'Postman Collection',
              click: () => {
                mainWindow?.webContents.send('menu:import-postman');
              }
            },
            {
              label: 'OpenAPI/Swagger',
              click: () => {
                mainWindow?.webContents.send('menu:import-openapi');
              }
            }
          ]
        },
        {
          label: 'Export',
          submenu: [
            {
              label: 'Collection',
              click: () => {
                mainWindow?.webContents.send('menu:export-collection');
              }
            }
          ]
        },
        { type: 'separator' },
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow?.webContents.send('menu:settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Collections',
          accelerator: 'CmdOrCtrl+1',
          click: () => {
            mainWindow?.webContents.send('menu:view-collections');
          }
        },
        {
          label: 'Environments',
          accelerator: 'CmdOrCtrl+2',
          click: () => {
            mainWindow?.webContents.send('menu:view-environments');
          }
        },
        {
          label: 'History',
          accelerator: 'CmdOrCtrl+3',
          click: () => {
            mainWindow?.webContents.send('menu:view-history');
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About ApiCool',
          click: () => {
            mainWindow?.webContents.send('menu:about');
          }
        },
        {
          label: 'Documentation',
          click: () => {
            mainWindow?.webContents.send('menu:documentation');
          }
        },
        {
          label: 'Keyboard Shortcuts',
          accelerator: 'CmdOrCtrl+/',
          click: () => {
            mainWindow?.webContents.send('menu:shortcuts');
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });

    // Window menu
    template[4].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' }
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, '../renderer/preload.js'),
    },
    icon: path.join(__dirname, '../../assets/icon.png'), // We'll add this later
    show: false, // Don't show until ready
    autoHideMenuBar: false, // Keep our custom menu visible
  });

  // Load the app
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null as any;
  });
}

// App event handlers
app.whenReady().then(() => {
  createCustomMenu();
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for workspace operations
ipcMain.handle('workspace:getAll', async () => {
  return await workspaceService.getAllWorkspaces();
});

ipcMain.handle('workspace:create', async (event, workspaceData) => {
  return await workspaceService.createWorkspace(workspaceData);
});

ipcMain.handle('workspace:update', async (event, id, workspaceData) => {
  return await workspaceService.updateWorkspace(id, workspaceData);
});

ipcMain.handle('workspace:delete', async (event, id) => {
  return await workspaceService.deleteWorkspace(id);
});

ipcMain.handle('workspace:getStats', async (event, id) => {
  return await workspaceService.getWorkspaceStats(id);
});

// IPC handlers for hierarchy operations
ipcMain.handle('hierarchy:getItems', async (event, workspaceId) => {
  return await hierarchyService.getWorkspaceItems(workspaceId);
});

ipcMain.handle('hierarchy:updateItem', async (event, itemId, parentId, order) => {
  return await hierarchyService.updateItemHierarchy(itemId, parentId, order);
});

// IPC handlers for workspace items (lightweight for left panel)
ipcMain.handle('workspace:getItemsLight', async (event, workspaceId) => {
  return await workspaceService.getWorkspaceItemsLight(workspaceId);
});

ipcMain.handle('workspace:getItemDetails', async (event, itemId) => {
  return await workspaceService.getItemDetails(itemId);
});

// IPC handlers for specific entity details (for tab opening)
ipcMain.handle('workspace:getCollectionDetails', async (event, collectionId) => {
  return await workspaceService.getCollectionDetails(collectionId);
});

ipcMain.handle('workspace:getFolderDetails', async (event, folderId) => {
  return await workspaceService.getFolderDetails(folderId);
});

ipcMain.handle('workspace:getRequestDetails', async (event, requestId) => {
  return await workspaceService.getRequestDetails(requestId);
});

ipcMain.handle('workspace:getExampleDetails', async (event, exampleId) => {
  return await workspaceService.getExampleDetails(exampleId);
});

ipcMain.handle('workspace:createExample', async (event, data) => {
  return await workspaceService.createExample(data);
});

ipcMain.handle('workspace:createCollection', async (event, data) => {
  return await workspaceService.createCollection(data);
});

ipcMain.handle('workspace:updateCollection', async (event, collectionId, data) => {
  return await workspaceService.updateCollection(collectionId, data);
});

ipcMain.handle('workspace:createFolder', async (event, data) => {
  return await workspaceService.createFolder(data);
});

ipcMain.handle('workspace:updateFolder', async (event, folderId, data) => {
  return await workspaceService.updateFolder(folderId, data);
});

ipcMain.handle('workspace:createRequest', async (event, data) => {
  return await workspaceService.createRequest(data);
});

ipcMain.handle('workspace:updateRequest', async (event, requestId, data) => {
  return await workspaceService.updateRequest(requestId, data);
});

ipcMain.handle('workspace:addItemToHierarchy', async (event, data) => {
  return await workspaceService.addItemToHierarchy(data);
});

ipcMain.handle('workspace:deleteItem', async (event, itemId) => {
  return await workspaceService.deleteItem(itemId);
});

ipcMain.handle('workspace:updateItemHierarchy', async (event, itemId, newParentId, newOrder) => {
  return await workspaceService.updateItemHierarchy(itemId, newParentId, newOrder);
});

ipcMain.handle('workspace:reorderItems', async (event, items) => {
  return await workspaceService.reorderItems(items);
});

// IPC handlers for request operations (legacy - now handled by request runner)

ipcMain.handle('request:save', async (event, requestData) => {
  return await requestService.saveRequest(requestData);
});



// IPC handlers for collection operations
ipcMain.handle('collection:import', async (event, filePath) => {
  return await collectionService.importPostmanCollection(filePath);
});

ipcMain.handle('collection:export', async (event, collectionId, filePath) => {
  return await collectionService.exportCollection(collectionId, filePath);
});

// IPC handlers for theme operations
ipcMain.handle('theme:get', async () => {
  return await themeService.getCurrentTheme();
});

ipcMain.handle('theme:set', async (event, theme) => {
  return await themeService.setTheme(theme);
});

ipcMain.handle('theme:getVariables', async () => {
  return themeService.getThemeVariables();
});

// IPC handlers for internationalization operations
ipcMain.handle('i18n:getLanguage', async () => {
  return i18nService.getCurrentLanguage();
});

ipcMain.handle('i18n:setLanguage', async (event, language) => {
  return await i18nService.setLanguage(language);
});

ipcMain.handle('i18n:getTranslations', async (event, language) => {
  return i18nService.getTranslations(language);
});

ipcMain.handle('i18n:getSupportedLanguages', async () => {
  return i18nService.getSupportedLanguages();
});

// IPC handlers for backup operations
ipcMain.handle('backup:create', async () => {
  return await backupService.createBackup();
});

ipcMain.handle('backup:restore', async (event, filePath) => {
  return await backupService.restoreBackup(filePath);
});

ipcMain.handle('backup:list', async () => {
  return await backupService.listBackups();
});

ipcMain.handle('backup:configure', async (event, config) => {
  return await backupService.configure(config);
});

// IPC handlers for logging operations
ipcMain.handle('log:getLogs', async (event, level, limit) => {
  return await loggingService.getLogs(level, limit);
});

ipcMain.handle('log:setLevel', async (event, level) => {
  loggingService.setLevel(level);
});

ipcMain.handle('log:clear', async () => {
  return await loggingService.clearLogs();
});

// IPC handlers for authentication operations
ipcMain.handle('auth:login', async (event, credentials) => {
  return await authService.login(credentials);
});

ipcMain.handle('auth:logout', async () => {
  return await authService.logout();
});

ipcMain.handle('auth:register', async (event, data) => {
  return await authService.register(data);
});

ipcMain.handle('auth:getCurrentUser', async () => {
  return authService.getCurrentUser();
});

ipcMain.handle('auth:changePassword', async (event, oldPassword, newPassword) => {
  return await authService.changePassword(oldPassword, newPassword);
});



// IPC handlers for file operations
ipcMain.handle('file:openDialog', async (event, options) => {
  const { dialog } = require('electron');
  return await dialog.showOpenDialog(mainWindow, options);
});

ipcMain.handle('file:saveDialog', async (event, options) => {
  const { dialog } = require('electron');
  return await dialog.showSaveDialog(mainWindow, options);
});

ipcMain.handle('file:read', async (event, filePath) => {
  const fs = require('fs').promises;
  return await fs.readFile(filePath, 'utf8');
});

ipcMain.handle('file:write', async (event, filePath, data) => {
  const fs = require('fs').promises;
  return await fs.writeFile(filePath, data, 'utf8');
});

// IPC handlers for window operations
ipcMain.handle('window:minimize', async () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window:maximize', async () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window:close', async () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.handle('window:toggleDevTools', async () => {
  if (mainWindow) {
    mainWindow.webContents.toggleDevTools();
  }
});

// IPC handlers for request execution
ipcMain.handle('request:execute', async (event, requestData, environmentVariables, collectionVariables, workspaceId, environmentName) => {
  try {
    return await requestRunnerService.executeRequest(
      requestData,
      environmentVariables,
      collectionVariables,
      workspaceId,
      environmentName
    );
  } catch (error) {
    loggingService.error('Request execution failed:', error);
    throw error;
  }
});

ipcMain.handle('collection:run', async (event, requests, environmentVariables, collectionVariables, options) => {
  try {
    return await requestRunnerService.executeCollection(requests, environmentVariables, collectionVariables, options);
  } catch (error) {
    loggingService.error('Collection run failed:', error);
    throw error;
  }
});

ipcMain.handle('request:getVariableState', async () => {
  try {
    return requestRunnerService.getVariableState();
  } catch (error) {
    loggingService.error('Failed to get variable state:', error);
    throw error;
  }
});

ipcMain.handle('request:setTimeout', async (event, timeout) => {
  try {
    requestRunnerService.setTimeout(timeout);
    return { success: true };
  } catch (error) {
    loggingService.error('Failed to set timeout:', error);
    throw error;
  }
});

// Environment IPC handlers
ipcMain.handle('environment:getAll', async (event, workspaceId) => {
  return await environmentService.getEnvironments(workspaceId);
});

ipcMain.handle('environment:getActive', async (event, workspaceId) => {
  return await environmentService.getActiveEnvironment(workspaceId);
});

ipcMain.handle('environment:create', async (event, data) => {
  return await environmentService.createEnvironment(data);
});

ipcMain.handle('environment:update', async (event, id, data) => {
  return await environmentService.updateEnvironment(id, data);
});

ipcMain.handle('environment:setActive', async (event, id, workspaceId) => {
  return await environmentService.setActiveEnvironment(id, workspaceId);
});

ipcMain.handle('environment:delete', async (event, id) => {
  return await environmentService.deleteEnvironment(id);
});

ipcMain.handle('environment:duplicate', async (event, id, newName) => {
  return await environmentService.duplicateEnvironment(id, newName);
});

// History IPC handlers
ipcMain.handle('history:getAll', async (event, workspaceId, limit, offset) => {
  return await historyService.getHistory(workspaceId, limit, offset);
});

ipcMain.handle('history:getEntry', async (event, id) => {
  return await historyService.getHistoryEntry(id);
});

ipcMain.handle('history:create', async (event, data) => {
  return await historyService.createHistoryEntry(data);
});

ipcMain.handle('history:delete', async (event, id) => {
  return await historyService.deleteHistoryEntry(id);
});

ipcMain.handle('history:clear', async (event, workspaceId) => {
  return await historyService.clearHistory(workspaceId);
});

ipcMain.handle('history:getStats', async (event, workspaceId) => {
  return await historyService.getHistoryStats(workspaceId);
});

ipcMain.handle('history:search', async (event, workspaceId, query, limit) => {
  return await historyService.searchHistory(workspaceId, query, limit);
});

// AI IPC handlers
ipcMain.handle('ai:sendMessage', async (event, request) => {
  return await newAIService.sendMessage(request);
});

ipcMain.handle('ai:getProviders', async () => {
  return newAIService.getProviders();
});

ipcMain.handle('ai:getProvider', async (event, id) => {
  return newAIService.getProvider(id);
});

ipcMain.handle('ai:updateProvider', async (event, provider) => {
  newAIService.updateProvider(provider);
});

ipcMain.handle('ai:testConnection', async (event, provider) => {
  return await newAIService.testConnection(provider);
});

ipcMain.handle('ai:fetchModels', async (event, providerConfig) => {
  return await newAIService.fetchModels(providerConfig);
});

ipcMain.handle('ai:reloadProviders', async () => {
  return await newAIService.reloadProviders();
});

ipcMain.handle('ai:sendStreamMessage', async (event, request) => {
  return new Promise((resolve, reject) => {
    const chunks: string[] = [];

    newAIService.sendStreamMessage(request, (chunk) => {
      // Send chunk to renderer
      event.sender.send('ai:streamChunk', chunk);

      if (!chunk.done) {
        chunks.push(chunk.content);
      } else {
        // Stream finished, resolve with complete content
        resolve({
          content: chunks.join(''),
          model: chunk.model,
          generatedRequest: newAIService.extractRequestFromContent ?
            newAIService.extractRequestFromContent(chunks.join('')) : null
        });
      }
    }).catch(reject);
  });
});

// Chat IPC handlers
ipcMain.handle('chat:getSessions', async (event, workspaceId) => {
  return await chatService.getChatSessions(workspaceId);
});

ipcMain.handle('chat:getSession', async (event, sessionId) => {
  return await chatService.getChatSession(sessionId);
});

ipcMain.handle('chat:createSession', async (event, data) => {
  return await chatService.createChatSession(data);
});

ipcMain.handle('chat:updateSession', async (event, sessionId, title) => {
  return await chatService.updateChatSession(sessionId, title);
});

ipcMain.handle('chat:deleteSession', async (event, sessionId) => {
  return await chatService.deleteChatSession(sessionId);
});

ipcMain.handle('chat:addMessage', async (event, data) => {
  return await chatService.addChatMessage(data);
});

ipcMain.handle('chat:updateMessage', async (event, messageId, data) => {
  return await chatService.updateChatMessage(messageId, data);
});

ipcMain.handle('chat:deleteMessage', async (event, messageId) => {
  return await chatService.deleteChatMessage(messageId);
});

ipcMain.handle('chat:getMessages', async (event, sessionId) => {
  return await chatService.getChatMessages(sessionId);
});

ipcMain.handle('chat:clearSession', async (event, sessionId) => {
  return await chatService.clearChatSession(sessionId);
});

// Settings IPC handlers
ipcMain.handle('settings:getAll', async () => {
  return await settingsService.getSettings();
});

ipcMain.handle('settings:get', async (event, key) => {
  return await settingsService.getSetting(key);
});

ipcMain.handle('settings:update', async (event, key, value) => {
  return await settingsService.updateSetting(key, value);
});

ipcMain.handle('settings:updateMultiple', async (event, settings) => {
  return await settingsService.updateSettings(settings);
});

ipcMain.handle('settings:reset', async () => {
  return await settingsService.resetSettings();
});

ipcMain.handle('settings:getTheme', async () => {
  return await settingsService.getTheme();
});

ipcMain.handle('settings:updateTheme', async (event, theme) => {
  return await settingsService.updateTheme(theme);
});

ipcMain.handle('settings:getAI', async () => {
  return await settingsService.getAISettings();
});

ipcMain.handle('settings:updateAI', async (event, aiSettings) => {
  return await settingsService.updateAISettings(aiSettings);
});

ipcMain.handle('settings:updateAIProvider', async (event, provider) => {
  await settingsService.updateAIProvider(provider);
  // Reload AI service providers to reflect the changes
  await newAIService.reloadProviders();
  return;
});

ipcMain.handle('settings:removeAIProvider', async (event, providerId) => {
  return await settingsService.removeAIProvider(providerId);
});

ipcMain.handle('settings:export', async () => {
  return await settingsService.exportSettings();
});

ipcMain.handle('settings:import', async (event, settings) => {
  return await settingsService.importSettings(settings);
});

// Error handling
process.on('uncaughtException', (error) => {
  loggingService.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  loggingService.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
