import { useEffect } from 'react';
import { keyboardShortcuts, ShortcutHandler } from '../services/KeyboardShortcutService';

/**
 * Hook to register keyboard shortcuts for a component
 */
export const useKeyboardShortcuts = (shortcuts: Omit<ShortcutHandler, 'handler'>[], handlers: Record<string, (event: KeyboardEvent) => void>) => {
  useEffect(() => {
    // Register shortcuts
    const registeredShortcuts: ShortcutHandler[] = shortcuts.map(shortcut => ({
      ...shortcut,
      handler: handlers[shortcut.key] || (() => {})
    }));

    registeredShortcuts.forEach(shortcut => {
      keyboardShortcuts.register(shortcut);
    });

    // Start listening if not already
    keyboardShortcuts.startListening();

    // Cleanup
    return () => {
      registeredShortcuts.forEach(shortcut => {
        keyboardShortcuts.unregister(shortcut);
      });
    };
  }, [shortcuts, handlers]);
};

/**
 * Predefined shortcut configurations
 */
export const SHORTCUTS = {
  // Tab management shortcuts
  CLOSE_TAB: {
    key: 'w',
    ctrlOrCmd: true,
    description: 'Close current tab',
    context: 'tabs'
  },
  NEW_TAB: {
    key: 't',
    ctrlOrCmd: true,
    description: 'Open new tab',
    context: 'tabs'
  },
  CLOSE_ALL_TABS: {
    key: 'W',
    ctrlOrCmd: true,
    shift: true,
    description: 'Close all tabs',
    context: 'tabs'
  },
  NEXT_TAB: {
    key: 'Tab',
    ctrlOrCmd: true,
    description: 'Switch to next tab',
    context: 'tabs'
  },
  PREV_TAB: {
    key: 'Tab',
    ctrlOrCmd: true,
    shift: true,
    description: 'Switch to previous tab',
    context: 'tabs'
  },
  RENAME_TAB: {
    key: 'F2',
    description: 'Rename current tab',
    context: 'tabs'
  },

  // Editor shortcuts
  SAVE: {
    key: 's',
    ctrlOrCmd: true,
    description: 'Save current item',
    context: 'editor'
  },
  SEND_REQUEST: {
    key: 'Enter',
    ctrlOrCmd: true,
    description: 'Send request',
    context: 'editor'
  },

  // Global shortcuts
  SAVE_GLOBAL: {
    key: 's',
    ctrlOrCmd: true,
    description: 'Save current item (global)',
    context: 'tabs'
  }
} as const;

/**
 * Helper to create tab number shortcuts (Ctrl+1, Ctrl+2, etc.)
 */
export const createTabNumberShortcuts = (maxTabs: number = 9) => {
  const shortcuts = [];
  for (let i = 1; i <= Math.min(maxTabs, 9); i++) {
    shortcuts.push({
      key: i.toString(),
      ctrlOrCmd: true,
      description: `Switch to tab ${i}`,
      context: 'tabs'
    });
  }
  return shortcuts;
};
