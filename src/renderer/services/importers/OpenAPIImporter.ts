import {
  ImportResult,
  Imported<PERSON>ollection,
  ImportedRequest,
  ImportedAuth,
  OpenAPISpec,
  OpenAPIOperation,
  OpenAPIParameter,
  OpenAPIRequestBody,
  OpenAPIResponse,
  OpenAPISecurityScheme
} from '../../types/import-export';

export class OpenAPIImporter {
  static async importSpec(fileContent: string): Promise<ImportResult> {
    try {
      const openApiData = JSON.parse(fileContent) as OpenAPISpec;
      
      if (!this.isValidOpenAPISpec(openApiData)) {
        return {
          success: false,
          collections: [],
          errors: ['Invalid OpenAPI/Swagger specification format'],
          warnings: []
        };
      }

      const collection = this.convertOpenAPISpec(openApiData);
      
      return {
        success: true,
        collections: [collection],
        errors: [],
        warnings: []
      };
    } catch (error) {
      return {
        success: false,
        collections: [],
        errors: [`Failed to parse OpenAPI specification: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  private static isValidOpenAPISpec(data: any): data is OpenAPISpec {
    return (
      data &&
      typeof data === 'object' &&
      (data.openapi || data.swagger) &&
      data.info &&
      typeof data.info.title === 'string' &&
      data.paths &&
      typeof data.paths === 'object'
    );
  }

  private static convertOpenAPISpec(spec: OpenAPISpec): ImportedCollection {
    const baseUrl = this.getBaseUrl(spec);
    
    const collection: ImportedCollection = {
      id: this.generateId(),
      name: spec.info.title,
      description: spec.info.description || '',
      version: spec.info.version,
      folders: [],
      requests: [],
      variables: {
        baseUrl: baseUrl
      },
      auth: this.convertGlobalAuth(spec)
    };

    // Convert paths to requests
    for (const [path, pathItem] of Object.entries(spec.paths)) {
      const methods = ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'];
      
      for (const method of methods) {
        const operation = pathItem[method as keyof typeof pathItem] as OpenAPIOperation;
        if (operation) {
          const request = this.convertOperation(path, method, operation, spec, baseUrl);
          collection.requests.push(request);
        }
      }
    }

    // Group requests by tags into folders
    this.groupRequestsByTags(collection);

    return collection;
  }

  private static getBaseUrl(spec: OpenAPISpec): string {
    if (spec.servers && spec.servers.length > 0) {
      return spec.servers[0].url;
    }
    
    // Fallback for older Swagger 2.0 format
    const swagger2 = spec as any;
    if (swagger2.host) {
      const scheme = swagger2.schemes?.[0] || 'https';
      const basePath = swagger2.basePath || '';
      return `${scheme}://${swagger2.host}${basePath}`;
    }
    
    return 'https://api.example.com';
  }

  private static convertOperation(
    path: string,
    method: string,
    operation: OpenAPIOperation,
    spec: OpenAPISpec,
    baseUrl: string
  ): ImportedRequest {
    const request: ImportedRequest = {
      id: this.generateId(),
      name: operation.summary || operation.operationId || `${method.toUpperCase()} ${path}`,
      description: operation.description || '',
      method: method.toUpperCase(),
      url: `{{baseUrl}}${path}`,
      headers: this.extractHeaders(operation.parameters || []),
      params: this.extractQueryParams(operation.parameters || []),
      body: this.convertRequestBody(operation.requestBody),
      auth: this.convertOperationAuth(operation, spec),
      tests: this.generateTests(operation),
      preRequestScript: '',
      examples: this.convertExamples(operation)
    };

    return request;
  }

  private static extractHeaders(parameters: OpenAPIParameter[]): Record<string, string> {
    const headers: Record<string, string> = {};
    
    for (const param of parameters) {
      if (param.in === 'header') {
        headers[param.name] = param.example || `{{${param.name}}}`;
      }
    }
    
    return headers;
  }

  private static extractQueryParams(parameters: OpenAPIParameter[]): Record<string, string> {
    const params: Record<string, string> = {};
    
    for (const param of parameters) {
      if (param.in === 'query') {
        params[param.name] = param.example || `{{${param.name}}}`;
      }
    }
    
    return params;
  }

  private static convertRequestBody(requestBody?: OpenAPIRequestBody): string {
    if (!requestBody || !requestBody.content) {
      return '';
    }

    // Try to find JSON content first
    const jsonContent = requestBody.content['application/json'];
    if (jsonContent) {
      if (jsonContent.example) {
        return JSON.stringify(jsonContent.example, null, 2);
      }
      
      if (jsonContent.examples) {
        const firstExample = Object.values(jsonContent.examples)[0];
        if (firstExample && firstExample.value) {
          return JSON.stringify(firstExample.value, null, 2);
        }
      }
      
      // Generate example from schema
      if (jsonContent.schema) {
        const example = this.generateExampleFromSchema(jsonContent.schema);
        return JSON.stringify(example, null, 2);
      }
    }

    // Fallback to other content types
    const contentTypes = Object.keys(requestBody.content);
    if (contentTypes.length > 0) {
      const content = requestBody.content[contentTypes[0]];
      if (content.example) {
        return typeof content.example === 'string' 
          ? content.example 
          : JSON.stringify(content.example, null, 2);
      }
    }

    return '';
  }

  private static generateExampleFromSchema(schema: any): any {
    if (!schema || typeof schema !== 'object') {
      return {};
    }

    switch (schema.type) {
      case 'object':
        const obj: any = {};
        if (schema.properties) {
          for (const [key, prop] of Object.entries(schema.properties as any)) {
            obj[key] = this.generateExampleFromSchema(prop);
          }
        }
        return obj;
        
      case 'array':
        return schema.items ? [this.generateExampleFromSchema(schema.items)] : [];
        
      case 'string':
        return schema.example || schema.default || 'string';
        
      case 'number':
      case 'integer':
        return schema.example || schema.default || 0;
        
      case 'boolean':
        return schema.example !== undefined ? schema.example : (schema.default !== undefined ? schema.default : true);
        
      default:
        return schema.example || schema.default || null;
    }
  }

  private static convertGlobalAuth(spec: OpenAPISpec): ImportedAuth | undefined {
    if (!spec.security || spec.security.length === 0) {
      return undefined;
    }

    const firstSecurity = spec.security[0];
    const securitySchemeName = Object.keys(firstSecurity)[0];
    
    if (!securitySchemeName || !spec.components?.securitySchemes) {
      return undefined;
    }

    const securityScheme = spec.components.securitySchemes[securitySchemeName];
    return this.convertSecurityScheme(securityScheme);
  }

  private static convertOperationAuth(operation: OpenAPIOperation, spec: OpenAPISpec): ImportedAuth | undefined {
    if (!operation.security || operation.security.length === 0) {
      return this.convertGlobalAuth(spec);
    }

    const firstSecurity = operation.security[0];
    const securitySchemeName = Object.keys(firstSecurity)[0];
    
    if (!securitySchemeName || !spec.components?.securitySchemes) {
      return undefined;
    }

    const securityScheme = spec.components.securitySchemes[securitySchemeName];
    return this.convertSecurityScheme(securityScheme);
  }

  private static convertSecurityScheme(scheme: OpenAPISecurityScheme): ImportedAuth | undefined {
    switch (scheme.type) {
      case 'http':
        if (scheme.scheme === 'bearer') {
          return {
            type: 'bearer',
            bearer: { token: '{{bearerToken}}' }
          };
        } else if (scheme.scheme === 'basic') {
          return {
            type: 'basic',
            basic: { username: '{{username}}', password: '{{password}}' }
          };
        }
        break;
        
      case 'apiKey':
        return {
          type: 'api-key',
          apikey: {
            key: scheme.name || 'X-API-Key',
            value: '{{apiKey}}',
            in: scheme.in === 'query' ? 'query' : 'header'
          }
        };
        
      case 'oauth2':
        return {
          type: 'oauth2',
          oauth2: { accessToken: '{{accessToken}}' }
        };
    }
    
    return undefined;
  }

  private static generateTests(operation: OpenAPIOperation): string {
    const tests: string[] = [];
    
    // Generate basic status code tests
    if (operation.responses) {
      const successCodes = Object.keys(operation.responses).filter(code => 
        code.startsWith('2') || code === 'default'
      );
      
      if (successCodes.length > 0) {
        const code = successCodes[0] === 'default' ? '200' : successCodes[0];
        tests.push(`pm.test("Status code is ${code}", function () {
    pm.response.to.have.status(${code});
});`);
      }
    }
    
    // Generate response time test
    tests.push(`pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});`);
    
    return tests.join('\n\n');
  }

  private static convertExamples(operation: OpenAPIOperation): any[] {
    // OpenAPI examples would be converted here
    // This is a simplified implementation
    return [];
  }

  private static groupRequestsByTags(collection: ImportedCollection): void {
    const taggedRequests = new Map<string, ImportedRequest[]>();
    const untaggedRequests: ImportedRequest[] = [];
    
    for (const request of collection.requests) {
      // Extract tags from request name or description
      // This is a simplified implementation
      const tags = this.extractTagsFromRequest(request);
      
      if (tags.length > 0) {
        const tag = tags[0];
        if (!taggedRequests.has(tag)) {
          taggedRequests.set(tag, []);
        }
        taggedRequests.get(tag)!.push(request);
      } else {
        untaggedRequests.push(request);
      }
    }
    
    // Create folders for tags
    collection.folders = Array.from(taggedRequests.entries()).map(([tag, requests]) => ({
      id: this.generateId(),
      name: tag,
      description: `Requests for ${tag}`,
      requests: requests,
      subfolders: [],
      variables: {},
      auth: undefined
    }));
    
    // Keep untagged requests at collection level
    collection.requests = untaggedRequests;
  }

  private static extractTagsFromRequest(request: ImportedRequest): string[] {
    // Simple tag extraction - in real implementation, this would come from OpenAPI operation.tags
    const pathParts = request.url.split('/').filter(part => part && !part.startsWith('{'));
    return pathParts.length > 1 ? [pathParts[1]] : [];
  }

  private static generateId(): string {
    return 'openapi_' + Math.random().toString(36).substr(2, 9);
  }
}

export default OpenAPIImporter;
