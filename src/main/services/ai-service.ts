import axios from 'axios';

export interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'ollama' | 'gemini' | 'anthropic' | 'siliconflow' | 'alibaba' | 'custom';
  baseUrl?: string;
  apiKey?: string;
  models: string[];
}

export interface ChatMessage {
  id: number;
  sessionId: number;
  workspaceId: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: string[];
  metadata?: any;
  generatedRequest?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatSession {
  id: number;
  title: string;
  workspaceId: number;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AIRequest {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    images?: string[];
  }>;
  model: string;
  provider: AIProvider;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface AIResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  generatedRequest?: any;
}

export interface AIStreamChunk {
  content: string;
  done: boolean;
  model: string;
}

export class AIService {
  private providers: Map<string, AIProvider> = new Map();
  private settingsService?: any;

  constructor(settingsService?: any) {
    this.settingsService = settingsService;
    this.initializeProviders();
  }

  private async initializeProviders() {
    if (this.settingsService) {
      try {
        const aiSettings = await this.settingsService.getAISettings();
        // Load providers from settings
        for (const provider of aiSettings.providers) {
          this.providers.set(provider.id, provider);
        }
        return;
      } catch (error) {
        console.error('Failed to load providers from settings, using defaults:', error);
      }
    }

    // Fallback to default providers
    this.initializeDefaultProviders();
  }

  private initializeDefaultProviders() {
    // OpenAI
    this.providers.set('openai', {
      id: 'openai',
      name: 'OpenAI',
      type: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4-vision-preview']
    });

    // Ollama (local)
    this.providers.set('ollama', {
      id: 'ollama',
      name: 'Ollama (Local)',
      type: 'ollama',
      baseUrl: 'http://localhost:11434',
      models: ['llama2', 'codellama', 'mistral', 'llama2:13b', 'llama2:70b']
    });

    // Google Gemini
    this.providers.set('gemini', {
      id: 'gemini',
      name: 'Google Gemini',
      type: 'gemini',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      models: ['gemini-pro', 'gemini-pro-vision']
    });

    // Anthropic Claude
    this.providers.set('anthropic', {
      id: 'anthropic',
      name: 'Anthropic Claude',
      type: 'anthropic',
      baseUrl: 'https://api.anthropic.com/v1',
      models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307']
    });
  }

  async sendMessage(request: AIRequest): Promise<AIResponse> {
    const { provider, model, messages, temperature = 0.7, maxTokens = 2048, stream = false } = request;

    try {
      switch (provider.type) {
        case 'openai':
          return await this.sendOpenAIMessage(provider, model, messages, temperature, maxTokens);
        case 'ollama':
          if (stream) {
            throw new Error('Use sendStreamMessage for streaming responses');
          }
          return await this.sendOllamaMessage(provider, model, messages, temperature, maxTokens);
        case 'gemini':
          return await this.sendGeminiMessage(provider, model, messages, temperature, maxTokens);
        case 'anthropic':
          return await this.sendAnthropicMessage(provider, model, messages, temperature, maxTokens);
        default:
          throw new Error(`Unsupported provider type: ${provider.type}`);
      }
    } catch (error) {
      console.error('AI Service Error:', error);
      throw new Error(`AI request failed: ${error.message}`);
    }
  }

  async sendStreamMessage(request: AIRequest, onChunk: (chunk: AIStreamChunk) => void): Promise<void> {
    const { messages, model, provider, temperature = 0.7, maxTokens = 2048 } = request;

    try {
      switch (provider.type) {
        case 'ollama':
          return await this.sendOllamaStreamMessage(provider, model, messages, temperature, maxTokens, onChunk);
        default:
          throw new Error(`Streaming not supported for provider type: ${provider.type}`);
      }
    } catch (error) {
      console.error('AI Stream Service Error:', error);
      throw new Error(`AI stream request failed: ${error.message}`);
    }
  }

  private async sendOpenAIMessage(
    provider: AIProvider,
    model: string,
    messages: any[],
    temperature: number,
    maxTokens: number
  ): Promise<AIResponse> {
    const response = await axios.post(
      `${provider.baseUrl}/chat/completions`,
      {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.images && msg.images.length > 0 ? [
            { type: 'text', text: msg.content },
            ...msg.images.map(img => ({ type: 'image_url', image_url: { url: img } }))
          ] : msg.content
        })),
        temperature,
        max_tokens: maxTokens
      },
      {
        headers: {
          'Authorization': `Bearer ${provider.apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const content = response.data.choices[0].message.content;
    return {
      content,
      model,
      usage: response.data.usage,
      generatedRequest: this.extractRequestFromContent(content)
    };
  }

  private async sendOllamaMessage(
    provider: AIProvider,
    model: string,
    messages: any[],
    temperature: number,
    maxTokens: number
  ): Promise<AIResponse> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/api/chat`,
        {
          model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.images && msg.images.length > 0 && { images: msg.images })
          })),
          stream: false, // Important: disable streaming for simple response
          options: {
            temperature,
            num_predict: maxTokens,
            think: false // Disable thinking output
          }
        },
        {
          timeout: 60000, // 60 second timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      const content = response.data.message?.content || response.data.response || '';
      return {
        content,
        model,
        generatedRequest: this.extractRequestFromContent(content)
      };
    } catch (error: any) {
      console.error('Ollama API Error:', error.response?.data || error.message);

      if (error.code === 'ECONNREFUSED' || error.response?.status === 502) {
        throw new Error('Ollama server is not running. Please start Ollama and try again.');
      } else if (error.response?.status === 404) {
        throw new Error(`Model "${model}" not found. Please check if the model is installed in Ollama.`);
      } else if (error.response?.status === 400) {
        throw new Error(`Invalid request: ${error.response?.data?.error || 'Bad request'}`);
      } else {
        throw new Error(`Ollama request failed: ${error.message}`);
      }
    }
  }

  private async sendOllamaStreamMessage(
    provider: AIProvider,
    model: string,
    messages: any[],
    temperature: number,
    maxTokens: number,
    onChunk: (chunk: AIStreamChunk) => void
  ): Promise<void> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/api/chat`,
        {
          model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.images && msg.images.length > 0 && { images: msg.images })
          })),
          stream: true, // Enable streaming
          options: {
            temperature,
            num_predict: maxTokens,
            think: false // Disable thinking output
          }
        },
        {
          timeout: 60000,
          headers: {
            'Content-Type': 'application/json'
          },
          responseType: 'stream'
        }
      );

      let buffer = '';

      response.data.on('data', (chunk: Buffer) => {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.message && data.message.content) {
                onChunk({
                  content: data.message.content,
                  done: data.done || false,
                  model
                });
              }
              if (data.done) {
                return;
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming chunk:', line);
            }
          }
        }
      });

      response.data.on('end', () => {
        onChunk({
          content: '',
          done: true,
          model
        });
      });

      response.data.on('error', (error: any) => {
        throw new Error(`Stream error: ${error.message}`);
      });

    } catch (error: any) {
      console.error('Ollama Stream API Error:', error.response?.data || error.message);

      if (error.code === 'ECONNREFUSED' || error.response?.status === 502) {
        throw new Error('Ollama server is not running. Please start Ollama and try again.');
      } else if (error.response?.status === 404) {
        throw new Error(`Model "${model}" not found. Please check if the model is installed in Ollama.`);
      } else if (error.response?.status === 400) {
        throw new Error(`Invalid request: ${error.response?.data?.error || 'Bad request'}`);
      } else {
        throw new Error(`Ollama stream request failed: ${error.message}`);
      }
    }
  }

  private async sendGeminiMessage(
    provider: AIProvider,
    model: string,
    messages: any[],
    temperature: number,
    maxTokens: number
  ): Promise<AIResponse> {
    const contents = messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [
        { text: msg.content },
        ...(msg.images || []).map(img => ({ inline_data: { mime_type: 'image/jpeg', data: img } }))
      ]
    }));

    const response = await axios.post(
      `${provider.baseUrl}/models/${model}:generateContent?key=${provider.apiKey}`,
      {
        contents,
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens
        }
      }
    );

    const content = response.data.candidates[0].content.parts[0].text;
    return {
      content,
      model,
      generatedRequest: this.extractRequestFromContent(content)
    };
  }

  private async sendAnthropicMessage(
    provider: AIProvider,
    model: string,
    messages: any[],
    temperature: number,
    maxTokens: number
  ): Promise<AIResponse> {
    const response = await axios.post(
      `${provider.baseUrl}/messages`,
      {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature,
        max_tokens: maxTokens
      },
      {
        headers: {
          'x-api-key': provider.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        }
      }
    );

    const content = response.data.content[0].text;
    return {
      content,
      model,
      generatedRequest: this.extractRequestFromContent(content)
    };
  }

  extractRequestFromContent(content: string): any {
    try {
      // Look for JSON blocks in the response
      const jsonRegex = /```(?:json)?\s*(\{[\s\S]*?\})\s*```/g;
      const matches = content.match(jsonRegex);
      
      if (matches) {
        for (const match of matches) {
          const jsonStr = match.replace(/```(?:json)?\s*/, '').replace(/\s*```/, '');
          try {
            const parsed = JSON.parse(jsonStr);
            // Check if it looks like a request object
            if (parsed.method || parsed.url || parsed.name) {
              return parsed;
            }
          } catch (e) {
            continue;
          }
        }
      }

      // Try to find JSON without code blocks
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          if (parsed.method || parsed.url || parsed.name) {
            return parsed;
          }
        } catch (e) {
          // Ignore
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  getProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }

  getProvider(id: string): AIProvider | undefined {
    return this.providers.get(id);
  }

  updateProvider(provider: AIProvider): void {
    this.providers.set(provider.id, provider);
  }

  async reloadProviders(): Promise<void> {
    this.providers.clear();
    await this.initializeProviders();
  }

  async testConnection(provider: AIProvider): Promise<boolean> {
    try {
      // For Ollama, test by fetching available models first
      if (provider.type === 'ollama') {
        await this.fetchOllamaModels(provider.baseUrl || 'http://localhost:11434');
        return true;
      }

      // For other providers, test with a simple message
      if (!provider.models || provider.models.length === 0) {
        throw new Error('No models available for testing');
      }

      const testRequest: AIRequest = {
        messages: [{ role: 'user', content: 'Hello' }],
        model: provider.models[0],
        provider,
        temperature: 0.1,
        maxTokens: 10
      };

      await this.sendMessage(testRequest);
      return true;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  async fetchModels(providerConfig: { type: string; baseUrl?: string; apiKey?: string }): Promise<string[]> {
    try {
      switch (providerConfig.type) {
        case 'openai':
          return await this.fetchOpenAIModels(providerConfig.baseUrl || 'https://api.openai.com/v1', providerConfig.apiKey);
        case 'ollama':
          return await this.fetchOllamaModels(providerConfig.baseUrl || 'http://localhost:11434');
        case 'gemini':
          return await this.fetchGeminiModels(providerConfig.apiKey);
        case 'anthropic':
          return await this.fetchAnthropicModels();
        case 'siliconflow':
          return await this.fetchSiliconFlowModels(providerConfig.apiKey);
        case 'alibaba':
          return await this.fetchAlibabaModels(providerConfig.apiKey);
        case 'custom':
          // Try OpenAI-compatible API first
          return await this.fetchOpenAIModels(providerConfig.baseUrl, providerConfig.apiKey);
        default:
          return [];
      }
    } catch (error) {
      console.error('Failed to fetch models:', error);
      throw new Error(`Failed to fetch models: ${error.message}`);
    }
  }

  private async fetchOpenAIModels(baseUrl: string, apiKey?: string): Promise<string[]> {
    if (!apiKey) {
      throw new Error('API key is required for OpenAI-compatible providers');
    }

    const response = await axios.get(`${baseUrl}/models`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    return response.data.data
      .filter((model: any) => model.id && !model.id.includes('embedding'))
      .map((model: any) => model.id)
      .sort();
  }

  private async fetchOllamaModels(baseUrl: string): Promise<string[]> {
    try {
      const response = await axios.get(`${baseUrl}/api/tags`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.models || !Array.isArray(response.data.models)) {
        throw new Error('Invalid response format from Ollama');
      }

      return response.data.models
        .map((model: any) => model.name)
        .filter((name: string) => name && name.trim())
        .sort();
    } catch (error: any) {
      console.error('Ollama Models Fetch Error:', error.response?.data || error.message);

      if (error.code === 'ECONNREFUSED' || error.response?.status === 502) {
        throw new Error('Cannot connect to Ollama server. Please ensure Ollama is running.');
      } else if (error.response?.status === 404) {
        throw new Error('Ollama API endpoint not found. Please check the base URL.');
      } else {
        throw new Error(`Failed to fetch Ollama models: ${error.message}`);
      }
    }
  }

  private async fetchGeminiModels(apiKey?: string): Promise<string[]> {
    if (!apiKey) {
      throw new Error('API key is required for Gemini');
    }

    const response = await axios.get(
      `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`,
      { timeout: 10000 }
    );

    return response.data.models
      .filter((model: any) => model.name.includes('generateContent'))
      .map((model: any) => model.name.split('/').pop())
      .sort();
  }

  private async fetchAnthropicModels(): Promise<string[]> {
    // Anthropic doesn't have a public models API, return known models
    return [
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }

  private async fetchSiliconFlowModels(apiKey?: string): Promise<string[]> {
    if (!apiKey) {
      throw new Error('API key is required for SiliconFlow');
    }

    try {
      const response = await axios.get('https://api.siliconflow.cn/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      return response.data.data
        .filter((model: any) => model.id && !model.id.includes('embedding'))
        .map((model: any) => model.id)
        .sort();
    } catch (error) {
      // Fallback to known SiliconFlow models
      return [
        'deepseek-chat',
        'deepseek-coder',
        'Qwen/Qwen2.5-7B-Instruct',
        'Qwen/Qwen2.5-14B-Instruct',
        'Qwen/Qwen2.5-32B-Instruct',
        'Qwen/Qwen2.5-72B-Instruct',
        'meta-llama/Meta-Llama-3.1-8B-Instruct',
        'meta-llama/Meta-Llama-3.1-70B-Instruct',
        'meta-llama/Meta-Llama-3.1-405B-Instruct'
      ];
    }
  }

  private async fetchAlibabaModels(apiKey?: string): Promise<string[]> {
    // Alibaba Cloud DashScope models - return known models as they don't have a public models API
    return [
      'qwen-turbo',
      'qwen-plus',
      'qwen-max',
      'qwen-max-1201',
      'qwen-max-longcontext',
      'qwen2.5-72b-instruct',
      'qwen2.5-32b-instruct',
      'qwen2.5-14b-instruct',
      'qwen2.5-7b-instruct',
      'qwen2-72b-instruct',
      'qwen2-57b-a14b-instruct',
      'qwen2-7b-instruct',
      'qwen2-1.5b-instruct',
      'qwen2-0.5b-instruct'
    ];
  }
}
