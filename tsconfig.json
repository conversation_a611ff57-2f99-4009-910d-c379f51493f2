{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": false, "noEmitOnError": false, "esModuleInterop": true, "skipLibCheck": true, "types": ["node"], "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}