import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { PrismaClient } from '@prisma/client';
import { RequestData, RequestResponse, ExecuteRequestResult } from '../../utils/types';

export class RequestService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async executeRequest(requestData: RequestData): Promise<ExecuteRequestResult> {
    const startTime = Date.now();

    try {
      // Prepare axios config
      const config: AxiosRequestConfig = {
        method: requestData.method.toLowerCase() as any,
        url: requestData.url,
        headers: requestData.headers,
        timeout: 30000, // 30 seconds timeout
        validateStatus: () => true, // Don't throw on any status code
      };

      // Add body for methods that support it
      if (['post', 'put', 'patch'].includes(requestData.method.toLowerCase()) && requestData.body) {
        config.data = requestData.body;
      }

      // Execute the request
      const response: AxiosResponse = await axios(config);
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Calculate response size
      const responseSize = this.calculateResponseSize(response);

      const result: RequestResponse = {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>,
        data: response.data,
        responseTime,
        size: responseSize
      };

      // Save request history
      await this.saveRequestHistory(requestData, result);

      return {
        success: true,
        response: result
      };

    } catch (error: any) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      let errorMessage = 'Unknown error occurred';
      
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timeout';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'Host not found';
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused';
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Save failed request history
      await this.saveRequestHistory(requestData, null, errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async saveRequest(requestData: RequestData): Promise<any> {
    try {
      if (requestData.id) {
        // Update existing request
        return await this.prisma.request.update({
          where: { id: requestData.id },
          data: {
            name: requestData.name,
            method: requestData.method,
            url: requestData.url,
            headers: JSON.stringify(requestData.headers || {}),
            body: requestData.body ? JSON.stringify(requestData.body) : undefined
          }
        });
      } else {
        // Create new request
        const request = await this.prisma.request.create({
          data: {
            name: requestData.name,
            method: requestData.method,
            url: requestData.url,
            headers: JSON.stringify(requestData.headers || {}),
            body: requestData.body ? JSON.stringify(requestData.body) : undefined,
            workspaceId: requestData.workspaceId
          }
        });

        // Create corresponding item in hierarchy
        await this.prisma.item.create({
          data: {
            type: 'REQUEST',
            entityId: request.id,
            workspaceId: requestData.workspaceId,
            parentId: null, // Will be set when user drags it
            order: await this.getNextOrderNumber(requestData.workspaceId, null)
          }
        });

        return request;
      }
    } catch (error) {
      console.error('Error saving request:', error);
      throw new Error('Failed to save request');
    }
  }

  async getRequest(id: number): Promise<any> {
    try {
      return await this.prisma.request.findUnique({
        where: { id },
        include: {
          examples: true
        }
      });
    } catch (error) {
      console.error('Error fetching request:', error);
      throw new Error('Failed to fetch request');
    }
  }

  async deleteRequest(id: number): Promise<void> {
    try {
      await this.prisma.$transaction(async (tx:any) => {
        // Delete examples first
        await tx.example.deleteMany({
          where: { requestId: id }
        });

        // Delete the request
        await tx.request.delete({
          where: { id }
        });

        // Delete the corresponding item
        await tx.item.deleteMany({
          where: {
            type: 'REQUEST',
            entityId: id
          }
        });
      });
    } catch (error) {
      console.error('Error deleting request:', error);
      throw new Error('Failed to delete request');
    }
  }

  async duplicateRequest(id: number): Promise<any> {
    try {
      const originalRequest = await this.prisma.request.findUnique({
        where: { id }
      });

      if (!originalRequest) {
        throw new Error('Request not found');
      }

      const duplicatedRequest = await this.prisma.request.create({
        data: {
          name: `${originalRequest.name} (Copy)`,
          method: originalRequest.method,
          url: originalRequest.url,
          headers: originalRequest.headers as any,
          body: originalRequest.body as any,
          workspaceId: originalRequest.workspaceId
        }
      });

      // Create corresponding item in hierarchy
      await this.prisma.item.create({
        data: {
          type: 'REQUEST',
          entityId: duplicatedRequest.id,
          workspaceId: originalRequest.workspaceId,
          parentId: null,
          order: await this.getNextOrderNumber(originalRequest.workspaceId, null)
        }
      });

      return duplicatedRequest;
    } catch (error) {
      console.error('Error duplicating request:', error);
      throw new Error('Failed to duplicate request');
    }
  }

  private calculateResponseSize(response: AxiosResponse): number {
    try {
      const dataString = JSON.stringify(response.data);
      return new Blob([dataString]).size;
    } catch {
      return 0;
    }
  }

  private async saveRequestHistory(
    requestData: RequestData, 
    response: RequestResponse | null, 
    error?: string
  ): Promise<void> {
    try {
      // This would save to a request history table
      // For now, we'll just log it
      console.log('Request executed:', {
        url: requestData.url,
        method: requestData.method,
        success: !!response,
        responseTime: response?.responseTime,
        error
      });
    } catch (error) {
      console.error('Error saving request history:', error);
    }
  }

  private async getNextOrderNumber(workspaceId: number, parentId: number | null): Promise<number> {
    const maxOrder = await this.prisma.item.aggregate({
      where: {
        workspaceId,
        parentId
      },
      _max: {
        order: true
      }
    });

    return (maxOrder._max.order || 0) + 1;
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
