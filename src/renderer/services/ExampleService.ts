import { BaseItemService } from './BaseItemService';
import { FullExampleData } from '../types/example';

export class ExampleService implements BaseItemService<FullExampleData> {
  async create(data: Partial<FullExampleData>, workspaceId: number): Promise<FullExampleData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const example = await window.electronAPI.workspace.createExample({
        name: data.name || 'Untitled Example',
        description: data.description || '',
        method: data.method || 'GET',
        url: data.url || '',
        headers: data.headers || {},
        body: data.body || '',
        response: data.response,
        requestId: data.requestId ? Number(data.requestId) : undefined,
        workspaceId
      });

      return {
        id: example.hierarchyId || example.id,
        entityId: example.id,
        name: example.name,
        description: example.description,
        method: example.method,
        url: example.url,
        headers: example.headers,
        body: example.body,
        response: example.response,
        requestId: example.requestId,
        createdAt: example.createdAt,
        updatedAt: example.updatedAt
      };
    } catch (error) {
      console.error('Failed to create example:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<FullExampleData>): Promise<FullExampleData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      // Use generic update method since updateExample doesn't exist
      const updated = await window.electronAPI.workspace.update(entityId, {
        name: data.name,
        description: data.description,
        method: data.method,
        url: data.url,
        headers: data.headers,
        body: data.body,
        response: data.response
      });

      return {
        id: updated.hierarchyId || updated.id,
        entityId: updated.id,
        name: updated.name,
        description: updated.description,
        method: updated.method,
        url: updated.url,
        headers: updated.headers,
        body: updated.body,
        response: updated.response,
        requestId: updated.requestId,
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      };
    } catch (error) {
      console.error('Failed to update example:', error);
      throw error;
    }
  }

  async get(id: string | number): Promise<FullExampleData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const example = await window.electronAPI.workspace.getExampleDetails(entityId);

      if (!example) return null;

      return {
        id: example.hierarchyId || example.id,
        entityId: example.id,
        name: example.name,
        description: example.description,
        method: example.method,
        url: example.url,
        headers: example.headers,
        body: example.body,
        response: example.response,
        requestId: example.requestId,
        createdAt: example.createdAt,
        updatedAt: example.updatedAt
      };
    } catch (error) {
      console.error('Failed to get example:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.workspace.deleteItem(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete example:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // An example is new if it doesn't have an entityId or has a temporary ID pattern
    return !data.entityId || 
           (typeof data.id === 'string' && data.id.startsWith('example-'));
  }

  getDisplayName(data: any): string {
    return data.name || 'Untitled Example';
  }

  getDefaultData(): Partial<FullExampleData> {
    return {
      name: 'Untitled Example',
      description: '',
      method: 'GET',
      url: '',
      headers: {},
      body: ''
    };
  }

  /**
   * Examples don't need save dialog - they save directly to current workspace
   */
  needsSaveDialog(data: any): boolean {
    return false;
  }
}
