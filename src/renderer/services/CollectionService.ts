import { BaseItemService } from './BaseItemService';
import { FullCollectionData } from '../types/collection';

export class CollectionService implements BaseItemService<FullCollectionData> {
  async create(data: Partial<FullCollectionData>, workspaceId: number): Promise<FullCollectionData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const collection = await window.electronAPI.workspace.createCollection({
        name: data.name || 'Untitled Collection',
        description: data.description || '',
        workspaceId
      });

      return {
        id: collection.hierarchyId || collection.id,
        entityId: collection.id,
        name: collection.name,
        description: collection.description,
        variables: collection.variables || {},
        tags: data.tags || [],
        version: data.version || '1.0.0',
        createdAt: collection.createdAt,
        updatedAt: collection.updatedAt
      };
    } catch (error) {
      console.error('Failed to create collection:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<FullCollectionData>): Promise<FullCollectionData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const updated = await window.electronAPI.workspace.updateCollection(entityId, {
        name: data.name,
        description: data.description,
        baseUrl: data.baseUrl,
        variables: data.variables,
        auth: data.auth,
        preRequestScript: data.preRequestScript,
        testScript: data.testScript,
        documentation: data.documentation,
        tags: data.tags,
        version: data.version
      });

      return {
        id: updated.hierarchyId || updated.id,
        entityId: updated.id,
        name: updated.name,
        description: updated.description,
        baseUrl: updated.baseUrl || data.baseUrl,
        variables: updated.variables || {},
        auth: updated.auth || data.auth,
        preRequestScript: updated.preRequestScript || data.preRequestScript,
        testScript: updated.testScript || data.testScript,
        documentation: updated.documentation || data.documentation,
        tags: updated.tags || data.tags || [],
        version: updated.version || data.version || '1.0.0',
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      };
    } catch (error) {
      console.error('Failed to update collection:', error);
      throw error;
    }
  }

  async get(id: string | number): Promise<FullCollectionData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const collection = await window.electronAPI.workspace.getCollectionDetails(entityId);

      if (!collection) return null;

      return {
        id: collection.hierarchyId || collection.id,
        entityId: collection.id,
        name: collection.name,
        description: collection.description,
        variables: collection.variables || {},
        tags: [], // TODO: Add tags support to backend
        version: '1.0.0', // TODO: Add version support to backend
        createdAt: collection.createdAt,
        updatedAt: collection.updatedAt
      };
    } catch (error) {
      console.error('Failed to get collection:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.workspace.deleteItem(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete collection:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // A collection is new if it doesn't have an entityId or has a temporary ID pattern
    return !data.id || 
           (typeof data.id === 'string' && data.id.startsWith('collection-'));
  }

  getDisplayName(data: any): string {
    return data.name || 'Untitled Collection';
  }

  getDefaultData(): Partial<FullCollectionData> {
    return {
      name: 'Untitled Collection',
      description: '',
      variables: {},
      tags: [],
      version: '1.0.0'
    };
  }

  /**
   * Collections don't need save dialog - they save directly to current workspace
   */
  needsSaveDialog(data: any): boolean {
    return false;
  }
}
