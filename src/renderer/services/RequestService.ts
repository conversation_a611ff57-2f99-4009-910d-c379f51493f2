import { BaseItemService } from './BaseItemService';
import { FullRequestData } from '../types/request';

export class RequestService implements BaseItemService<FullRequestData> {
  async create(data: Partial<FullRequestData>, workspaceId: number): Promise<FullRequestData> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      // Create the request entity
      const request = await window.electronAPI.workspace.createRequest({
        name: data.name || 'Untitled Request',
        method: data.method || 'GET',
        url: data.url || '',
        headers: data.headers || {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        params: data.params || {},
        body: data.body || '',
        description: data.description || '',
        auth: data.auth || { type: 'none' },
        preScript: data.preScript || '',
        testScript: data.testScript || '',
        workspaceId
      });

      return {
        id: request.hierarchyId || request.id,
        entityId: request.id,
        name: request.name,
        method: request.method,
        url: request.url,
        headers: request.headers,
        params: request.params,
        body: request.body,
        description: request.description,
        auth: request.auth,
        preScript: request.preScript,
        testScript: request.testScript,
        workspaceId: workspaceId,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt
      };
    } catch (error) {
      console.error('Failed to create request:', error);
      throw error;
    }
  }

  async update(id: string | number, data: Partial<FullRequestData>): Promise<FullRequestData> {
    console.log('[RequestService] update called with id:', id);
    console.log('[RequestService] update called with data:', data);
    console.log('[RequestService] Headers:', data.headers);
    console.log('[RequestService] Params:', data.params);

    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const updatePayload = {
        name: data.name,
        method: data.method,
        url: data.url,
        headers: data.headers,
        params: data.params,
        body: data.body,
        description: data.description,
        auth: data.auth,
        preScript: data.preScript,
        testScript: data.testScript
      };

      console.log('[RequestService] Sending updatePayload to backend:', updatePayload);
      console.log('[RequestService] UpdatePayload headers:', updatePayload.headers);
      console.log('[RequestService] UpdatePayload params:', updatePayload.params);

      const updated = await window.electronAPI.workspace.updateRequest(entityId, updatePayload);

      return {
        id: updated.hierarchyId || updated.id,
        entityId: updated.id,
        name: updated.name,
        method: updated.method,
        url: updated.url,
        headers: updated.headers,
        params: updated.params,
        body: updated.body,
        description: updated.description,
        auth: updated.auth,
        preScript: updated.preScript,
        testScript: updated.testScript,
        workspaceId: updated.workspaceId,
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      };
    } catch (error) {
      console.error('Failed to update request:', error);
      throw error;
    }
  }

  async get(id: string | number): Promise<FullRequestData | null> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      const request = await window.electronAPI.workspace.getRequestDetails(entityId);

      if (!request) return null;

      return {
        id: request.hierarchyId || request.id,
        entityId: request.id,
        name: request.name,
        method: request.method,
        url: request.url,
        headers: request.headers,
        params: request.params,
        body: request.body,
        description: request.description,
        workspaceId: request.workspaceId,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt
      };
    } catch (error) {
      console.error('Failed to get request:', error);
      return null;
    }
  }

  async delete(id: string | number): Promise<boolean> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }

    try {
      const entityId = typeof id === 'string' ? parseInt(id) : id;
      await window.electronAPI.workspace.deleteItem(entityId);
      return true;
    } catch (error) {
      console.error('Failed to delete request:', error);
      return false;
    }
  }

  isNew(data: any): boolean {
    // A request is new if it doesn't have an entityId or has a temporary ID pattern
    return !data.id || 
           (typeof data.id === 'string' && (
             data.id.startsWith('curl-import-') ||
             data.id.startsWith('request-') && data.id.length > 15
           ));
  }

  getDisplayName(data: any): string {
    return data.name || 'Untitled Request';
  }

  getDefaultData(): Partial<FullRequestData> {
    return {
      name: 'Untitled Request',
      method: 'GET',
      url: '',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      params: {},
      body: '',
      description: ''
    };
  }

  /**
   * Check if a request needs a save dialog (new requests need location selection)
   */
  needsSaveDialog(data: any): boolean {
    return this.isNew(data);
  }
}
