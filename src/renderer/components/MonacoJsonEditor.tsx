import React, { useRef, useEffect, useState } from 'react';

interface MonacoJsonEditorProps {
  value: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  height?: string | number;
  theme?: 'vs-dark' | 'light';
  language?: string;
  placeholder?: string;
  onError?: () => void;
}

declare global {
  interface Window {
    monaco: any;
    monacoLoaded: boolean;
  }
}

const MonacoJsonEditor: React.FC<MonacoJsonEditorProps> = ({
  value,
  onChange,
  readOnly = false,
  height = '300px',
  theme = 'vs-dark',
  language = 'json',
  placeholder = '',
  onError
}) => {
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [monacoReady, setMonacoReady] = useState(false);

  useEffect(() => {
    // Check if Monaco is already loaded
    if (window.monacoLoaded && window.monaco) {
      setMonacoReady(true);
      setIsLoading(false);
      return;
    }

    // Listen for Monaco loaded event
    const handleMonacoLoaded = () => {
      setMonacoReady(true);
      setIsLoading(false);
    };

    window.addEventListener('monacoLoaded', handleMonacoLoaded);

    // Set a timeout to fallback if Monaco takes too long
    const timeout = setTimeout(() => {
      if (!window.monacoLoaded) {
        console.warn('Monaco Editor loading timeout, falling back to simple editor');
        setHasError(true);
        setIsLoading(false);
        if (onError) {
          onError();
        }
      }
    }, 10000);

    return () => {
      window.removeEventListener('monacoLoaded', handleMonacoLoaded);
      clearTimeout(timeout);
    };
  }, [onError]);

  useEffect(() => {
    if (!monacoReady || !containerRef.current || !window.monaco) return;

    try {
      // Create Monaco Editor instance
      const editor = window.monaco.editor.create(containerRef.current, {
        value: value,
        language: language,
        theme: theme === 'vs-dark' ? 'vs-dark' : 'vs',
        readOnly: readOnly,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        fontSize: 13,
        lineNumbers: 'on',
        wordWrap: 'on',
        automaticLayout: true,
        tabSize: 2,
        insertSpaces: true,
        detectIndentation: false,
        folding: true,
        foldingStrategy: 'indentation',
        showFoldingControls: 'always',
        scrollbar: {
          vertical: 'auto',
          horizontal: 'auto',
          verticalScrollbarSize: 8,
          horizontalScrollbarSize: 8
        }
      });

      editorRef.current = editor;

      // Set up JSON validation for JSON language
      if (language === 'json') {
        window.monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
          validate: true,
          allowComments: false,
          schemas: [],
          enableSchemaRequest: false
        });
      }

      // Handle value changes
      editor.onDidChangeModelContent(() => {
        const newValue = editor.getValue();
        if (onChange && newValue !== value) {
          onChange(newValue);
        }
      });

      // Format JSON on mount if it's valid JSON
      if (language === 'json' && value && !readOnly) {
        try {
          const parsed = JSON.parse(value);
          const formatted = JSON.stringify(parsed, null, 2);
          if (formatted !== value) {
            editor.setValue(formatted);
          }
        } catch {
          // Invalid JSON, keep as is
        }
      }

      // Define custom theme
      window.monaco.editor.defineTheme('custom-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'string.key.json', foreground: '9CDCFE' },
          { token: 'string.value.json', foreground: 'CE9178' },
          { token: 'number.json', foreground: 'B5CEA8' },
          { token: 'keyword.json', foreground: '569CD6' }
        ],
        colors: {
          'editor.background': '#1e1e1e',
          'editor.foreground': '#d4d4d4',
          'editorLineNumber.foreground': '#858585',
          'editor.selectionBackground': '#264f78',
          'editor.lineHighlightBackground': '#2a2d2e'
        }
      });

      // Set Monaco to use the custom theme if dark theme is requested
      if (theme === 'vs-dark') {
        window.monaco.editor.setTheme('custom-dark');
      }

    } catch (error) {
      console.error('Monaco Editor initialization error:', error);
      setHasError(true);
      if (onError) {
        onError();
      }
    }

    // Cleanup function
    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
      }
    };
  }, [monacoReady, value, language, theme, readOnly, onChange, onError]);

  // Update editor value when prop changes
  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.getValue()) {
      editorRef.current.setValue(value);
    }
  }, [value]);

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  if (isLoading) {
    return (
      <div className="monaco-json-editor">
        <div style={{ 
          padding: '20px', 
          textAlign: 'center',
          color: 'var(--color-text-secondary)'
        }}>
          Loading Monaco Editor...
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="monaco-json-editor">
        <textarea
          value={value}
          onChange={handleTextareaChange}
          placeholder={placeholder}
          readOnly={readOnly}
          style={{
            width: '100%',
            height: typeof height === 'string' ? height : `${height}px`,
            border: '1px solid var(--color-border)',
            borderRadius: '4px',
            padding: '8px',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '13px',
            lineHeight: '1.4',
            resize: 'vertical',
            background: 'var(--color-background)',
            color: 'var(--color-text)'
          }}
        />
      </div>
    );
  }

  return (
    <div className="monaco-json-editor">
      <div 
        ref={containerRef}
        style={{
          height: typeof height === 'string' ? height : `${height}px`,
          border: '1px solid var(--color-border)',
          borderRadius: '4px'
        }}
      />
    </div>
  );
};

export default MonacoJsonEditor;
