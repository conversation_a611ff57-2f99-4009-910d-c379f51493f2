import * as winston from 'winston';
import * as path from 'path';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  metadata?: any;
}

export class LoggingService {
  private logger: winston.Logger;

  constructor() {
    // Create logs directory if it doesn't exist
    const logsDir = path.join(process.cwd(), 'logs');

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'apicool' },
      transports: [
        // Write all logs with level 'error' and below to error.log
        new winston.transports.File({ 
          filename: path.join(logsDir, 'error.log'), 
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5
        }),
        
        // Write all logs with level 'info' and below to combined.log
        new winston.transports.File({ 
          filename: path.join(logsDir, 'combined.log'),
          maxsize: 5242880, // 5MB
          maxFiles: 5
        }),

        // Console transport for development
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        })
      ]
    });

    // If we're not in production, log to console with more detail
    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
          })
        )
      }));
    }
  }

  error(message: string, ...meta: any[]): void {
    this.logger.error(message, ...meta);
  }

  warn(message: string, ...meta: any[]): void {
    this.logger.warn(message, ...meta);
  }

  info(message: string, ...meta: any[]): void {
    this.logger.info(message, ...meta);
  }

  debug(message: string, ...meta: any[]): void {
    this.logger.debug(message, ...meta);
  }

  setLevel(level: LogLevel): void {
    this.logger.level = level;
  }

  async getLogs(level?: LogLevel, limit: number = 100): Promise<LogEntry[]> {
    return new Promise((resolve, reject) => {
      const options: any = {
        limit: limit,
        start: 0,
        order: 'desc'
      };

      if (level) {
        options.level = level;
      }

      this.logger.query(options, (err, results) => {
        if (err) {
          reject(err);
        } else {
          const logs: LogEntry[] = results.file?.map((log: any) => ({
            level: log.level,
            message: log.message,
            timestamp: new Date(log.timestamp),
            metadata: log.meta
          })) || [];
          
          resolve(logs);
        }
      });
    });
  }

  async clearLogs(): Promise<void> {
    // This would clear log files - implement with caution
    this.info('Log clear requested');
  }

  // Convenience methods for common logging scenarios
  logRequest(method: string, url: string, statusCode?: number, responseTime?: number): void {
    this.info('HTTP Request', {
      method,
      url,
      statusCode,
      responseTime
    });
  }

  logError(error: Error, context?: string): void {
    this.error(`${context ? `[${context}] ` : ''}${error.message}`, {
      stack: error.stack,
      context
    });
  }

  logUserAction(userId: number, action: string, details?: any): void {
    this.info('User Action', {
      userId,
      action,
      details
    });
  }

  logSystemEvent(event: string, details?: any): void {
    this.info('System Event', {
      event,
      details
    });
  }

  logPerformance(operation: string, duration: number, details?: any): void {
    this.info('Performance', {
      operation,
      duration,
      details
    });
  }
}
