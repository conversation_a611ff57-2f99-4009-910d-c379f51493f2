import React, { useState, useEffect } from 'react';

interface HistoryEntry {
  id: number;
  requestId?: number;
  name: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  response?: any;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  environment?: string;
  workspaceId: number;
  executedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface HistoryStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
}

interface HistoryListProps {
  workspaceId: number;
  onOpenHistoryEntry: (historyId: string, historyName: string) => void;
  onOpenRequest: (historyEntry: HistoryEntry) => void;
}

const HistoryList: React.FC<HistoryListProps> = ({ 
  workspaceId, 
  onOpenHistoryEntry,
  onOpenRequest 
}) => {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [stats, setStats] = useState<HistoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([]);

  useEffect(() => {
    loadHistory();
    loadStats();
  }, [workspaceId]);

  useEffect(() => {
    // Filter history based on search query
    if (searchQuery.trim()) {
      const filtered = history.filter(entry =>
        entry.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.method.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredHistory(filtered);
    } else {
      setFilteredHistory(history);
    }
  }, [history, searchQuery]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await window.electronAPI.history.getAll(workspaceId, 50, 0);
      setHistory(historyData);
    } catch (err) {
      setError('Failed to load history');
      console.error('Error loading history:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await window.electronAPI.history.getStats(workspaceId);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading history stats:', err);
    }
  };

  const handleDeleteEntry = async (id: number) => {
    if (!confirm('Are you sure you want to delete this history entry?')) return;

    try {
      await window.electronAPI.history.delete(id);
      setHistory(prev => prev.filter(entry => entry.id !== id));
      loadStats(); // Refresh stats
    } catch (err) {
      setError('Failed to delete history entry');
    }
  };

  const handleClearHistory = async () => {
    if (!confirm('Are you sure you want to clear all history? This action cannot be undone.')) return;

    try {
      await window.electronAPI.history.clear(workspaceId);
      setHistory([]);
      loadStats();
    } catch (err) {
      setError('Failed to clear history');
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return 'N/A';
    return `${time}ms`;
  };

  const getStatusColor = (statusCode?: number) => {
    if (!statusCode) return 'gray';
    if (statusCode >= 200 && statusCode < 300) return 'green';
    if (statusCode >= 300 && statusCode < 400) return 'orange';
    if (statusCode >= 400) return 'red';
    return 'gray';
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET': return 'blue';
      case 'POST': return 'green';
      case 'PUT': return 'orange';
      case 'DELETE': return 'red';
      case 'PATCH': return 'purple';
      default: return 'gray';
    }
  };

  if (loading) {
    return <div className="history-list loading">Loading history...</div>;
  }

  return (
    <div className="history-list">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="history-header">
        <h3>Request History</h3>
        <div className="history-actions">
          <button 
            className="btn-secondary btn-small"
            onClick={loadHistory}
            title="Refresh"
          >
            ↻
          </button>
          <button 
            className="btn-danger btn-small"
            onClick={handleClearHistory}
            title="Clear All"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Stats */}
      {stats && (
        <div className="history-stats">
          <div className="stat-item">
            <span className="stat-label">Total:</span>
            <span className="stat-value">{stats.totalRequests}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Success:</span>
            <span className="stat-value success">{stats.successfulRequests}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Failed:</span>
            <span className="stat-value error">{stats.failedRequests}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Avg Time:</span>
            <span className="stat-value">{stats.averageResponseTime}ms</span>
          </div>
        </div>
      )}

      {/* Search */}
      <div className="history-search">
        <input
          type="text"
          placeholder="Search history..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input"
        />
      </div>

      {/* History Items */}
      <div className="history-content">
        {filteredHistory.length === 0 ? (
          <div className="history-empty">
            {searchQuery ? 'No matching history entries' : 'No history entries yet'}
          </div>
        ) : (
          <div className="history-items">
            {filteredHistory.map(entry => (
              <div
                key={entry.id}
                className="history-item"
                onClick={() => onOpenRequest(entry)}
              >
                <div className="history-item-header">
                  <span className={`method-badge ${getMethodColor(entry.method)}`}>
                    {entry.method}
                  </span>
                  <span className="history-name">{entry.name}</span>
                  {entry.statusCode && (
                    <span className={`status-badge ${getStatusColor(entry.statusCode)}`}>
                      {entry.statusCode}
                    </span>
                  )}
                </div>
                <div className="history-item-url">{entry.url}</div>
                <div className="history-item-meta">
                  <span className="history-time">{formatDate(entry.executedAt)}</span>
                  {entry.responseTime && (
                    <span className="response-time">{formatResponseTime(entry.responseTime)}</span>
                  )}
                  {entry.environment && (
                    <span className="environment-badge">{entry.environment}</span>
                  )}
                </div>
                <div className="history-item-actions" onClick={(e) => e.stopPropagation()}>
                  <button
                    className="btn-icon btn-primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onOpenHistoryEntry(entry.id.toString(), entry.name);
                    }}
                    title="View Details"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                      <circle cx="12" cy="12" r="3"/>
                    </svg>
                  </button>
                  <button
                    className="btn-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      onOpenRequest(entry);
                    }}
                    title="Open as Request"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15,3 21,3 21,9"/>
                      <line x1="10" y1="14" x2="21" y2="3"/>
                    </svg>
                  </button>
                  <button
                    className="btn-icon btn-danger"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteEntry(entry.id);
                    }}
                    title="Delete"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="3,6 5,6 21,6"/>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryList;
